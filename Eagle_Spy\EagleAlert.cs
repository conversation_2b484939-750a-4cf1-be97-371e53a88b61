using System;
using System.Drawing;
using Eagle_Spy.My.Resources;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[StandardModule]
internal sealed class EagleAlert
{
	public static void ShowCustome(string msg, Bitmap img, Color backcolor, Color rondcolor)
	{
		try
		{
			alertform alertform2 = new alertform(msg);
			alertform2.alertimage.Image = img;
			alertform2.Show();
		}
		catch (Exception)
		{
		}
	}

	public static void ShowSucess(string msg)
	{
		alertform alertform2 = new alertform(msg);
		alertform2.alertimage.Image = Resources.sucess48px;
		alertform2.Show();
	}

	public static void ShowWarning(string msg)
	{
		alertform alertform2 = new alertform(msg);
		alertform2.alertimage.Image = Resources.warning48px;
		alertform2.Show();
	}

	public static void Showinformation(string msg)
	{
		alertform alertform2 = new alertform(msg);
		alertform2.alertimage.Image = Resources.information48px;
		alertform2.Show();
	}

	public static void ShowError(string msg)
	{
		alertform alertform2 = new alertform(msg);
		alertform2.alertimage.Image = Resources.error48px;
		alertform2.Show();
	}
}
