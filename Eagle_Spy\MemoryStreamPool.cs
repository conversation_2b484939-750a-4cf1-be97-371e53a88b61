using System;
using System.Collections.Concurrent;
using System.IO;

namespace Eagle_Spy
{
    /// <summary>
    /// MemoryStream对象池，减少GC压力和内存分配开销
    /// 专为小强远控项目优化，解决FormatPacket等方法的内存分配热点
    /// </summary>
    public static class MemoryStreamPool
    {
        // 使用线程安全的并发队列作为对象池
        private static readonly ConcurrentQueue<MemoryStream> _streamPool = new ConcurrentQueue<MemoryStream>();
        
        // 池中对象的最大容量限制（1MB），避免内存泄漏
        private const int MaxPooledStreamCapacity = 1024 * 1024;
        
        // 池的最大大小，避免无限增长
        private const int MaxPoolSize = 50;
        
        // 当前池中对象数量（近似值，用于快速判断）
        private static int _poolCount = 0;

        /// <summary>
        /// 从对象池获取MemoryStream实例
        /// 如果池中有可用对象则复用，否则创建新对象
        /// </summary>
        /// <returns>可用的MemoryStream实例</returns>
        public static MemoryStream GetStream()
        {
            if (_streamPool.TryDequeue(out MemoryStream stream))
            {
                // 重置流的位置和长度，准备复用
                stream.SetLength(0);
                stream.Position = 0;
                
                // 原子递减计数
                System.Threading.Interlocked.Decrement(ref _poolCount);
                
                return stream;
            }
            
            // 池中没有可用对象，创建新的
            return new MemoryStream();
        }

        /// <summary>
        /// 将MemoryStream实例归还到对象池
        /// 只有容量合适的对象才会被放入池中，过大的对象直接释放
        /// </summary>
        /// <param name="stream">要归还的MemoryStream实例</param>
        public static void ReturnStream(MemoryStream stream)
        {
            if (stream == null)
                return;

            try
            {
                // 检查对象是否适合放入池中
                if (stream.Capacity <= MaxPooledStreamCapacity && _poolCount < MaxPoolSize)
                {
                    // 清理流的内容，准备下次复用
                    stream.SetLength(0);
                    stream.Position = 0;
                    
                    // 放入池中
                    _streamPool.Enqueue(stream);
                    
                    // 原子递增计数
                    System.Threading.Interlocked.Increment(ref _poolCount);
                }
                else
                {
                    // 对象太大或池已满，直接释放
                    stream.Dispose();
                }
            }
            catch (ObjectDisposedException)
            {
                // 流已经被释放，忽略
            }
            catch (Exception)
            {
                // 其他异常，安全释放
                try
                {
                    stream.Dispose();
                }
                catch
                {
                    // 忽略释放时的异常
                }
            }
        }

        /// <summary>
        /// 获取池的统计信息（用于调试和监控）
        /// </summary>
        /// <returns>池中当前对象数量</returns>
        public static int GetPoolCount()
        {
            return _poolCount;
        }

        /// <summary>
        /// 清空对象池，释放所有缓存的对象
        /// 通常在应用程序关闭时调用
        /// </summary>
        public static void ClearPool()
        {
            while (_streamPool.TryDequeue(out MemoryStream stream))
            {
                try
                {
                    stream.Dispose();
                }
                catch
                {
                    // 忽略释放时的异常
                }
            }
            _poolCount = 0;
        }

        /// <summary>
        /// 使用对象池的便捷方法，自动管理MemoryStream的生命周期
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="action">使用MemoryStream的操作</param>
        /// <returns>操作的返回值</returns>
        public static T UseStream<T>(Func<MemoryStream, T> action)
        {
            MemoryStream stream = GetStream();
            try
            {
                return action(stream);
            }
            finally
            {
                ReturnStream(stream);
            }
        }

        /// <summary>
        /// 使用对象池的便捷方法，自动管理MemoryStream的生命周期（无返回值版本）
        /// </summary>
        /// <param name="action">使用MemoryStream的操作</param>
        public static void UseStream(Action<MemoryStream> action)
        {
            MemoryStream stream = GetStream();
            try
            {
                action(stream);
            }
            finally
            {
                ReturnStream(stream);
            }
        }
    }
}
