﻿using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

// Token: 0x02000004 RID: 4
[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[DebuggerNonUserCode]
[CompilerGenerated]
internal class Craxs_Rat_Applications
{
    // Token: 0x06000004 RID: 4 RVA: 0x00002085 File Offset: 0x00000285
    internal Craxs_Rat_Applications()
    {
    }

    // Token: 0x17000001 RID: 1
    // (get) Token: 0x06000005 RID: 5 RVA: 0x00004400 File Offset: 0x00002600
    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager
    {
        get
        {
            if (Craxs_Rat_Applications.resourceMan == null)
            {
                ResourceManager resourceManager = new ResourceManager("Eagle_Spy_Applications", typeof(Eagle_Spy_Applications).Assembly);
                Craxs_Rat_Applications.resourceMan = resourceManager;
            }
            return Craxs_Rat_Applications.resourceMan;
        }
    }

    // Token: 0x17000002 RID: 2
    // (get) Token: 0x06000006 RID: 6 RVA: 0x00004440 File Offset: 0x00002640
    // (set) Token: 0x06000007 RID: 7 RVA: 0x0000208D File Offset: 0x0000028D
    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture
    {
        get
        {
            return Craxs_Rat_Applications.resourceCulture;
        }
        set
        {
            Craxs_Rat_Applications.resourceCulture = value;
        }
    }

    // Token: 0x17000003 RID: 3
    // (get) Token: 0x06000008 RID: 8 RVA: 0x00004454 File Offset: 0x00002654
    internal static Bitmap Android_logo_2019__stacked_
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("Android_logo_2019_(stacked)", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000004 RID: 4
    // (get) Token: 0x06000009 RID: 9 RVA: 0x00004480 File Offset: 0x00002680
    internal static Bitmap Android_logo_2019__stacked_1
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("Android_logo_2019_(stacked)1", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000005 RID: 5
    // (get) Token: 0x0600000A RID: 10 RVA: 0x000044AC File Offset: 0x000026AC
    internal static Bitmap download_removebg_preview
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("download-removebg-preview", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000006 RID: 6
    // (get) Token: 0x0600000B RID: 11 RVA: 0x000044D8 File Offset: 0x000026D8
    internal static Bitmap download_removebg_preview1
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("download-removebg-preview1", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000007 RID: 7
    // (get) Token: 0x0600000C RID: 12 RVA: 0x00004504 File Offset: 0x00002704
    internal static Bitmap eagle
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("eagle", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000008 RID: 8
    // (get) Token: 0x0600000D RID: 13 RVA: 0x00004530 File Offset: 0x00002730
    internal static Bitmap icons8_access_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-access-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000009 RID: 9
    // (get) Token: 0x0600000E RID: 14 RVA: 0x0000455C File Offset: 0x0000275C
    internal static Bitmap icons8_android_studio_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-android-studio-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700000A RID: 10
    // (get) Token: 0x0600000F RID: 15 RVA: 0x00004588 File Offset: 0x00002788
    internal static Bitmap icons8_back_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-back-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700000B RID: 11
    // (get) Token: 0x06000010 RID: 16 RVA: 0x000045B4 File Offset: 0x000027B4
    internal static Bitmap icons8_bank_1001
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-bank-1001", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700000C RID: 12
    // (get) Token: 0x06000011 RID: 17 RVA: 0x000045E0 File Offset: 0x000027E0
    internal static Bitmap icons8_block_user_96
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-block-user-96", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700000D RID: 13
    // (get) Token: 0x06000012 RID: 18 RVA: 0x0000460C File Offset: 0x0000280C
    internal static Bitmap icons8_browser_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-browser-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700000E RID: 14
    // (get) Token: 0x06000013 RID: 19 RVA: 0x00004638 File Offset: 0x00002838
    internal static Bitmap icons8_cell_phone_with_up_and_down_arrows_96
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-cell-phone-with-up-and-down-arrows-96", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700000F RID: 15
    // (get) Token: 0x06000014 RID: 20 RVA: 0x00004664 File Offset: 0x00002864
    internal static Bitmap icons8_clean_50
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-clean-50", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000010 RID: 16
    // (get) Token: 0x06000015 RID: 21 RVA: 0x00004690 File Offset: 0x00002890
    internal static Bitmap icons8_client_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-client-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000011 RID: 17
    // (get) Token: 0x06000016 RID: 22 RVA: 0x000046BC File Offset: 0x000028BC
    internal static Bitmap icons8_cloud_64__1_1
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-cloud-64 (1)1", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000012 RID: 18
    // (get) Token: 0x06000017 RID: 23 RVA: 0x000046E8 File Offset: 0x000028E8
    internal static Bitmap icons8_cloud_641
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-cloud-641", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000013 RID: 19
    // (get) Token: 0x06000018 RID: 24 RVA: 0x00004714 File Offset: 0x00002914
    internal static Bitmap icons8_contact_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-contact-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000014 RID: 20
    // (get) Token: 0x06000019 RID: 25 RVA: 0x00004740 File Offset: 0x00002940
    internal static Bitmap icons8_cut_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-cut-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000015 RID: 21
    // (get) Token: 0x0600001A RID: 26 RVA: 0x0000476C File Offset: 0x0000296C
    internal static Bitmap icons8_delete_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-delete-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000016 RID: 22
    // (get) Token: 0x0600001B RID: 27 RVA: 0x00004798 File Offset: 0x00002998
    internal static Bitmap icons8_electric_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-electric-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000017 RID: 23
    // (get) Token: 0x0600001C RID: 28 RVA: 0x000047C4 File Offset: 0x000029C4
    internal static Bitmap icons8_email_48
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-email-48", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000018 RID: 24
    // (get) Token: 0x0600001D RID: 29 RVA: 0x000047F0 File Offset: 0x000029F0
    internal static Bitmap icons8_folder_32
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-folder-32", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000019 RID: 25
    // (get) Token: 0x0600001E RID: 30 RVA: 0x0000481C File Offset: 0x00002A1C
    internal static Bitmap icons8_folder_50
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-folder-50", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700001A RID: 26
    // (get) Token: 0x0600001F RID: 31 RVA: 0x00004848 File Offset: 0x00002A48
    internal static Bitmap icons8_folder_501
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-folder-501", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700001B RID: 27
    // (get) Token: 0x06000020 RID: 32 RVA: 0x00004874 File Offset: 0x00002A74
    internal static Bitmap icons8_google_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-google-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700001C RID: 28
    // (get) Token: 0x06000021 RID: 33 RVA: 0x000048A0 File Offset: 0x00002AA0
    internal static Bitmap icons8_google_play_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-google-play-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700001D RID: 29
    // (get) Token: 0x06000022 RID: 34 RVA: 0x000048CC File Offset: 0x00002ACC
    internal static Bitmap icons8_guarantee_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-guarantee-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700001E RID: 30
    // (get) Token: 0x06000023 RID: 35 RVA: 0x000048F8 File Offset: 0x00002AF8
    internal static Bitmap icons8_hand_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-hand-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700001F RID: 31
    // (get) Token: 0x06000024 RID: 36 RVA: 0x00004924 File Offset: 0x00002B24
    internal static Bitmap icons8_internet_antenna_30
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-internet-antenna-30", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000020 RID: 32
    // (get) Token: 0x06000025 RID: 37 RVA: 0x00004950 File Offset: 0x00002B50
    internal static Bitmap icons8_keyboard_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-keyboard-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000021 RID: 33
    // (get) Token: 0x06000026 RID: 38 RVA: 0x0000497C File Offset: 0x00002B7C
    internal static Bitmap icons8_kindle_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-kindle-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000022 RID: 34
    // (get) Token: 0x06000027 RID: 39 RVA: 0x000049A8 File Offset: 0x00002BA8
    internal static Bitmap icons8_location_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-location-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000023 RID: 35
    // (get) Token: 0x06000028 RID: 40 RVA: 0x000049D4 File Offset: 0x00002BD4
    internal static Bitmap icons8_lock_643
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-lock-643", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000024 RID: 36
    // (get) Token: 0x06000029 RID: 41 RVA: 0x00004A00 File Offset: 0x00002C00
    internal static Bitmap icons8_lock_96
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-lock-96", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000025 RID: 37
    // (get) Token: 0x0600002A RID: 42 RVA: 0x00004A2C File Offset: 0x00002C2C
    internal static Bitmap icons8_lock_961
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-lock-961", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000026 RID: 38
    // (get) Token: 0x0600002B RID: 43 RVA: 0x00004A58 File Offset: 0x00002C58
    internal static Bitmap icons8_locked_network_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-locked-network-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000027 RID: 39
    // (get) Token: 0x0600002C RID: 44 RVA: 0x00004A84 File Offset: 0x00002C84
    internal static Bitmap icons8_message_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-message-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000028 RID: 40
    // (get) Token: 0x0600002D RID: 45 RVA: 0x00004AB0 File Offset: 0x00002CB0
    internal static Bitmap icons8_microsoft_admin_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-microsoft-admin-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000029 RID: 41
    // (get) Token: 0x0600002E RID: 46 RVA: 0x00004ADC File Offset: 0x00002CDC
    internal static Bitmap icons8_minus_50
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-minus-50", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700002A RID: 42
    // (get) Token: 0x0600002F RID: 47 RVA: 0x00004B08 File Offset: 0x00002D08
    internal static Bitmap icons8_mobile_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-mobile-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700002B RID: 43
    // (get) Token: 0x06000030 RID: 48 RVA: 0x00004B34 File Offset: 0x00002D34
    internal static Bitmap icons8_no_audio_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-no-audio-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700002C RID: 44
    // (get) Token: 0x06000031 RID: 49 RVA: 0x00004B60 File Offset: 0x00002D60
    internal static Bitmap icons8_notification_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-notification-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700002D RID: 45
    // (get) Token: 0x06000032 RID: 50 RVA: 0x00004B8C File Offset: 0x00002D8C
    internal static Bitmap icons8_number_pad_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-number-pad-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700002E RID: 46
    // (get) Token: 0x06000033 RID: 51 RVA: 0x00004BB8 File Offset: 0x00002DB8
    internal static Bitmap icons8_ocr_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-ocr-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700002F RID: 47
    // (get) Token: 0x06000034 RID: 52 RVA: 0x00004BE4 File Offset: 0x00002DE4
    internal static Bitmap icons8_online_support_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-online-support-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000030 RID: 48
    // (get) Token: 0x06000035 RID: 53 RVA: 0x00004C10 File Offset: 0x00002E10
    internal static Bitmap icons8_padlock_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-padlock-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000031 RID: 49
    // (get) Token: 0x06000036 RID: 54 RVA: 0x00004C3C File Offset: 0x00002E3C
    internal static Bitmap icons8_padlock_641
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-padlock-641", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000032 RID: 50
    // (get) Token: 0x06000037 RID: 55 RVA: 0x00004C68 File Offset: 0x00002E68
    internal static Bitmap icons8_phone_1001
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-phone-1001", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000033 RID: 51
    // (get) Token: 0x06000038 RID: 56 RVA: 0x00004C94 File Offset: 0x00002E94
    internal static Bitmap icons8_play_record_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-play-record-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000034 RID: 52
    // (get) Token: 0x06000039 RID: 57 RVA: 0x00004CC0 File Offset: 0x00002EC0
    internal static Bitmap icons8_play_record_641
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-play-record-641", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000035 RID: 53
    // (get) Token: 0x0600003A RID: 58 RVA: 0x00004CEC File Offset: 0x00002EEC
    internal static Bitmap icons8_power_off_button_641
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-power-off-button-641", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000036 RID: 54
    // (get) Token: 0x0600003B RID: 59 RVA: 0x00004D18 File Offset: 0x00002F18
    internal static Bitmap icons8_public_speaking_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-public-speaking-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000037 RID: 55
    // (get) Token: 0x0600003C RID: 60 RVA: 0x00004D44 File Offset: 0x00002F44
    internal static Bitmap icons8_restart_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-restart-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000038 RID: 56
    // (get) Token: 0x0600003D RID: 61 RVA: 0x00004D70 File Offset: 0x00002F70
    internal static Bitmap icons8_restart_641
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-restart-641", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000039 RID: 57
    // (get) Token: 0x0600003E RID: 62 RVA: 0x00004D9C File Offset: 0x00002F9C
    internal static Bitmap icons8_send_30
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-send-30", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700003A RID: 58
    // (get) Token: 0x0600003F RID: 63 RVA: 0x00004DC8 File Offset: 0x00002FC8
    internal static Bitmap icons8_shortcut_1001
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-shortcut-1001", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700003B RID: 59
    // (get) Token: 0x06000040 RID: 64 RVA: 0x00004DF4 File Offset: 0x00002FF4
    internal static Bitmap icons8_sms_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-sms-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700003C RID: 60
    // (get) Token: 0x06000041 RID: 65 RVA: 0x00004E20 File Offset: 0x00003020
    internal static Bitmap icons8_speaker_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-speaker-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700003D RID: 61
    // (get) Token: 0x06000042 RID: 66 RVA: 0x00004E4C File Offset: 0x0000304C
    internal static Bitmap icons8_stop_100
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-stop-100", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700003E RID: 62
    // (get) Token: 0x06000043 RID: 67 RVA: 0x00004E78 File Offset: 0x00003078
    internal static Bitmap icons8_stop_503
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-stop-503", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x1700003F RID: 63
    // (get) Token: 0x06000044 RID: 68 RVA: 0x00004EA4 File Offset: 0x000030A4
    internal static Bitmap icons8_target_audience_68
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-target-audience-68", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000040 RID: 64
    // (get) Token: 0x06000045 RID: 69 RVA: 0x00004ED0 File Offset: 0x000030D0
    internal static Bitmap icons8_terminal_641
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-terminal-641", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000041 RID: 65
    // (get) Token: 0x06000046 RID: 70 RVA: 0x00004EFC File Offset: 0x000030FC
    internal static Bitmap icons8_video_64
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-video-64", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000042 RID: 66
    // (get) Token: 0x06000047 RID: 71 RVA: 0x00004F28 File Offset: 0x00003128
    internal static Bitmap icons8_x_30
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-x-30", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000043 RID: 67
    // (get) Token: 0x06000048 RID: 72 RVA: 0x00004F54 File Offset: 0x00003154
    internal static Bitmap icons8_x_642
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-x-642", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000044 RID: 68
    // (get) Token: 0x06000049 RID: 73 RVA: 0x00004F80 File Offset: 0x00003180
    internal static Bitmap icons8_xbox_menu_30
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("icons8-xbox-menu-30", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000045 RID: 69
    // (get) Token: 0x0600004A RID: 74 RVA: 0x00004FAC File Offset: 0x000031AC
    internal static Bitmap OpenToolStripMenuItem_Image
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("OpenToolStripMenuItem.Image", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x17000046 RID: 70
    // (get) Token: 0x0600004B RID: 75 RVA: 0x00004FD8 File Offset: 0x000031D8
    internal static Bitmap photo_2023_11_21_13_40_14
    {
        get
        {
            object @object = Craxs_Rat_Applications.ResourceManager.GetObject("photo_2023-11-21_13-40-14", Craxs_Rat_Applications.resourceCulture);
            return (Bitmap)@object;
        }
    }

    // Token: 0x04000002 RID: 2
    private static ResourceManager resourceMan;

    // Token: 0x04000003 RID: 3
    private static CultureInfo resourceCulture;
}
