using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DrakeUI.Framework;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class Dialog1 : Form
{
	private IContainer components;

	public string Title;

	public string TheText;

	[AccessedThroughProperty("Mytext")]
	internal TextBox Mytext;

	[AccessedThroughProperty("Mytitle")]
	internal Label Mytitle;

	private Guna2AnimateWindow guna2AnimateWindow1;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	[AccessedThroughProperty("DrakeUITitlePanel1")]
	internal DrakeUITitlePanel DrakeUITitlePanel1;

	public Dialog1()
	{
		base.Load += Dialog1_Load;
		InitializeComponent();
	}

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.Mytext = new System.Windows.Forms.TextBox();
		this.Mytitle = new System.Windows.Forms.Label();
		this.DrakeUITitlePanel1 = new DrakeUI.Framework.DrakeUITitlePanel();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2AnimateWindow1 = new Guna.UI2.WinForms.Guna2AnimateWindow(this.components);
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.DrakeUITitlePanel1.SuspendLayout();
		base.SuspendLayout();
		this.Mytext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Mytext.Font = new System.Drawing.Font("Bahnschrift SemiBold", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Mytext.ForeColor = System.Drawing.Color.White;
		this.Mytext.Location = new System.Drawing.Point(23, 36);
		this.Mytext.Multiline = true;
		this.Mytext.Name = "Mytext";
		this.Mytext.Size = new System.Drawing.Size(263, 27);
		this.Mytext.TabIndex = 1;
		this.Mytitle.BackColor = System.Drawing.Color.Transparent;
		this.Mytitle.Dock = System.Windows.Forms.DockStyle.Top;
		this.Mytitle.Font = new System.Drawing.Font("Calibri", 20f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Mytitle.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Mytitle.Location = new System.Drawing.Point(0, 0);
		this.Mytitle.Name = "Mytitle";
		this.Mytitle.Size = new System.Drawing.Size(324, 33);
		this.Mytitle.TabIndex = 3;
		this.Mytitle.Text = "Text";
		this.Mytitle.TextAlign = System.Drawing.ContentAlignment.TopCenter;
		this.DrakeUITitlePanel1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DrakeUITitlePanel1.Controls.Add(this.drakeUIButtonIcon2);
		this.DrakeUITitlePanel1.Controls.Add(this.drakeUIButtonIcon1);
		this.DrakeUITitlePanel1.Controls.Add(this.Mytext);
		this.DrakeUITitlePanel1.Controls.Add(this.Mytitle);
		this.DrakeUITitlePanel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.DrakeUITitlePanel1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DrakeUITitlePanel1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.DrakeUITitlePanel1.ForeColor = System.Drawing.Color.White;
		this.DrakeUITitlePanel1.Location = new System.Drawing.Point(0, 0);
		this.DrakeUITitlePanel1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.DrakeUITitlePanel1.Name = "DrakeUITitlePanel1";
		this.DrakeUITitlePanel1.RectColor = System.Drawing.Color.Blue;
		this.DrakeUITitlePanel1.Size = new System.Drawing.Size(324, 107);
		this.DrakeUITitlePanel1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUITitlePanel1.StyleCustomMode = true;
		this.DrakeUITitlePanel1.TabIndex = 6;
		this.DrakeUITitlePanel1.Text = null;
		this.DrakeUITitlePanel1.TitleColor = System.Drawing.Color.Red;
		this.DrakeUITitlePanel1.TitleHeight = 0;
		this.DrakeUITitlePanel1.TitleInterval = 0;
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(246, 66);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(40, 35);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.TabIndex = 7;
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click_1);
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(157, 67);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(39, 35);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.Symbol = 61453;
		this.drakeUIButtonIcon1.TabIndex = 6;
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click_1);
		this.guna2AnimateWindow1.TargetForm = this;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.Black;
		base.ClientSize = new System.Drawing.Size(324, 107);
		base.ControlBox = false;
		base.Controls.Add(this.DrakeUITitlePanel1);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.MaximizeBox = false;
		this.MaximumSize = new System.Drawing.Size(324, 107);
		base.MinimizeBox = false;
		this.MinimumSize = new System.Drawing.Size(324, 107);
		base.Name = "Dialog1";
		base.ShowIcon = false;
		base.ShowInTaskbar = false;
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
		this.Text = "Select";
		base.TopMost = true;
		this.DrakeUITitlePanel1.ResumeLayout(false);
		this.DrakeUITitlePanel1.PerformLayout();
		base.ResumeLayout(false);
	}

	private void translateme()
	{
	}

	private void Cancel_Button_Click(object sender, EventArgs e)
	{
	}

	private void Dialog1_Load(object sender, EventArgs e)
	{
		Mytitle.Text = Title;
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
	}

	private void DrakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
	}

	private void drakeUIButtonIcon2_Click_1(object sender, EventArgs e)
	{
		TheText = Mytext.Text;
		base.DialogResult = DialogResult.OK;
		Close();
	}

	private void drakeUIButtonIcon1_Click_1(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.No;
		Close();
	}
}
