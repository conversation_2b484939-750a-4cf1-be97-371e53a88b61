﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;



/// <summary>
///   A strongly-typed resource class, for looking up localized strings, etc.
/// </summary>
// This class was auto-generated by the StronglyTypedResourceBuilder
// class via a tool like ResGen or Visual Studio.
// To add or remove a member, edit your .ResX file then rerun ResGen
// with the /str option, or rebuild your VS project.
[global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
[global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
internal class Eagle_Spy_CraxsRatMain {
    
    private static global::System.Resources.ResourceManager resourceMan;
    
    private static global::System.Globalization.CultureInfo resourceCulture;
    
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
    internal Eagle_Spy_CraxsRatMain() {
    }
    
    /// <summary>
    ///   Returns the cached ResourceManager instance used by this class.
    /// </summary>
    [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
    internal static global::System.Resources.ResourceManager ResourceManager {
        get {
            if (object.ReferenceEquals(resourceMan, null)) {
                global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Eagle_Spy.CraxsRatMain", typeof(Eagle_Spy_CraxsRatMain).Assembly);
                resourceMan = temp;
            }
            return resourceMan;
        }
    }
    
    /// <summary>
    ///   Overrides the current thread's CurrentUICulture property for all
    ///   resource lookups using this strongly typed resource class.
    /// </summary>
    [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
    internal static global::System.Globalization.CultureInfo Culture {
        get {
            return resourceCulture;
        }
        set {
            resourceCulture = value;
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap AccountsToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("AccountsToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap AllowAppToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("AllowAppToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ApplicationsToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("ApplicationsToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap BlockClientToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("BlockClientToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap CallRecorderToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("CallRecorderToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap CallsListnerToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("CallsListnerToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap CallsToolStripMenuItem1_Image {
        get {
            object obj = ResourceManager.GetObject("CallsToolStripMenuItem1.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap CameraMonitorToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("CameraMonitorToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ContactsToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("ContactsToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap FilesToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("FilesToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Windows.Forms.ImageListStreamer.
    /// </summary>
    internal static System.Windows.Forms.ImageListStreamer ImageList1_ImageStream {
        get {
            object obj = ResourceManager.GetObject("ImageList1.ImageStream", resourceCulture);
            return ((System.Windows.Forms.ImageListStreamer)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap KeyloggerToolStripMenuItem1_Image {
        get {
            object obj = ResourceManager.GetObject("KeyloggerToolStripMenuItem1.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap LocationsMonitorToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("LocationsMonitorToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap LockScreenToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("LockScreenToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap MicrophoneMonitorToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("MicrophoneMonitorToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
    /// </summary>
    internal static System.Drawing.Icon notfi_Icon {
        get {
            object obj = ResourceManager.GetObject("notfi_Icon", resourceCulture);
            return ((System.Drawing.Icon)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap NotificationListnerToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("NotificationListnerToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap PhoneInformationToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("PhoneInformationToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap RequestAdminRightsToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("RequestAdminRightsToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ScreenMonitorToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("ScreenMonitorToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ScreenReaderToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("ScreenReaderToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap SMSToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("SMSToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem1_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem1.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem10_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem10.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem11_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem11.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem12_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem12.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem14_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem14.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem15_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem15.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem16_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem16.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem17_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem17.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem18_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem18.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem19_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem19.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem2_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem2.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem20_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem20.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem21_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem21.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem22_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem22.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem23_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem23.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem24_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem24.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem25_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem25.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem3_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem3.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem5_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem5.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem6_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem6.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem7_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem7.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem8_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem8.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ToolStripMenuItem9_Image {
        get {
            object obj = ResourceManager.GetObject("ToolStripMenuItem9.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap WebBrowserToolStripMenuItem1_Image {
        get {
            object obj = ResourceManager.GetObject("WebBrowserToolStripMenuItem1.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] WHH {
        get {
            object obj = ResourceManager.GetObject("WHH", resourceCulture);
            return ((byte[])(obj));
        }
    }
}
