{"RootPath": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC", "ProjectFileName": "Spyroid VIP.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Eaglespy\\About.cs"}, {"SourceFile": "Eaglespy\\ApkBuilder.cs"}, {"SourceFile": "Eaglespy\\ApkDexEncrypter.cs"}, {"SourceFile": "Eaglespy\\BankingInjections.cs"}, {"SourceFile": "Eaglespy\\Dialogue3.cs"}, {"SourceFile": "Eaglespy\\Injection.cs"}, {"SourceFile": "Eaglespy\\LangSelect.cs"}, {"SourceFile": "Eaglespy\\Lockscreen.cs"}, {"SourceFile": "Eaglespy\\Msgbox.cs"}, {"SourceFile": "Eaglespy\\Program.cs"}, {"SourceFile": "Eaglespy\\Ransomeware.cs"}, {"SourceFile": "Eaglespy\\ServerManager.cs"}, {"SourceFile": "Eaglespy\\SmartInjection.cs"}, {"SourceFile": "Eagle_Spy.MetroSet_UI\\Controls.cs"}, {"SourceFile": "Eagle_Spy.My.Resources\\Resources.cs"}, {"SourceFile": "Eagle_Spy.My\\MyApplication.cs"}, {"SourceFile": "Eagle_Spy.My\\MyComputer.cs"}, {"SourceFile": "Eagle_Spy.My\\MyProject.cs"}, {"SourceFile": "Eagle_Spy.My\\MySettings.cs"}, {"SourceFile": "Eagle_Spy.My\\MySettingsProperty.cs"}, {"SourceFile": "Eagle_Spy.sockets\\Accept.cs"}, {"SourceFile": "Eagle_Spy.sockets\\Client.cs"}, {"SourceFile": "Eagle_Spy.sockets\\Data.cs"}, {"SourceFile": "Eagle_Spy\\AccountManager.cs"}, {"SourceFile": "Eagle_Spy\\AddNumber.cs"}, {"SourceFile": "Eagle_Spy\\alertform.cs"}, {"SourceFile": "Eagle_Spy\\Apk_studio.cs"}, {"SourceFile": "Eagle_Spy\\Applications.cs"}, {"SourceFile": "Eagle_Spy\\AppsProperties.cs"}, {"SourceFile": "Eagle_Spy\\AsyncLock.cs"}, {"SourceFile": "Eagle_Spy\\Build.cs"}, {"SourceFile": "Eagle_Spy\\Button5.cs"}, {"SourceFile": "Eagle_Spy\\CallPhone.cs"}, {"SourceFile": "Eagle_Spy\\CallsManager.cs"}, {"SourceFile": "Eagle_Spy\\CameraManager.cs"}, {"SourceFile": "Eagle_Spy\\ClipboardManager.cs"}, {"SourceFile": "Eagle_Spy\\clrSAVE.cs"}, {"SourceFile": "Eagle_Spy\\clsComputerInfo.cs"}, {"SourceFile": "Eagle_Spy\\Codes.cs"}, {"SourceFile": "Eagle_Spy\\Color_Box0.cs"}, {"SourceFile": "Eagle_Spy\\comptableform.cs"}, {"SourceFile": "Eagle_Spy\\ContactsManager.cs"}, {"SourceFile": "Eagle_Spy\\CustomFont.cs"}, {"SourceFile": "Eagle_Spy\\CustomFontDrawString.cs"}, {"SourceFile": "Eagle_Spy\\DebugProtect1.cs"}, {"SourceFile": "Eagle_Spy\\Dialog1.cs"}, {"SourceFile": "Eagle_Spy\\Dialog2.cs"}, {"SourceFile": "Eagle_Spy\\DialogPloice.cs"}, {"SourceFile": "Eagle_Spy\\Download.cs"}, {"SourceFile": "Eagle_Spy\\Drooper.cs"}, {"SourceFile": "Eagle_Spy\\EagleAlert.cs"}, {"SourceFile": "Eagle_Spy\\Eaglepopup.cs"}, {"SourceFile": "Eagle_Spy\\EagleSpyCallLogs.cs"}, {"SourceFile": "Eagle_Spy\\EagleSpyMain.cs"}, {"SourceFile": "Eagle_Spy\\EagleSpyMsgbox.cs"}, {"SourceFile": "Eagle_Spy\\Editor.cs"}, {"SourceFile": "Eagle_Spy\\EditSocket.cs"}, {"SourceFile": "Eagle_Spy\\FileManager.cs"}, {"SourceFile": "Eagle_Spy\\Form1.cs"}, {"SourceFile": "Eagle_Spy\\GeoIP.cs"}, {"SourceFile": "Eagle_Spy\\GetCountryName2.cs"}, {"SourceFile": "Eagle_Spy\\GetFlagThisIp.cs"}, {"SourceFile": "Eagle_Spy\\getIconFrmReg.cs"}, {"SourceFile": "Eagle_Spy\\Icons.cs"}, {"SourceFile": "Eagle_Spy\\information.cs"}, {"SourceFile": "Eagle_Spy\\infoServer.cs"}, {"SourceFile": "Eagle_Spy\\inp.cs"}, {"SourceFile": "Eagle_Spy\\Jector.cs"}, {"SourceFile": "Eagle_Spy\\Keylogger.cs"}, {"SourceFile": "Eagle_Spy\\LanguageSelector.cs"}, {"SourceFile": "Eagle_Spy\\ListData.cs"}, {"SourceFile": "Eagle_Spy\\LocationManager.cs"}, {"SourceFile": "Eagle_Spy\\Microphone.cs"}, {"SourceFile": "Eagle_Spy\\MultiSounds.cs"}, {"SourceFile": "Eagle_Spy\\MyWebClient.cs"}, {"SourceFile": "Eagle_Spy\\NativeMethods.cs"}, {"SourceFile": "Eagle_Spy\\nonetform.cs"}, {"SourceFile": "Eagle_Spy\\NotificationMaker.cs"}, {"SourceFile": "Eagle_Spy\\Notifications.cs"}, {"SourceFile": "Eagle_Spy\\Notif_Sound.cs"}, {"SourceFile": "Eagle_Spy\\PBar.cs"}, {"SourceFile": "Eagle_Spy\\PermissionsManager.cs"}, {"SourceFile": "Eagle_Spy\\Ports.cs"}, {"SourceFile": "Eagle_Spy\\RefreshExplorer.cs"}, {"SourceFile": "Eagle_Spy\\RegistryHandler.cs"}, {"SourceFile": "Eagle_Spy\\Report.cs"}, {"SourceFile": "Eagle_Spy\\ResizeableControl.cs"}, {"SourceFile": "Eagle_Spy\\reso.cs"}, {"SourceFile": "Eagle_Spy\\RTB.cs"}, {"SourceFile": "Eagle_Spy\\ScreenLoger.cs"}, {"SourceFile": "Eagle_Spy\\ScreenReader.cs"}, {"SourceFile": "Eagle_Spy\\ScreenReaderV2.cs"}, {"SourceFile": "Eagle_Spy\\SecurityKey.cs"}, {"SourceFile": "Eagle_Spy\\SelfRemove.cs"}, {"SourceFile": "Eagle_Spy\\Settings.cs"}, {"SourceFile": "Eagle_Spy\\ShellTerminal.cs"}, {"SourceFile": "Eagle_Spy\\smsMaker.cs"}, {"SourceFile": "Eagle_Spy\\SMSManager.cs"}, {"SourceFile": "Eagle_Spy\\snapsdownloader.cs"}, {"SourceFile": "Eagle_Spy\\SpySettings.cs"}, {"SourceFile": "Eagle_Spy\\Upload.cs"}, {"SourceFile": "Eagle_Spy\\WebViewMonitor.cs"}, {"SourceFile": "Eagle_Spy\\Win_Users.cs"}, {"SourceFile": "Eagle_Spy\\ZoomPictureBox.cs"}, {"SourceFile": "Eagle_Spy_Applications.cs"}, {"SourceFile": "Eagle_Spy_Build.cs"}, {"SourceFile": "Eagle_Spy_Eaglepopup.cs"}, {"SourceFile": "Eagle_Spy_EagleSettings.cs"}, {"SourceFile": "Eagle_Spy_EagleSpyMain.cs"}, {"SourceFile": "Eagle_Spy_Microphone.cs"}, {"SourceFile": "Eagle_Spy_PermissionsManager.cs"}, {"SourceFile": "Eagle_Spy_Ports.cs"}, {"SourceFile": "Eagle_Spy_Resources.cs"}, {"SourceFile": "Eagle_Spy_ScreenShoter.cs"}, {"SourceFile": "Eagle_Spy_Settings.cs"}, {"SourceFile": "Eagle_Spy_tipfirewalltracker.cs"}, {"SourceFile": "Eagle_Spy_Updater.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "VB_0024AnonymousDelegate_0.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\DrakeUI.Framework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\GeoIPCitys.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\Guna.UI2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\LiveCharts.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\LiveCharts.WinForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\LiveCharts.Wpf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.VisualBasic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\SipaaFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\Siticone.Desktop.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IO.Compression.FileSystem.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\WinMM.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\EagleSpy Spyroid VIP Full SRC\\bin\\Debug\\Spyroid VIP.exe", "OutputItemRelativePath": "Spyroid VIP.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}