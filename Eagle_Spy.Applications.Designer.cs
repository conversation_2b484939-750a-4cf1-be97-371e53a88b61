﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;



/// <summary>
///   A strongly-typed resource class, for looking up localized strings, etc.
/// </summary>
// This class was auto-generated by the StronglyTypedResourceBuilder
// class via a tool like ResGen or Visual Studio.
// To add or remove a member, edit your .ResX file then rerun ResGen
// with the /str option, or rebuild your VS project.
[global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
[global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
internal class Eagle_Spy_Applications {
    
    private static global::System.Resources.ResourceManager resourceMan;
    
    private static global::System.Globalization.CultureInfo resourceCulture;
    
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
    internal Eagle_Spy_Applications() {
    }
    
    /// <summary>
    ///   Returns the cached ResourceManager instance used by this class.
    /// </summary>
    [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
    internal static global::System.Resources.ResourceManager ResourceManager {
        get {
            if (object.ReferenceEquals(resourceMan, null)) {
                global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Eagle_Spy.Applications", typeof(Eagle_Spy_Applications).Assembly);
                resourceMan = temp;
            }
            return resourceMan;
        }
    }
    
    /// <summary>
    ///   Overrides the current thread's CurrentUICulture property for all
    ///   resource lookups using this strongly typed resource class.
    /// </summary>
    [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
    internal static global::System.Globalization.CultureInfo Culture {
        get {
            return resourceCulture;
        }
        set {
            resourceCulture = value;
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap _1_Ajditq7CoiSbj9_2OPAO8w {
        get {
            object obj = ResourceManager.GetObject("1_Ajditq7CoiSbj9-2OPAO8w", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap _3825032 {
        get {
            object obj = ResourceManager.GetObject("3825032", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap AgentSpy {
        get {
            object obj = ResourceManager.GetObject("AgentSpy", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Android_logo_2019__stacked_ {
        get {
            object obj = ResourceManager.GetObject("Android_logo_2019_(stacked)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Android_logo_2019__stacked_1 {
        get {
            object obj = ResourceManager.GetObject("Android_logo_2019_(stacked)1", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap android_security_2x {
        get {
            object obj = ResourceManager.GetObject("android-security_2x", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap arrow_down_2_50 {
        get {
            object obj = ResourceManager.GetObject("arrow down 2 50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap bitcoin_encryption {
        get {
            object obj = ResourceManager.GetObject("bitcoin-encryption", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap bitfinext {
        get {
            object obj = ResourceManager.GetObject("bitfinext", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap bitrex {
        get {
            object obj = ResourceManager.GetObject("bitrex", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap blockchain_com_logo_300x300 {
        get {
            object obj = ResourceManager.GetObject("blockchain.com-logo-300x300", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap bybit_com {
        get {
            object obj = ResourceManager.GetObject("bybit.com", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap coinbase_icon2 {
        get {
            object obj = ResourceManager.GetObject("coinbase-icon2", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap coinbasewallet {
        get {
            object obj = ResourceManager.GetObject("coinbasewallet", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap communityIcon_dsj9hnnlkiub1_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("communityIcon_dsj9hnnlkiub1-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Compile {
        get {
            object obj = ResourceManager.GetObject("Compile", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Decompile {
        get {
            object obj = ResourceManager.GetObject("Decompile", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap decsmall {
        get {
            object obj = ResourceManager.GetObject("decsmall", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap download_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("download-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap download_removebg_preview1 {
        get {
            object obj = ResourceManager.GetObject("download-removebg-preview1", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap eagle {
        get {
            object obj = ResourceManager.GetObject("eagle", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap EAGLESPY {
        get {
            object obj = ResourceManager.GetObject("EAGLESPY", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap exodus {
        get {
            object obj = ResourceManager.GetObject("exodus", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap frame_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("frame-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap glogo {
        get {
            object obj = ResourceManager.GetObject("glogo", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap homeorig {
        get {
            object obj = ResourceManager.GetObject("homeorig", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap homewhite {
        get {
            object obj = ResourceManager.GetObject("homewhite", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_access_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-access-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_128 {
        get {
            object obj = ResourceManager.GetObject("icons8-android-128", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_128__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-android-128 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_phone_96 {
        get {
            object obj = ResourceManager.GetObject("icons8-android-phone-96", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_studio_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-android-studio-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_studio_100__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-android-studio-100 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_studio_100__1_1 {
        get {
            object obj = ResourceManager.GetObject("icons8-android-studio-100 (1)1", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_studio_100__2_ {
        get {
            object obj = ResourceManager.GetObject("icons8-android-studio-100 (2)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_android_studio_48 {
        get {
            object obj = ResourceManager.GetObject("icons8-android-studio-48", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_approval_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-approval-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_approval_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-approval-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_back_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-back-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_bank_1001 {
        get {
            object obj = ResourceManager.GetObject("icons8-bank-1001", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_bank_60 {
        get {
            object obj = ResourceManager.GetObject("icons8-bank-60", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_bank_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-bank-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_binance_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-binance-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_bitcoin {
        get {
            object obj = ResourceManager.GetObject("icons8-bitcoin", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_block_user_96 {
        get {
            object obj = ResourceManager.GetObject("icons8-block-user-96", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_browser_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-browser-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_business_96 {
        get {
            object obj = ResourceManager.GetObject("icons8-business-96", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_calls_48 {
        get {
            object obj = ResourceManager.GetObject("icons8-calls-48", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_camera_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-camera-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_cell_phone_with_up_and_down_arrows_96 {
        get {
            object obj = ResourceManager.GetObject("icons8-cell-phone-with-up-and-down-arrows-96", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_clean_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-clean-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_client_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-client-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_clipboard_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-clipboard-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_cloud_64__1_1 {
        get {
            object obj = ResourceManager.GetObject("icons8-cloud-64 (1)1", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_cloud_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-cloud-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_contact_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-contact-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_contacts_48 {
        get {
            object obj = ResourceManager.GetObject("icons8-contacts-48", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_contacts_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-contacts-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_correct_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-correct-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_cut_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-cut-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_delete_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-delete-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_delete_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-delete-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_down_arrow_58 {
        get {
            object obj = ResourceManager.GetObject("icons8-down-arrow-58", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_down_arrow_58__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-down-arrow-58 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_electric_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-electric-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_electric_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-electric-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_email_48 {
        get {
            object obj = ResourceManager.GetObject("icons8-email-48", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_email_96 {
        get {
            object obj = ResourceManager.GetObject("icons8-email-96", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_fingerprint_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-fingerprint-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_folder_32 {
        get {
            object obj = ResourceManager.GetObject("icons8-folder-32", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_folder_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-folder-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_folder_501 {
        get {
            object obj = ResourceManager.GetObject("icons8-folder-501", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_folder_60 {
        get {
            object obj = ResourceManager.GetObject("icons8-folder-60", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_folder_601 {
        get {
            object obj = ResourceManager.GetObject("icons8-folder-601", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_google_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-google-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_google_play_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-google-play-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_google_play_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-google-play-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_guarantee_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-guarantee-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_hand_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-hand-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_hand_drag_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-hand-drag-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_information_32 {
        get {
            object obj = ResourceManager.GetObject("icons8-information-32", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_information_32__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-information-32 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_internet_antenna_30 {
        get {
            object obj = ResourceManager.GetObject("icons8-internet-antenna-30", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_iphone_15_pro {
        get {
            object obj = ResourceManager.GetObject("icons8-iphone-15-pro", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_keyboard_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-keyboard-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_keyboard_66 {
        get {
            object obj = ResourceManager.GetObject("icons8-keyboard-66", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_kindle_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-kindle-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_location_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-location-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_location_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-location-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_lock_643 {
        get {
            object obj = ResourceManager.GetObject("icons8-lock-643", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_lock_96 {
        get {
            object obj = ResourceManager.GetObject("icons8-lock-96", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_lock_961 {
        get {
            object obj = ResourceManager.GetObject("icons8-lock-961", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_locked_network_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-locked-network-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_message_48 {
        get {
            object obj = ResourceManager.GetObject("icons8-message-48", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_message_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-message-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_message_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-message-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_microphone_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-microphone-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_microsoft_admin_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-microsoft-admin-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_microsoft_admin_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-microsoft-admin-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_minus_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-minus-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_mobile_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-mobile-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_mobile_pattern_lock_53 {
        get {
            object obj = ResourceManager.GetObject("icons8-mobile-pattern-lock-53", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_no_audio_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-no-audio-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_notification_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-notification-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_notification_501 {
        get {
            object obj = ResourceManager.GetObject("icons8-notification-501", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_notification_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-notification-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_number_pad_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-number-pad-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_number_pad_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-number-pad-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_ocr_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-ocr-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_online_support_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-online-support-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_online_support_64__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-online-support-64 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_online_support_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-online-support-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_open_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-open-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_padlock_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-padlock-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_padlock_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-padlock-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_padlock_66 {
        get {
            object obj = ResourceManager.GetObject("icons8-padlock-66", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_password_lock_53 {
        get {
            object obj = ResourceManager.GetObject("icons8-password-lock-53", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_phone_1001 {
        get {
            object obj = ResourceManager.GetObject("icons8-phone-1001", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_phone_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-phone-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_play_record_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-play-record-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_play_record_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-play-record-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_power_off_button_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-power-off-button-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_public_speaking_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-public-speaking-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_renbtc_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-renbtc-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_renbtc_64__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-renbtc-64 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_reset_64__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-reset-64 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_restart_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-restart-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_restart_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-restart-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_select_all_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-select-all-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_send_30 {
        get {
            object obj = ResourceManager.GetObject("icons8-send-30", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_settings_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-settings-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_settings_64__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-settings-64 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_shield_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-shield-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_shortcut_1001 {
        get {
            object obj = ResourceManager.GetObject("icons8-shortcut-1001", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_sms_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-sms-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_speaker_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-speaker-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_start_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-start-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_stop_100 {
        get {
            object obj = ResourceManager.GetObject("icons8-stop-100", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_stop_50 {
        get {
            object obj = ResourceManager.GetObject("icons8-stop-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_stop_503 {
        get {
            object obj = ResourceManager.GetObject("icons8-stop-503", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_syringe_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-syringe-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_syringe_64__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-syringe-64 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_syringe_64__2_ {
        get {
            object obj = ResourceManager.GetObject("icons8-syringe-64 (2)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_target_audience_68 {
        get {
            object obj = ResourceManager.GetObject("icons8-target-audience-68", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_terminal_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-terminal-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_terminal_641 {
        get {
            object obj = ResourceManager.GetObject("icons8-terminal-641", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_text_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-text-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_unlock_60 {
        get {
            object obj = ResourceManager.GetObject("icons8-unlock-60", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_video_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-video-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_wallet {
        get {
            object obj = ResourceManager.GetObject("icons8-wallet", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_wi_fi_connected_64 {
        get {
            object obj = ResourceManager.GetObject("icons8-wi-fi-connected-64", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_wi_fi_connected_64__1_ {
        get {
            object obj = ResourceManager.GetObject("icons8-wi-fi-connected-64 (1)", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_x_30 {
        get {
            object obj = ResourceManager.GetObject("icons8-x-30", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_x_642 {
        get {
            object obj = ResourceManager.GetObject("icons8-x-642", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap icons8_xbox_menu_30 {
        get {
            object obj = ResourceManager.GetObject("icons8-xbox-menu-30", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap imggg {
        get {
            object obj = ResourceManager.GetObject("imggg", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap kraken {
        get {
            object obj = ResourceManager.GetObject("kraken", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Kucoin_wallet_logo {
        get {
            object obj = ResourceManager.GetObject("Kucoin-wallet-logo", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap mcafee {
        get {
            object obj = ResourceManager.GetObject("mcafee", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap mcafee_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("mcafee-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap mcafee_removebg_preview1 {
        get {
            object obj = ResourceManager.GetObject("mcafee-removebg-preview1", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap miuiautostart {
        get {
            object obj = ResourceManager.GetObject("miuiautostart", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap modern_oval_notch_smartphone_realistic_mockup_Photoroom_png_Photoroom {
        get {
            object obj = ResourceManager.GetObject("modern_oval_notch_smartphone_realistic_mockup-Photoroom.png-Photoroom", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap modern_oval_notch_smartphone_realistic_mockup_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("modern_oval_notch_smartphone_realistic_mockup-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap oie_glitters {
        get {
            object obj = ResourceManager.GetObject("oie_glitters", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap OpenToolStripMenuItem_Image {
        get {
            object obj = ResourceManager.GetObject("OpenToolStripMenuItem.Image", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap petya_ransomware_featured_3 {
        get {
            object obj = ResourceManager.GetObject("petya-ransomware-featured-3", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap photo_2023_11_21_13_40_14 {
        get {
            object obj = ResourceManager.GetObject("photo_2023-11-21_13-40-14", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap photo_2024_03_18_17_55_50 {
        get {
            object obj = ResourceManager.GetObject("photo_2024-03-18_17-55-50", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap portbackground {
        get {
            object obj = ResourceManager.GetObject("portbackground", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap protectapk {
        get {
            object obj = ResourceManager.GetObject("protectapk", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap protectapk1 {
        get {
            object obj = ResourceManager.GetObject("protectapk1", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap rpand {
        get {
            object obj = ResourceManager.GetObject("rpand", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap saveicon_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("saveicon-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap SBER_ME_10de1f5f {
        get {
            object obj = ResourceManager.GetObject("SBER.ME-10de1f5f", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Screenshot_2024_01_28_125819_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("Screenshot_2024-01-28_125819-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Screenshot_2024_03_18_191904 {
        get {
            object obj = ResourceManager.GetObject("Screenshot 2024-03-18 191904", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Screenshot_2024_03_30_040925_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("Screenshot_2024-03-30_040925-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap server_8385949 {
        get {
            object obj = ResourceManager.GetObject("server_8385949", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap shield_6643256 {
        get {
            object obj = ResourceManager.GetObject("shield_6643256", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap sign {
        get {
            object obj = ResourceManager.GetObject("sign", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap SILENT {
        get {
            object obj = ResourceManager.GetObject("SILENT", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap skull_red_icon_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("skull-red-icon-removebg-preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap sss {
        get {
            object obj = ResourceManager.GetObject("sss", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap sssss {
        get {
            object obj = ResourceManager.GetObject("sssss", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap telegram {
        get {
            object obj = ResourceManager.GetObject("telegram", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap toggle {
        get {
            object obj = ResourceManager.GetObject("toggle", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap UnZip {
        get {
            object obj = ResourceManager.GetObject("UnZip", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap wechat_logo_png_transparent {
        get {
            object obj = ResourceManager.GetObject("wechat-logo-png-transparent", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] WHH {
        get {
            object obj = ResourceManager.GetObject("WHH", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Zip {
        get {
            object obj = ResourceManager.GetObject("Zip", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Zipalign {
        get {
            object obj = ResourceManager.GetObject("Zipalign", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
}
