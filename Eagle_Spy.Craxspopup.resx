﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
<resheader name="resmimetype"><value>text/microsoft-resx</value></resheader><resheader name="version"><value>1.3</value></resheader><resheader name="reader"><value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><resheader name="writer"><value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><data name="$this.BackgroundImage" mimetype="application/x-microsoft.net.object.binary.base64"><value>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</value></data></root>