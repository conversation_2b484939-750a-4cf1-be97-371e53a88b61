using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class AccountManager : Form
{
	private IContainer components;

	public TcpClient Client;

	public Client classClient;

	public string Title;

	public string tmpFolderUSER;

	public string tmpClientName;

	public string tmpCountry;

	public string tmpAddressIP;

	private bool BoxTitlePaintEventArgsWait;

	internal DataGridView DGV0;

	[AccessedThroughProperty("ctxMenu")]
	internal ContextMenuStrip ctxMenu;

	internal ToolStripMenuItem SaveToolStripMenuItem;

	internal ToolStripMenuItem SaveAsToolStripMenuItem;

	internal System.Windows.Forms.Timer TOpacity;

	[AccessedThroughProperty("PB")]
	internal ProgressBar PB;

	internal PictureBox BoxTitle;

	[AccessedThroughProperty("Column1")]
	internal DataGridViewTextBoxColumn Column1;

	[AccessedThroughProperty("Column6")]
	internal DataGridViewTextBoxColumn Column6;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Guna2Panel guna2Panel1;

	private Guna2Panel guna2Panel2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private DrakeUIButtonIcon drakeUIButtonIcon3;

	private DrakeUIButtonIcon drakeUIButtonIcon2;

	private Label label2;

	private Guna2ControlBox guna2ControlBox1;

	[AccessedThroughProperty("Column2")]
	internal DataGridViewImageColumn Column2;

	public AccountManager()
	{
		base.Load += AccountManager_Load;
		base.Activated += AngelAndroidForm_Activated;
		base.Deactivate += AngelAndroidForm_Deactivate;
		Title = "null";
		BoxTitlePaintEventArgsWait = false;
		InitializeComponent();
	}

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
		this.DGV0 = new System.Windows.Forms.DataGridView();
		this.Column1 = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.Column6 = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.Column2 = new System.Windows.Forms.DataGridViewImageColumn();
		this.ctxMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
		this.SaveToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.SaveAsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.TOpacity = new System.Windows.Forms.Timer(this.components);
		this.PB = new System.Windows.Forms.ProgressBar();
		this.BoxTitle = new System.Windows.Forms.PictureBox();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.guna2Panel1 = new Guna.UI2.WinForms.Guna2Panel();
		this.drakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon2 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2Panel2 = new Guna.UI2.WinForms.Guna2Panel();
		this.label2 = new System.Windows.Forms.Label();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		((System.ComponentModel.ISupportInitialize)this.DGV0).BeginInit();
		this.ctxMenu.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.BoxTitle).BeginInit();
		this.guna2Panel1.SuspendLayout();
		this.guna2Panel2.SuspendLayout();
		base.SuspendLayout();
		this.DGV0.AllowUserToAddRows = false;
		this.DGV0.AllowUserToDeleteRows = false;
		this.DGV0.AllowUserToResizeColumns = false;
		this.DGV0.AllowUserToResizeRows = false;
		dataGridViewCellStyle.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle.SelectionBackColor = System.Drawing.Color.FromArgb(36, 7, 115);
		dataGridViewCellStyle.SelectionForeColor = System.Drawing.Color.FromArgb(192, 192, 255);
		this.DGV0.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle;
		this.DGV0.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.AllCells;
		this.DGV0.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
		this.DGV0.BackgroundColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.DGV0.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.DGV0.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleVertical;
		this.DGV0.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
		dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle2.Font = new System.Drawing.Font("Consolas", 8.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(36, 7, 115);
		dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.FromArgb(192, 192, 255);
		dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
		this.DGV0.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
		this.DGV0.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.DGV0.Columns.AddRange(this.Column1, this.Column6, this.Column2);
		this.DGV0.ContextMenuStrip = this.ctxMenu;
		dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle3.Font = new System.Drawing.Font("Calibri", 8.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(36, 7, 115);
		dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(192, 192, 255);
		dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
		this.DGV0.DefaultCellStyle = dataGridViewCellStyle3;
		this.DGV0.EditMode = System.Windows.Forms.DataGridViewEditMode.EditProgrammatically;
		this.DGV0.EnableHeadersVisualStyles = false;
		this.DGV0.GridColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.DGV0.Location = new System.Drawing.Point(3, 33);
		this.DGV0.Name = "DGV0";
		this.DGV0.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
		dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		dataGridViewCellStyle4.Font = new System.Drawing.Font("Calibri", 8.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		dataGridViewCellStyle4.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(36, 7, 115);
		dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.FromArgb(192, 192, 255);
		dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
		this.DGV0.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
		this.DGV0.RowHeadersVisible = false;
		dataGridViewCellStyle5.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.FromArgb(36, 7, 115);
		dataGridViewCellStyle5.SelectionForeColor = System.Drawing.Color.FromArgb(192, 192, 255);
		this.DGV0.RowsDefaultCellStyle = dataGridViewCellStyle5;
		this.DGV0.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
		this.DGV0.Size = new System.Drawing.Size(427, 454);
		this.DGV0.TabIndex = 3;
		this.DGV0.RowsAdded += new System.Windows.Forms.DataGridViewRowsAddedEventHandler(DGV0_RowsAdded);
		this.DGV0.RowsRemoved += new System.Windows.Forms.DataGridViewRowsRemovedEventHandler(DGV0_RowsRemoved);
		this.DGV0.SelectionChanged += new System.EventHandler(DGV0_SelectionChanged);
		this.Column1.HeaderText = "Program";
		this.Column1.Name = "Column1";
		this.Column1.Width = 72;
		this.Column6.HeaderText = "Account";
		this.Column6.Name = "Column6";
		this.Column6.Width = 72;
		this.Column2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None;
		this.Column2.FillWeight = 1f;
		this.Column2.HeaderText = "";
		this.Column2.MinimumWidth = 2;
		this.Column2.Name = "Column2";
		this.Column2.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
		this.Column2.Width = 2;
		this.ctxMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[2] { this.SaveToolStripMenuItem, this.SaveAsToolStripMenuItem });
		this.ctxMenu.Name = "ctxMenu";
		this.ctxMenu.ShowImageMargin = false;
		this.ctxMenu.Size = new System.Drawing.Size(90, 48);
		this.SaveToolStripMenuItem.Name = "SaveToolStripMenuItem";
		this.SaveToolStripMenuItem.Size = new System.Drawing.Size(89, 22);
		this.SaveToolStripMenuItem.Text = "Save";
		this.SaveToolStripMenuItem.Visible = false;
		this.SaveToolStripMenuItem.Click += new System.EventHandler(SaveToolStripMenuItem_Click);
		this.SaveAsToolStripMenuItem.Name = "SaveAsToolStripMenuItem";
		this.SaveAsToolStripMenuItem.Size = new System.Drawing.Size(89, 22);
		this.SaveAsToolStripMenuItem.Text = "Save As";
		this.SaveAsToolStripMenuItem.Visible = false;
		this.SaveAsToolStripMenuItem.Click += new System.EventHandler(SaveAsToolStripMenuItem_Click);
		this.TOpacity.Interval = 1;
		this.TOpacity.Tick += new System.EventHandler(TOpacity_Tick);
		this.PB.BackColor = System.Drawing.Color.Navy;
		this.PB.Location = new System.Drawing.Point(3, 524);
		this.PB.Name = "PB";
		this.PB.Size = new System.Drawing.Size(427, 5);
		this.PB.TabIndex = 6;
		this.BoxTitle.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.BoxTitle.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.BoxTitle.ErrorImage = null;
		this.BoxTitle.InitialImage = null;
		this.BoxTitle.Location = new System.Drawing.Point(0, 532);
		this.BoxTitle.Name = "BoxTitle";
		this.BoxTitle.Size = new System.Drawing.Size(434, 10);
		this.BoxTitle.TabIndex = 7;
		this.BoxTitle.TabStop = false;
		this.BoxTitle.Paint += new System.Windows.Forms.PaintEventHandler(BoxTitle_Paint);
		this.BoxTitle.Resize += new System.EventHandler(BoxTitle_Resize);
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.guna2Panel1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2Panel1.BorderColor = System.Drawing.Color.Navy;
		this.guna2Panel1.BorderRadius = 15;
		this.guna2Panel1.BorderThickness = 1;
		this.guna2Panel1.Controls.Add(this.drakeUIButtonIcon3);
		this.guna2Panel1.Controls.Add(this.drakeUIButtonIcon2);
		this.guna2Panel1.Controls.Add(this.drakeUIButtonIcon1);
		this.guna2Panel1.Controls.Add(this.guna2Panel2);
		this.guna2Panel1.Controls.Add(this.DGV0);
		this.guna2Panel1.Controls.Add(this.PB);
		this.guna2Panel1.Controls.Add(this.BoxTitle);
		this.guna2Panel1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.guna2Panel1.Location = new System.Drawing.Point(0, 0);
		this.guna2Panel1.Name = "guna2Panel1";
		this.guna2Panel1.Size = new System.Drawing.Size(434, 542);
		this.guna2Panel1.TabIndex = 8;
		this.drakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon3.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon3.FillDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.Font = new System.Drawing.Font("Candara", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon3.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon3.ForeDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.ForePressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon3.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon3.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.drakeUIButtonIcon3.Location = new System.Drawing.Point(43, 493);
		this.drakeUIButtonIcon3.Name = "drakeUIButtonIcon3";
		this.drakeUIButtonIcon3.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon3.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.drakeUIButtonIcon3.Size = new System.Drawing.Size(102, 25);
		this.drakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon3.StyleCustomMode = true;
		this.drakeUIButtonIcon3.Symbol = 61564;
		this.drakeUIButtonIcon3.TabIndex = 13;
		this.drakeUIButtonIcon3.Text = "Saved Folder";
		this.drakeUIButtonIcon3.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
		this.drakeUIButtonIcon3.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.drakeUIButtonIcon2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon2.FillDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.Font = new System.Drawing.Font("Candara", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon2.ForeDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.ForePressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon2.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon2.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.drakeUIButtonIcon2.Location = new System.Drawing.Point(196, 495);
		this.drakeUIButtonIcon2.Name = "drakeUIButtonIcon2";
		this.drakeUIButtonIcon2.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon2.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.drakeUIButtonIcon2.Size = new System.Drawing.Size(56, 25);
		this.drakeUIButtonIcon2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon2.StyleCustomMode = true;
		this.drakeUIButtonIcon2.Symbol = 61639;
		this.drakeUIButtonIcon2.TabIndex = 12;
		this.drakeUIButtonIcon2.Text = "Save";
		this.drakeUIButtonIcon2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
		this.drakeUIButtonIcon2.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIButtonIcon1.FillDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Candara", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.ForeDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.ForePressColor = System.Drawing.Color.Aqua;
		this.drakeUIButtonIcon1.ForeSelectedColor = System.Drawing.Color.MediumSpringGreen;
		this.drakeUIButtonIcon1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(327, 495);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectHoverColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectPressColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectSelectedColor = System.Drawing.Color.Empty;
		this.drakeUIButtonIcon1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(69, 25);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.StyleCustomMode = true;
		this.drakeUIButtonIcon1.Symbol = 61639;
		this.drakeUIButtonIcon1.TabIndex = 11;
		this.drakeUIButtonIcon1.Text = "Save As";
		this.drakeUIButtonIcon1.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
		this.drakeUIButtonIcon1.TipsColor = System.Drawing.Color.FromArgb(0, 0, 192);
		this.guna2Panel2.BackColor = System.Drawing.Color.FromArgb(10, 0, 36);
		this.guna2Panel2.Controls.Add(this.guna2ControlBox1);
		this.guna2Panel2.Controls.Add(this.label2);
		this.guna2Panel2.Dock = System.Windows.Forms.DockStyle.Top;
		this.guna2Panel2.Location = new System.Drawing.Point(0, 0);
		this.guna2Panel2.Name = "guna2Panel2";
		this.guna2Panel2.Size = new System.Drawing.Size(434, 30);
		this.guna2Panel2.TabIndex = 8;
		this.guna2Panel2.MouseDown += new System.Windows.Forms.MouseEventHandler(guna2Panel2_MouseDown);
		this.label2.AutoSize = true;
		this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 14.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label2.Location = new System.Drawing.Point(145, 2);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(97, 24);
		this.label2.TabIndex = 15;
		this.label2.Text = "Accounts";
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.FromArgb(10, 0, 36);
		this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
		this.guna2ControlBox1.Location = new System.Drawing.Point(377, -1);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 16;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(434, 542);
		base.Controls.Add(this.guna2Panel1);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "AccountManager";
		base.Opacity = 0.0;
		this.Text = "AccountManager";
		base.Load += new System.EventHandler(AccountManager_Load);
		((System.ComponentModel.ISupportInitialize)this.DGV0).EndInit();
		this.ctxMenu.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.BoxTitle).EndInit();
		this.guna2Panel1.ResumeLayout(false);
		this.guna2Panel2.ResumeLayout(false);
		this.guna2Panel2.PerformLayout();
		base.ResumeLayout(false);
	}

	private void TOpacity_Tick(object sender, EventArgs e)
	{
		if (base.Opacity != 1.0)
		{
			base.Opacity += 0.1;
		}
		else
		{
			TOpacity.Enabled = false;
		}
	}

	private void AccountManager_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		base.Icon = new Icon(reso.res_Path + "\\Icons\\win\\1.ico");
		DGV0.ColumnHeadersDefaultCellStyle.Font = reso.f;
		DGV0.DefaultCellStyle.Font = reso.f;
		if (Operators.CompareString(SpySettings.SAVING_DATA, "No", TextCompare: false) == 0)
		{
			SaveToolStripMenuItem.Visible = true;
			SaveAsToolStripMenuItem.Visible = true;
		}
		Text = Title;
		TOpacity.Interval = SpySettings.T_Interval;
		TOpacity.Enabled = true;
		BoxTitlePaintEventArgsWait = true;
	}

	private void SaveAsToolStripMenuItem_Click(object sender, EventArgs e)
	{
		SaveFileDialog saveFileDialog = new SaveFileDialog();
		saveFileDialog.FileName = DateAndTime.Now.ToString("yyyy-dd-M--HH-mm-ss") + ".html";
		saveFileDialog.Filter = "html (*.html)|*.html";
		if (saveFileDialog.ShowDialog() == DialogResult.OK)
		{
			ThreadPool.QueueUserWorkItem([DebuggerHidden] (object a0) =>
			{
				reso.SAVEit((Array)a0);
			}, new object[8]
			{
				DGV0,
				"null",
				saveFileDialog.FileName,
				tmpClientName,
				tmpCountry + " - " + tmpAddressIP,
				"Accounts",
				"log",
				"null"
			});
		}
		saveFileDialog.Dispose();
	}

	private void SaveToolStripMenuItem_Click(object sender, EventArgs e)
	{
		reso.Directory_Exist(classClient);
		ThreadPool.QueueUserWorkItem([DebuggerHidden] (object a0) =>
		{
			reso.SAVEit((Array)a0);
		}, new object[8]
		{
			DGV0,
			tmpFolderUSER,
			"Account Manager",
			tmpClientName,
			tmpCountry + " - " + tmpAddressIP,
			"Accounts",
			"log",
			DateAndTime.Now.ToString("yyyy-dd-M--HH-mm-ss") + ".html"
		});
	}

	private void BoxTitle_Paint(object sender, PaintEventArgs e)
	{
		checked
		{
			if (BoxTitlePaintEventArgsWait)
			{
				int count = DGV0.Rows.Count;
				string text = "All " + Conversions.ToString(count);
				string text2 = "Selected " + Conversions.ToString(DGV0.SelectedRows.Count);
				Color defaultColor_Foreground = SpySettings.DefaultColor_Foreground;
				e.Graphics.DrawLine(new Pen(Color.FromArgb(50, defaultColor_Foreground.R, defaultColor_Foreground.G, defaultColor_Foreground.B)), 0, 1, BoxTitle.Width, 1);
				Brush brush = new SolidBrush(SpySettings.DefaultColor_Foreground);
				Brush brush2 = new SolidBrush(Color.FromArgb(170, BoxTitle.BackColor.R, BoxTitle.BackColor.G, BoxTitle.BackColor.B));
				Size size = TextRenderer.MeasureText(text + Strings.Space(10) + text2, reso.f);
				Rectangle rect = new Rectangle(0, 2, BoxTitle.Width, size.Height + 5);
				e.Graphics.FillRectangle(new Pen(brush2).Brush, rect);
				e.Graphics.DrawString(text + Strings.Space(10) + text2 + Strings.Space(10), reso.f, brush, 0f, 2f);
				Size size2 = TextRenderer.MeasureText("S", reso.f);
				if (BoxTitle.Height != size2.Height + 3)
				{
					BoxTitle.Height = size2.Height + 3;
				}
			}
		}
	}

	private void AngelAndroidForm_Activated(object sender, EventArgs e)
	{
		BoxTitle.Invalidate();
	}

	private void AngelAndroidForm_Deactivate(object sender, EventArgs e)
	{
		BoxTitle.Invalidate();
	}

	private void BoxTitle_Resize(object sender, EventArgs e)
	{
		BoxTitle.Invalidate();
	}

	private void DGV0_RowsRemoved(object sender, DataGridViewRowsRemovedEventArgs e)
	{
		BoxTitle.Invalidate();
	}

	private void DGV0_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
	{
		BoxTitle.Invalidate();
	}

	private void DGV0_SelectionChanged(object sender, EventArgs e)
	{
		BoxTitle.Invalidate();
	}

	[DllImport("user32.dll")]
	public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);

	[DllImport("user32.dll")]
	public static extern bool ReleaseCapture();

	private void guna2Panel2_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void UpdateEnglish()
	{
		label2.Text = "Accounts";
		Column1.HeaderText = "Program";
		Column6.HeaderText = "Account";
		drakeUIButtonIcon1.Text = "Save As";
		drakeUIButtonIcon2.Text = "Save";
		drakeUIButtonIcon3.Text = "Saved Folder";
	}

	private void UpdateChinese()
	{
		label2.Text = "账户";
		Column1.HeaderText = "程序";
		Column6.HeaderText = "账号";
		drakeUIButtonIcon1.Text = "另存为";
		drakeUIButtonIcon2.Text = "保存";
		drakeUIButtonIcon3.Text = "保存文件夹";
	}

	private void UpdateRussian()
	{
		label2.Text = "Аккаунты";
		Column1.HeaderText = "Программа";
		Column6.HeaderText = "Аккаунт";
		drakeUIButtonIcon1.Text = "Сохранить как";
		drakeUIButtonIcon2.Text = "Сохранить";
		drakeUIButtonIcon3.Text = "Папка для сохранения";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}
}
