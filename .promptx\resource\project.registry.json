{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-07T08:29:12.771Z", "updatedAt": "2025-08-07T08:29:12.775Z", "resourceCount": 6}, "resources": [{"id": "product-analysis-workflow", "source": "project", "protocol": "execution", "name": "Product Analysis Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/remote-control-pm/execution/product-analysis-workflow.execution.md", "metadata": {"createdAt": "2025-08-07T08:29:12.773Z", "updatedAt": "2025-08-07T08:29:12.773Z", "scannedAt": "2025-08-07T08:29:12.773Z", "path": "role/remote-control-pm/execution/product-analysis-workflow.execution.md"}}, {"id": "remote-control-pm", "source": "project", "protocol": "role", "name": "Remote Control Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/remote-control-pm/remote-control-pm.role.md", "metadata": {"createdAt": "2025-08-07T08:29:12.773Z", "updatedAt": "2025-08-07T08:29:12.773Z", "scannedAt": "2025-08-07T08:29:12.773Z", "path": "role/remote-control-pm/remote-control-pm.role.md"}}, {"id": "product-thinking", "source": "project", "protocol": "thought", "name": "Product Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/remote-control-pm/thought/product-thinking.thought.md", "metadata": {"createdAt": "2025-08-07T08:29:12.774Z", "updatedAt": "2025-08-07T08:29:12.774Z", "scannedAt": "2025-08-07T08:29:12.774Z", "path": "role/remote-control-pm/thought/product-thinking.thought.md"}}, {"id": "dev-pm-workflow", "source": "project", "protocol": "execution", "name": "Dev Pm Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/super-dev-pm/execution/dev-pm-workflow.execution.md", "metadata": {"createdAt": "2025-08-07T08:29:12.774Z", "updatedAt": "2025-08-07T08:29:12.774Z", "scannedAt": "2025-08-07T08:29:12.774Z", "path": "role/super-dev-pm/execution/dev-pm-workflow.execution.md"}}, {"id": "super-dev-pm", "source": "project", "protocol": "role", "name": "Super Dev Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/super-dev-pm/super-dev-pm.role.md", "metadata": {"createdAt": "2025-08-07T08:29:12.775Z", "updatedAt": "2025-08-07T08:29:12.775Z", "scannedAt": "2025-08-07T08:29:12.775Z", "path": "role/super-dev-pm/super-dev-pm.role.md"}}, {"id": "full-stack-thinking", "source": "project", "protocol": "thought", "name": "Full Stack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/super-dev-pm/thought/full-stack-thinking.thought.md", "metadata": {"createdAt": "2025-08-07T08:29:12.775Z", "updatedAt": "2025-08-07T08:29:12.775Z", "scannedAt": "2025-08-07T08:29:12.775Z", "path": "role/super-dev-pm/thought/full-stack-thinking.thought.md"}}], "stats": {"totalResources": 6, "byProtocol": {"execution": 2, "role": 2, "thought": 2}, "bySource": {"project": 6}}}