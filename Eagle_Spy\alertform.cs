using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows.Forms;
using DrakeUI.Framework;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class alertform : Form
{
	[CompilerGenerated]
	private sealed class VB_0024StateMachine_39_FadeIn : IAsyncStateMachine
	{
		public int _0024State;

		public AsyncVoidMethodBuilder _0024Builder;

		internal Form _0024VB_0024Local_o;

		internal int _0024VB_0024Local_interval;

		internal alertform _0024VB_0024Me;

		internal Form _0024S0;

		internal Exception _0024VB_0024ResumableLocal_ex_00241;

		internal TaskAwaiter _0024A0;

		[CompilerGenerated]
		internal void MoveNext()
		{
			int num = _0024State;
			try
			{
				try
				{
					TaskAwaiter awaiter;
					if (num != -3)
					{
						if (num != 0)
						{
							goto IL_0039;
						}
						num = -1;
						_0024State = -1;
						awaiter = _0024A0;
						_0024A0 = default(TaskAwaiter);
						goto IL_0071;
					}
					num = -1;
					_0024State = -1;
					return;
					IL_0071:
					awaiter.GetResult();
					awaiter = default(TaskAwaiter);
					(_0024S0 = _0024VB_0024Local_o).Opacity = _0024S0.Opacity + 0.05;
					goto IL_0039;
					IL_0039:
					if (_0024VB_0024Local_o.Opacity < 1.0)
					{
						awaiter = Task.Delay(_0024VB_0024Local_interval).GetAwaiter();
						if (!awaiter.IsCompleted)
						{
							num = 0;
							_0024State = 0;
							_0024A0 = awaiter;
							ref AsyncVoidMethodBuilder reference = ref _0024Builder;
							VB_0024StateMachine_39_FadeIn stateMachine = this;
							reference.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
							return;
						}
						goto IL_0071;
					}
					_0024VB_0024Local_o.Opacity = 1.0;
					_0024VB_0024Me.Showtime = DateTime.Now.AddSeconds(5.0);
					_0024VB_0024Me.closetimer.Start();
				}
				catch (Exception ex)
				{
					ProjectData.SetProjectError(ex);
					Exception ex2 = ex;
					_0024VB_0024ResumableLocal_ex_00241 = ex2;
					ProjectData.ClearProjectError();
				}
			}
			catch (Exception ex3)
			{
				ProjectData.SetProjectError(ex3);
				Exception exception = ex3;
				_0024State = -2;
				_0024Builder.SetException(exception);
				ProjectData.ClearProjectError();
				return;
			}
			num = -2;
			_0024State = -2;
			_0024Builder.SetResult();
		}

		void IAsyncStateMachine.MoveNext()
		{
			MoveNext();
		}

		[DebuggerNonUserCode]
		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine stateMachine)
		{
		}
	}

	private IContainer components;

	private Label msglabel;

	private const uint SWP_SHOWWINDOW = 64u;

	public string TheMessage;

	public int oldY;

	public Rectangle workingArea;

	public DateTime Showtime;

	private const int SW_SHOWNOACTIVATE = 4;

	public const int HWND_TOPMOST = -1;

	private const uint SWP_NOACTIVATE = 16u;

	private int counter;

	private int hold;

	internal PictureBox pictureBox2;

	[AccessedThroughProperty("alertimage")]
	internal PictureBox alertimage;

	internal Timer closetimer;

	[AccessedThroughProperty("backpanel")]
	internal DrakeUITitlePanel backpanel;

	private Guna2BorderlessForm guna2BorderlessForm1;

	internal DrakeUIButtonIcon lidlamb;

	protected override bool ShowWithoutActivation => true;

	[DllImport("user32.dll", SetLastError = true)]
	private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int x, int y, int cx, int cy, uint uFlags);

	protected override void OnLoad(EventArgs e)
	{
		base.OnLoad(e);
		SetWindowPos(base.Handle, new IntPtr(-1), base.Left, base.Top, base.Width, base.Height, 80u);
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.alertimage = new System.Windows.Forms.PictureBox();
		this.pictureBox2 = new System.Windows.Forms.PictureBox();
		this.msglabel = new System.Windows.Forms.Label();
		this.closetimer = new System.Windows.Forms.Timer(this.components);
		this.backpanel = new DrakeUI.Framework.DrakeUITitlePanel();
		this.lidlamb = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		((System.ComponentModel.ISupportInitialize)this.alertimage).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).BeginInit();
		this.backpanel.SuspendLayout();
		base.SuspendLayout();
		this.alertimage.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.alertimage.Dock = System.Windows.Forms.DockStyle.Left;
		this.alertimage.Location = new System.Drawing.Point(16, 0);
		this.alertimage.Name = "alertimage";
		this.alertimage.Size = new System.Drawing.Size(44, 62);
		this.alertimage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
		this.alertimage.TabIndex = 0;
		this.alertimage.TabStop = false;
		this.pictureBox2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.pictureBox2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.pictureBox2.Dock = System.Windows.Forms.DockStyle.Right;
		this.pictureBox2.Location = new System.Drawing.Point(291, 0);
		this.pictureBox2.Name = "pictureBox2";
		this.pictureBox2.Size = new System.Drawing.Size(20, 62);
		this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox2.TabIndex = 1;
		this.pictureBox2.TabStop = false;
		this.pictureBox2.Click += new System.EventHandler(PictureBox2_Click);
		this.msglabel.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.msglabel.Dock = System.Windows.Forms.DockStyle.Fill;
		this.msglabel.Font = new System.Drawing.Font("Calibri", 11f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.msglabel.ForeColor = System.Drawing.Color.White;
		this.msglabel.Location = new System.Drawing.Point(60, 0);
		this.msglabel.Name = "msglabel";
		this.msglabel.Size = new System.Drawing.Size(231, 62);
		this.msglabel.TabIndex = 2;
		this.msglabel.Text = "this is test msg";
		this.msglabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.closetimer.Interval = 40;
		this.closetimer.Tick += new System.EventHandler(Closetimer_Tick);
		this.backpanel.BackColor = System.Drawing.Color.Transparent;
		this.backpanel.Controls.Add(this.msglabel);
		this.backpanel.Controls.Add(this.pictureBox2);
		this.backpanel.Controls.Add(this.alertimage);
		this.backpanel.Controls.Add(this.lidlamb);
		this.backpanel.Dock = System.Windows.Forms.DockStyle.Fill;
		this.backpanel.FillColor = System.Drawing.Color.Black;
		this.backpanel.Font = new System.Drawing.Font("Calibri", 12f);
		this.backpanel.ForeColor = System.Drawing.Color.White;
		this.backpanel.Location = new System.Drawing.Point(0, 0);
		this.backpanel.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.backpanel.Name = "backpanel";
		this.backpanel.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.backpanel.Size = new System.Drawing.Size(311, 62);
		this.backpanel.Style = DrakeUI.Framework.UIStyle.Custom;
		this.backpanel.TabIndex = 3;
		this.backpanel.Text = null;
		this.backpanel.TitleColor = System.Drawing.Color.Black;
		this.backpanel.TitleHeight = 0;
		this.backpanel.TitleInterval = 0;
		this.lidlamb.Cursor = System.Windows.Forms.Cursors.Hand;
		this.lidlamb.Dock = System.Windows.Forms.DockStyle.Left;
		this.lidlamb.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.lidlamb.Font = new System.Drawing.Font("Calibri", 12f);
		this.lidlamb.ForeColor = System.Drawing.Color.Lime;
		this.lidlamb.Location = new System.Drawing.Point(0, 0);
		this.lidlamb.Margin = new System.Windows.Forms.Padding(2);
		this.lidlamb.Name = "lidlamb";
		this.lidlamb.RectColor = System.Drawing.Color.Transparent;
		this.lidlamb.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.lidlamb.Size = new System.Drawing.Size(16, 62);
		this.lidlamb.Style = DrakeUI.Framework.UIStyle.Custom;
		this.lidlamb.StyleCustomMode = true;
		this.lidlamb.Symbol = 61713;
		this.lidlamb.TabIndex = 3;
		this.lidlamb.MouseDown += new System.Windows.Forms.MouseEventHandler(Lidlamb_MouseDown);
		this.lidlamb.MouseUp += new System.Windows.Forms.MouseEventHandler(Lidlamb_MouseUp);
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.Black;
		base.CausesValidation = false;
		base.ClientSize = new System.Drawing.Size(311, 62);
		base.ControlBox = false;
		base.Controls.Add(this.backpanel);
		this.DoubleBuffered = true;
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "alertform";
		base.Opacity = 0.0;
		base.ShowIcon = false;
		base.ShowInTaskbar = false;
		this.Text = "AlertForm";
		((System.ComponentModel.ISupportInitialize)this.alertimage).EndInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).EndInit();
		this.backpanel.ResumeLayout(false);
		base.ResumeLayout(false);
	}

	[DllImport("user32.dll")]
	private static extern bool SetWindowPos(int hWnd, int hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

	[DllImport("user32.dll")]
	private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

	public static void ShowInactiveTopmost(Form frm)
	{
		ShowWindow(frm.Handle, 4);
		SetWindowPos(frm.Handle.ToInt32(), -1, frm.Left, frm.Top, frm.Width, frm.Height, 16u);
	}

	[AsyncStateMachine(typeof(VB_0024StateMachine_39_FadeIn))]
	[DebuggerStepThrough]
	private void FadeIn(Form o, int interval = 45)
	{
		VB_0024StateMachine_39_FadeIn stateMachine = new VB_0024StateMachine_39_FadeIn();
		stateMachine._0024VB_0024Me = this;
		stateMachine._0024VB_0024Local_o = o;
		stateMachine._0024VB_0024Local_interval = interval;
		stateMachine._0024State = -1;
		stateMachine._0024Builder = AsyncVoidMethodBuilder.Create();
		stateMachine._0024Builder.Start(ref stateMachine);
	}

	public alertform(string msg)
	{
		base.Load += AlertForm_Load;
		components = null;
		counter = 1;
		hold = 50;
		InitializeComponent();
		TheMessage = msg;
	}

	private void AlertForm_Load(object sender, EventArgs e)
	{
		base.Opacity = 0.0;
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "AR", TextCompare: false) == 0)
			{
				TheMessage = Codes.Translate(TheMessage, "en", "ar");
			}
		}
		else
		{
			TheMessage = Codes.Translate(TheMessage, "en", "zh");
		}
		msglabel.Text = TheMessage;
		workingArea = Screen.GetWorkingArea(this);
		checked
		{
			oldY = workingArea.Bottom - base.Size.Height - 10;
			int num = 1;
			do
			{
				try
				{
					string name = "Craxs_Alert_" + Conversions.ToString(num);
					alertform alertform2 = (alertform)Application.OpenForms[name];
					if (alertform2 == null)
					{
						base.Name = name;
						break;
					}
					oldY = workingArea.Bottom - base.Size.Height - 10 - base.Height * num;
				}
				catch (Exception)
				{
				}
				num++;
			}
			while (num <= 99);
			base.Location = new Point(workingArea.Right - base.Size.Width, oldY);
			ShowInactiveTopmost(this);
			FadeIn(this);
		}
	}

	private void Closetimer_Tick(object sender, EventArgs e)
	{
		checked
		{
			try
			{
				if (DateTime.Compare(Showtime, DateTime.Now) < 0)
				{
					base.Opacity = 0.0;
					closetimer.Stop();
					closetimer.Dispose();
					Close();
				}
				else if (hold == 0)
				{
					base.Name = "ended";
					workingArea = Screen.GetWorkingArea(this);
					base.Location = new Point(workingArea.Right - base.Size.Width, oldY - counter);
					counter++;
					base.Opacity -= 0.05;
				}
				else
				{
					hold--;
				}
			}
			catch (Exception)
			{
			}
		}
	}

	private void PictureBox2_Click(object sender, EventArgs e)
	{
		closetimer.Stop();
		closetimer.Dispose();
		Close();
	}

	private void Lidlamb_MouseDown(object sender, MouseEventArgs e)
	{
		closetimer.Stop();
		closetimer.Dispose();
		Close();
	}

	private void Lidlamb_MouseUp(object sender, MouseEventArgs e)
	{
		closetimer.Start();
	}
}
