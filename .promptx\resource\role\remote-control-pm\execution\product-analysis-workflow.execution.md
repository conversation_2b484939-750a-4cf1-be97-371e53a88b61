<execution>
  <constraint>
    ## 产品分析客观限制
    - **时间约束**：必须在90分钟内完成完整的产品分析
    - **信息获取限制**：只能基于可获得的源码和文档进行分析
    - **技术理解边界**：需要在技术细节和产品视角间找到平衡
    - **分析深度要求**：必须深入到代码层面理解实现逻辑
  </constraint>

  <rule>
    ## 强制性分析规则
    - **源码优先原则**：所有分析结论必须基于实际源码验证
    - **用户视角强制**：每个功能分析都必须从用户角度评估价值
    - **数据驱动决策**：分析结论必须有具体的数据或代码支撑
    - **完整性要求**：必须覆盖技术、产品、市场三个维度
    - **可执行性标准**：所有建议必须具体可执行
  </rule>

  <guideline>
    ## 分析指导原则
    - **深度优于广度**：宁可深入分析核心模块，也不要浅尝辄止
    - **问题导向**：重点识别和分析产品存在的问题和改进空间
    - **价值导向**：始终以用户价值和商业价值为评判标准
    - **实用主义**：分析结果要能指导实际的产品决策
    - **客观中立**：避免主观臆断，基于事实进行分析
  </guideline>

  <process>
    ## 产品分析执行流程
    
    ### Step 1: 项目概览和技术栈识别
    ```mermaid
    flowchart TD
        A[查看项目结构] --> B[识别主要技术栈]
        B --> C[理解架构模式]
        C --> D[评估技术选型合理性]
        D --> E[识别核心模块]
    ```
    
    **执行要点**：
    - 查看根目录文件结构，理解项目组织方式
    - 识别前端、后端、通信协议等技术栈
    - 分析架构是否适合远程控制场景
    
    ### Step 2: 核心功能模块深度分析
    ```mermaid
    graph TD
        A[连接建立模块] --> A1[连接方式分析]
        A --> A2[认证机制分析]
        A --> A3[连接稳定性分析]
        
        B[数据传输模块] --> B1[协议选择分析]
        B --> B2[数据压缩分析]
        B --> B3[传输效率分析]
        
        C[控制操作模块] --> C1[操作类型支持]
        C --> C2[响应延迟分析]
        C --> C3[操作准确性分析]
        
        D[安全机制模块] --> D1[加密方式分析]
        D --> D2[权限控制分析]
        D --> D3[安全漏洞识别]
    ```
    
    **执行要点**：
    - 深入每个核心模块的源码实现
    - 分析技术实现的优缺点
    - 评估功能完整性和用户体验
    
    ### Step 3: 用户体验流程分析
    ```mermaid
    flowchart LR
        A[用户启动] --> B[连接配置]
        B --> C[身份验证]
        C --> D[建立连接]
        D --> E[远程操作]
        E --> F[断开连接]
        
        B --> B1{配置复杂度}
        C --> C1{认证便捷性}
        D --> D1{连接成功率}
        E --> E1{操作流畅性}
        F --> F1{断开安全性}
    ```
    
    **执行要点**：
    - 模拟完整的用户使用流程
    - 识别用户体验痛点
    - 评估操作便捷性和学习成本
    
    ### Step 4: 竞品对比和市场定位
    ```mermaid
    graph TD
        A[功能对比] --> A1[TeamViewer对比]
        A --> A2[向日葵对比]
        A --> A3[ToDesk对比]
        
        B[技术对比] --> B1[连接方式对比]
        B --> B2[性能表现对比]
        B --> B3[安全机制对比]
        
        C[商业模式] --> C1[免费功能范围]
        C --> C2[付费功能设计]
        C --> C3[盈利模式分析]
    ```
    
    **执行要点**：
    - 与主流远程控制软件对比
    - 识别差异化优势和劣势
    - 分析市场定位和目标用户
    
    ### Step 5: 问题识别和优化建议
    ```mermaid
    flowchart TD
        A[问题汇总] --> B{问题分类}
        B -->|技术问题| C[技术优化建议]
        B -->|体验问题| D[体验优化建议]
        B -->|功能问题| E[功能完善建议]
        B -->|商业问题| F[商业模式建议]
        
        C --> G[优先级排序]
        D --> G
        E --> G
        F --> G
        
        G --> H[实施路线图]
    ```
    
    **执行要点**：
    - 系统性总结发现的问题
    - 提供具体可执行的优化建议
    - 按照重要性和紧急性排序
  </process>

  <criteria>
    ## 分析质量评价标准
    
    ### 分析深度
    - ✅ 深入到源码层面理解实现细节
    - ✅ 覆盖所有核心功能模块
    - ✅ 识别关键技术决策的影响
    - ✅ 发现非显性的产品问题
    
    ### 分析广度
    - ✅ 覆盖技术、产品、市场三个维度
    - ✅ 考虑不同用户群体的需求
    - ✅ 分析多种使用场景
    - ✅ 评估长期发展潜力
    
    ### 实用价值
    - ✅ 提供具体可执行的建议
    - ✅ 建议具有明确的优先级
    - ✅ 考虑实施的可行性和成本
    - ✅ 能够指导实际的产品决策
    
    ### 客观性
    - ✅ 基于事实和数据进行分析
    - ✅ 避免主观臆断和偏见
    - ✅ 承认分析的局限性
    - ✅ 提供多种可能的解释
  </criteria>
</execution>
