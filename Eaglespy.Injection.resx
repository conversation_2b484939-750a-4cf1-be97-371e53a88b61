﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
<resheader name="resmimetype"><value>text/microsoft-resx</value></resheader><resheader name="version"><value>1.3</value></resheader><resheader name="reader"><value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><resheader name="writer"><value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><data name="$this.Icon" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABNTeXN0ZW0uRHJhd2luZy5JY29uAgAAAAhJY29uRGF0YQhJY29uU2l6ZQcEAhNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAIAAAAJAwAAAAX8////E1N5c3RlbS5EcmF3aW5nLlNpemUCAAAABXdpZHRoBmhlaWdodAAACAgCAAAAAAAAAAAAAAAPAwAAAL4QAAACAAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAMMOAADDDgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALKpyQCyqckVsqnJM7KpyQKyqckAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAsqnJALKpyTCyqcnKsqnJWbKpyQGyqckAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACyqckAsqnJALKpyVSyqcnRsqnJWbKpyQGyqckAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACyqckAsqnJALKpyVSyqcnRsqnJWbKpyQGyqckAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACyqckAsqnJALKpyVSyqcnRsqnJWbKpyQGyqckAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACyqckAsqrKALKpyVSyqcnRsajIWbqy0QGonr4AWz/mAF1A5gNXOeYXVjjmDU8x5gBbPuYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACyqckAppm+ALGoyFS7s9LWz8jnm9PO6yNBH+UGVznmdFQ35thUN+a6WDvmNE8w5gBkSecAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADQyekAz8noKs/J6OHRzOr/z8jqvWhN541UNub1VDbm/1Q25v9VN+bSWDvmN1Ay5gBiR+cAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANHL6gDRy+oj0czqzdDJ6v+ZiOj/WTzm/lQ25v9UNub/VDbm/1Q25v9VN+bUWDvmOkwt5gBlSuYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/PztALGl6QDKw+o2k4Ho41o95v9TNeb/VDbm/1Q25v9UNub/VDbm/1Q25v9VN+bWWDvmPE0t5QBiSOcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABcP+YAYUbmA1U35m5UNub0VDbm/1Q25v9UNub/VDbm/1Q25v9UNub/VDbm/1Q25v9VN+bYWDvmPksr5QBhRucAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFY55gBXOeY3VDfm61Q25v9UNuX/Uzbk/1Q25v9UNub/VDbm/1Q25v9UNub/VDbm/1Q25v9UNubabFPnQAAA4ADRy+oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVDfmAFU35kZUNub3Uzbk/04y1P9PM9f/VDbm/1Q25v9UNub/VDbm/1Q25v9UNub/UjTm/25U5/+5runb1dDqQdHL6gDRy+oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWOeYAVznnDlU45aFOMtX+TDHP/1I14v9UNub/VDbm/1Q25v9UNub/VDbm/1I05v9sU+f/uq/p/9LN6v/Ry+rc0cvqRNHL6gDRy+oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHhh6ABWOuAAWTzhFFE116JTNeL9VDbn/1M14v9PM9f/UzXk/1Q25v9SNOb/a1Hn/7mu6f/Tzer/0cvq/9HL6v/Ry+re0cvqRtHL6gDRy+oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALiZ/wBZO+cAXD/pE1Y4559TNeL9TDHQ/04y1P9UNuX/UzTm/2pQ5/+4ren/083q/9HL6v/Ry+r/0cvq/9HL6v/Ry+rg0cvqSdHL6gDRy+oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANPU6ABYO+YAXT/rElE12JxOMtX9Uzbk/1M05v9pT+f/tqvp/9PN6v/Ry+r/0cvq/9HL6v/Ry+r/0cvq/9HL6v/Ry+rh0szqTAYA4QDl4esAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABXO+AAWT3hEFY45ZlTNeb8aE7n/7Wq6f/Tzur/0cvq/9HL6v/Ry+r/0cvq/9HL6v/Ry+r/0cvq/9LM6v/EvOrkc1vnaCQA5ARUNuYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABaPeYAUDHmD2lP55Wzp+n71M/q/7+26f+PfOj/xb3q/9LM6v/Ry+r/0cvq/9HL6v/SzOr/yMDq/4Js5/xWOOaHSirmA1Q25gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPyeoAxLzpDdPO6pG/tun7dV7n/4l16P/Mxer/083q/9LM6v/Ry+r/0szq/8nB6v+Cbej8UjTmjk0t5gxRMuYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADRy+oA7e3rDJeG6I6Qfej6zMXq/87H6v+hkun/v7Xp/9PN6v/Jwur/hXHo/4Vx6PTAtulZQiHlAO/u6wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAt+oAwrnqC9DK6ovNxur5kH7o/3FZ5//At+n/y8Tq/4dy6P+Hc+j/ysPq/9LM6uXRy+pS0cvqANHL6gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADRy+oA7e3rCqmc6Yd8Zuf5u7Hp/8zF6v+Jduj/hnHo/8nC6v/SzOr/0cvq/9HL6ufRy+pV0cvqAdHL6gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC0qOkAqZvpCsa+6oTMxer4inbo/GtR58HIwOrn0szq/9HL6v/Ry+r/0cvq/9HL6unRy+pZ29bqAdXP6gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC2q+kAxr7qDXtl57lYO+amVzrmFNTO6lbRy+ro0cvq/9HL6v/Ry+r/0cvq/9DK6umJdeiHSirmJFQ25gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEkp5gBIKOYCTzDmO1Ez5hdiR+cA0cvqAdHL6lPRy+rm0cvq/9HL6v/Ry+r/opPo/2BE5slQMeYjVDbmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADRy+oA0cvqANHL6lDRy+rk0czq/6SV6f9fQ+bLTi/mLlAx5gBUNuYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADRy+oAbVHnANHK6k+hkujsYUXmz04v5jFQMeYAVDbmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABlSuYAZkvmDFg65oVRM+Y3TzHmAFQ25gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACcM5AA0FuUASivmBFU25gBUNuYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/////4////+H////w////+H////w////+GP///wB///8AP///AB///4AP//8AB///AAP//wAB//8AAP//gAB//8AAP//gAB//8AAH//gAB//8AA///gAf//8AD///gAP//8AB///gAP//4gD///+B////w////8f////v/////8L</value></data></root>